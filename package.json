{"name": "farmafacil", "version": "1.0.0", "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "build": "npm run build:frontend && npm run build:backend && npm run copy:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "copy:frontend": "cp -r frontend/dist/* backend/dist/public/", "start": "cd backend && npm start", "install:all": "npm install && npm install -w frontend && npm install -w backend"}, "devDependencies": {"concurrently": "^8.2.2"}}
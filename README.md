# FarmaFácil

Sistema de gestión para farmacias con frontend en React y backend en Node.js.

## Requisitos

- Node.js >= 18.0.0
- npm >= 8.0.0
- MongoDB >= 5.0

## Instalación

1. Clonar el repositorio

```bash
git clone https://github.com/tu-usuario/farmafacil.git
cd farmafacil
```

2. Instalar dependencias

```bash
npm run install:all
```

3. Configurar variables de entorno

Backend (`backend/.env`):

```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/farmafacil
JWT_SECRET=your_jwt_secret_must_be_at_least_32_characters_long
NODE_ENV=development
CORS_ORIGIN=*
JWT_EXPIRES_IN=24h
MAX_LOGO_SIZE_MB=2
MAX_IMAGE_SIZE_MB=5
UNCATEGORIZED_CATEGORY_ID=65c3944f00c9d8232dc73000
NO_LABORATORY_ID=65c3944f00c9d8232dc74000
```

Frontend (`frontend/.env`):

```env
VITE_PORT=5173
VITE_API_PREFIX=/api
VITE_API_URL=http://localhost:3000
VITE_MAX_LOGO_SIZE_MB=2
```

4. Iniciar en desarrollo

```bash
npm run dev
```

## URLs por defecto

- Frontend: `http://localhost:8080`
- Backend API: `http://localhost:3000/api`
- API Docs: `http://localhost:3000/api-docs`

## Scripts principales

```bash
npm run dev          # Inicia frontend y backend en desarrollo
npm run seed        # Crea datos iniciales
npm run reset-db    # Reinicia la base de datos y crea datos iniciales
npm test           # Ejecuta tests
```

## Estructura del Proyecto

### Frontend (React + Vite + ShadcnUI)

```
frontend/
├── src/
│   ├── assets/         # Imágenes, fuentes y otros recursos estáticos
│   ├── components/     # Componentes reutilizables
│   │   ├── layouts/   # Layouts reutilizables
│   │   └── ui/        # Componentes UI (shadcn)
│   ├── features/      # Características organizadas por módulo
│   │   ├── auth/      # Autenticación
│   │   ├── sales/     # Módulo de ventas
│   │   ├── inventory/ # Módulo de inventario
│   │   └── reports/   # Módulo de reportes
│   ├── hooks/         # Custom hooks globales
│   ├── lib/           # Utilidades y configuraciones
│   ├── pages/         # Páginas de la aplicación
│   │   ├── auth/      # Páginas de autenticación
│   │   ├── error/     # Páginas de error
│   │   ├── products/  # Páginas de productos
│   │   ├── reports/   # Páginas de reportes
│   │   ├── sales/     # Páginas de ventas
│   │   ├── settings/  # Páginas de configuración
│   │   └── users/     # Páginas de usuarios
│   ├── store/         # Estado global (Zustand)
│   └── types/         # Definiciones de tipos TypeScript
└── tests/             # Tests unitarios y de integración

backend/
├── src/
│   ├── config/        # Configuraciones
│   ├── controllers/   # Controladores
│   ├── interfaces/    # Interfaces TypeScript
│   ├── models/        # Modelos MongoDB
│   ├── routes/        # Rutas API
│   ├── middleware/    # Middlewares
│   ├── utils/         # Utilidades
│   └── seeders/       # Scripts de datos iniciales
└── tests/             # Tests unitarios y de integración
```

### Convenciones de Nombres

- **Archivos de Tipos**: `types/index.ts` para centralizar tipos
- **Componentes**: PascalCase (ej: `UserCreateDialog.tsx`)
- **Hooks**: camelCase con prefix 'use' (ej: `useUsers.ts`)
- **Stores**: camelCase con suffix '.store' (ej: `auth.store.ts`)
- **Utils**: camelCase (ej: `formatDate.ts`)
- **Interfaces**: Un archivo por dominio (ej: `auth.interface.ts`)

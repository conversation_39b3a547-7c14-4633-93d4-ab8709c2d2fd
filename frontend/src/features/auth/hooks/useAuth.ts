import { useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import {
  useAuthStore,
  AuthService,
  type LoginCredentials,
} from '@/features/auth';
import { userService } from '@/features/users';

export function useAuth() {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    setAuth,
    logout: logoutStore,
    checkPermission,
  } = useAuthStore();

  const navigate = useNavigate();
  const verifyingRef = useRef(false);

  const logout = useCallback(() => {
    logoutStore();
    navigate('/auth/login');
  }, [logoutStore, navigate]);

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      try {
        const response = await AuthService.login(credentials);
        setAuth(response.user, response.token);
        navigate('/dashboard');
        return response;
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.message ||
          error.response?.data?.error ||
          'Error al iniciar sesión';
        throw new Error(errorMessage);
      }
    },
  });

  const verifySession = useCallback(async () => {
    if (verifyingRef.current || !isAuthenticated) return false;

    try {
      verifyingRef.current = true;
      const userData = await userService.getCurrentUser();

      // Si el usuario está inactivo, hacer logout
      if (!userData.isActive) {
        logout();
        return false;
      }

      setAuth(userData, token);
      return true;
    } catch (error: any) {
      if (error.response?.status === 401) {
        logout();
      }
      return false;
    } finally {
      verifyingRef.current = false;
    }
  }, [isAuthenticated, token, setAuth, logout]);

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    login: loginMutation.mutate,
    logout,
    verifySession,
    checkPermission,
    isLoggingIn: loginMutation.isPending,
    loginError: loginMutation.error,
  };
}

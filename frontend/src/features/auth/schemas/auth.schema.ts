import { z } from 'zod';
import { USER_VALIDATION_CONSTANTS } from '@/features/users';

export const loginSchema = z.object({
  username: z
    .string()
    .min(USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH, {
      message: `El nombre de usuario debe tener al menos ${USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH} caracteres`,
    })
    .max(USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH, {
      message: `El nombre de usuario no puede tener más de ${USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH} caracteres`,
    }),
  password: z
    .string()
    .min(USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH, {
      message: `La contraseña debe tener al menos ${USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH} caracteres`,
    })
    .max(USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH, {
      message: `La contraseña no puede tener más de ${USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH} caracteres`,
    }),
});

export type LoginSchema = z.infer<typeof loginSchema>;

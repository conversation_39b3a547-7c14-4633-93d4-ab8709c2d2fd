import { PERMISSIONS, USER_ROLES } from '@/features/auth';

type PermissionValues = typeof PERMISSIONS;

export type Permission =
  | PermissionValues['SALES'][keyof PermissionValues['SALES']]
  | PermissionValues['INVENTORY']['PRODUCTS'][keyof PermissionValues['INVENTORY']['PRODUCTS']]
  | PermissionValues['INVENTORY']['STOCK'][keyof PermissionValues['INVENTORY']['STOCK']]
  | PermissionValues['INVENTORY']['STOCK']['MOVEMENTS'][keyof PermissionValues['INVENTORY']['STOCK']['MOVEMENTS']]
  | PermissionValues['INVENTORY']['STOCK']['ENTRIES'][keyof PermissionValues['INVENTORY']['STOCK']['ENTRIES']]
  | PermissionValues['INVENTORY']['STOCK']['OUTPUTS'][keyof PermissionValues['INVENTORY']['STOCK']['OUTPUTS']]
  | PermissionValues['CATEGORIES'][keyof PermissionValues['CATEGORIES']]
  | PermissionValues['LABORATORIES'][keyof PermissionValues['LABORATORIES']]
  | PermissionValues['CUSTOMERS'][keyof PermissionValues['CUSTOMERS']]
  | PermissionValues['USERS'][keyof PermissionValues['USERS']]
  | PermissionValues['REPORTS'][keyof PermissionValues['REPORTS']]
  | PermissionValues['SETTINGS'][keyof PermissionValues['SETTINGS']]
  | '*';

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

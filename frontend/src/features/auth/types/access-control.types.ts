import { ReactNode } from 'react';
import { Permission } from '@/features/auth';
import { USER_ROLES } from '@/features/auth/constants/permissions.constants';

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES];

export interface RoleBasedRouteProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
  requiredPermissions?: Permission[];
}

export interface ProtectedRouteProps {
  children: ReactNode;
}

import { api } from '@/lib/api/api';
import type { LoginCredentials, LoginResponse } from '@/features/auth';
import type { User } from '@/features/users';

export const AuthService = {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  async verifySession(): Promise<User> {
    const response = await api.get('/users/profile');
    return response.data;
  },
};

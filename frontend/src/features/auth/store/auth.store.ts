import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { jwtDecode } from 'jwt-decode';
import type { AuthState } from '@/features/auth';
import type { Permission } from '@/features/auth';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null,
      user: null,
      isAuthenticated: false,
      isLoading: true,

      setAuth: (user, token) =>
        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        }),

      logout: () =>
        set({
          token: null,
          user: null,
          isAuthenticated: false,
          isLoading: false,
        }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      initializeAuth: () => {
        const { token, checkAuthStatus } = get();
        const isValid = token && checkAuthStatus();

        if (!isValid) {
          get().logout();
        }

        set({
          isLoading: false,
          isAuthenticated: isValid,
        });
      },

      checkAuthStatus: () => {
        const { token } = get();
        if (!token) return false;

        try {
          const decoded = jwtDecode(token);
          const currentTime = Date.now() / 1000;
          return decoded && typeof decoded === 'object' && 'exp' in decoded
            ? decoded.exp > currentTime
            : false;
        } catch {
          return false;
        }
      },

      checkPermission: (permission: Permission) => {
        const { user } = get();
        if (!user?.permissions) return false;
        return (
          user.permissions.includes('*') ||
          user.permissions.includes(permission)
        );
      },
    }),
    {
      name: 'auth-storage',
    }
  )
);

import { useTheme } from 'next-themes';
import { Sun, Moon, Laptop } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useSettingsStore } from '@/features/settings';
import { getImageUrl } from '@/lib/utils/image';

type ThemeOption = 'light' | 'dark' | 'system';

const themeIcons = {
  light: <Sun className='h-5 w-5' />,
  dark: <Moon className='h-5 w-5' />,
  system: <Laptop className='h-5 w-5' />,
} as const;

const themeLabels = {
  light: 'Claro',
  dark: 'Oscuro',
  system: 'Sistema',
} as const;

export function AuthNavbar() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const publicSettings = useSettingsStore((state) => state.publicSettings);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <nav className='h-16 bg-background border-b transition-colors duration-200 fixed left-0 right-0 top-0 z-50'>
      <div className='h-full w-full flex items-center justify-between px-4'>
        <div className='flex items-center gap-2'>
          {publicSettings?.logo && (
            <img
              src={getImageUrl(publicSettings.logo)}
              alt='Logo'
              className='h-8 w-auto object-contain'
              onError={(e) => {
                e.currentTarget.src = '/placeholder-logo.png';
                e.currentTarget.onerror = null;
              }}
            />
          )}
          <span className='self-center text-2xl font-semibold whitespace-nowrap text-foreground'>
            {publicSettings?.systemName || ''}
          </span>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              className='hover:bg-accent hover:text-accent-foreground rounded-lg'
            >
              {themeIcons[theme as ThemeOption]}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            {(Object.entries(themeIcons) as [ThemeOption, JSX.Element][]).map(
              ([themeKey, icon]) => (
                <DropdownMenuItem
                  key={themeKey}
                  onClick={() => setTheme(themeKey)}
                  className='cursor-pointer'
                >
                  {icon && (
                    <span className='w-4 h-4 mr-2 inline-flex items-center'>
                      {icon}
                    </span>
                  )}
                  {themeLabels[themeKey]}
                </DropdownMenuItem>
              )
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </nav>
  );
}

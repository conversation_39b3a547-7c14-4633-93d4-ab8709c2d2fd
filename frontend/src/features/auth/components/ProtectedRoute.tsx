import { Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuth } from '@/features/auth';
import type { ProtectedRouteProps } from '@/features/auth';

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, user, verifySession } = useAuth();

  // Verify the session when the component mounts
  useEffect(() => {
    if (isAuthenticated) {
      verifySession();
    }
  }, [isAuthenticated, verifySession]);

  if (!isAuthenticated || !user?.isActive) {
    return <Navigate to='/auth/login' replace />;
  }

  return <>{children}</>;
}

import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import type { RoleBasedRouteProps } from '@/features/auth';

export function RoleBasedRoute({
  children,
  allowedRoles = [],
  requiredPermissions = [],
}: RoleBasedRouteProps) {
  const { user } = useAuthStore();

  if (!user) {
    return <Navigate to='/login' replace />;
  }

  // If the user is admin, allow access to everything
  if (user.role === 'admin') {
    return <>{children}</>;
  }

  // Verify role if allowed roles are specified
  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    return <Navigate to='/unauthorized' replace />;
  }

  // Verify permissions if required permissions are specified
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every((permission) =>
      user.permissions.includes(permission)
    );

    if (!hasAllPermissions) {
      return <Navigate to='/unauthorized' replace />;
    }
  }

  return <>{children}</>;
}

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { User, Lock, Eye, EyeOff } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils/styles';
import { useAuthStore, loginSchema, useAuth } from '@/features/auth';

type LoginFormValues = z.infer<typeof loginSchema>;

export const LoginForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { logout } = useAuthStore();
  const { login, isLoggingIn, loginError } = useAuth();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  useEffect(() => {
    logout();
  }, [logout]);

  useEffect(() => {
    if (loginError) {
      form.setError('root', {
        message: loginError.message,
      });
    }
  }, [loginError, form]);

  const toggleShowPassword = () => setShowPassword(!showPassword);

  const handleLogin = (values: LoginFormValues) => {
    if (!values.username || !values.password) return;

    login({
      username: values.username,
      password: values.password,
    });
  };

  return (
    <Card className='w-full max-w-md'>
      <CardHeader>
        <CardTitle>Iniciar Sesión</CardTitle>
        <CardDescription>
          Ingresa tus credenciales para acceder al sistema
        </CardDescription>
      </CardHeader>
      <CardContent>
        {form.formState.errors.root && (
          <div className='text-destructive text-sm text-center mb-4 p-2'>
            {form.formState.errors.root.message}
          </div>
        )}

        <Form {...form}>
          <div className='space-y-4'>
            <FormField
              control={form.control}
              name='username'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.username && 'text-destructive'
                    )}
                  >
                    <User className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      Nombre de Usuario
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ingresa tu nombre de usuario'
                      {...field}
                      className={cn(
                        form.formState.errors.username && 'border-red-500'
                      )}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const passwordField = form.getValues('password');
                          if (passwordField) {
                            form.handleSubmit(handleLogin)();
                          }
                        }
                      }}
                      disabled={isLoggingIn}
                    />
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.password && 'text-destructive'
                    )}
                  >
                    <Lock className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      Contraseña
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <div className='relative'>
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Ingresa tu contraseña'
                        {...field}
                        className={cn(
                          form.formState.errors.password && 'border-red-500'
                        )}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            form.handleSubmit(handleLogin)();
                          }
                        }}
                        disabled={isLoggingIn}
                      />
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        className='absolute right-2 top-1/2 -translate-y-1/2'
                        onClick={(e) => {
                          e.preventDefault();
                          toggleShowPassword();
                        }}
                        disabled={isLoggingIn}
                      >
                        {showPassword ? (
                          <EyeOff className='h-5 w-5 text-gray-500' />
                        ) : (
                          <Eye className='h-5 w-5 text-gray-500' />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <Button
              type='button'
              className='w-full'
              onClick={(e) => {
                e.preventDefault();
                form.handleSubmit(handleLogin)();
              }}
              disabled={isLoggingIn}
            >
              {isLoggingIn ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            </Button>
          </div>
        </Form>
      </CardContent>
      <CardFooter className='flex flex-col items-center'>
        <div className='text-sm text-gray-600 dark:text-gray-400'>
          <span>
            Desarrollado por el equipo de{' '}
            <a
              href='https://cloudvision.com'
              className='text-blue-500 hover:underline'
            >
              CloudVision
            </a>{' '}
            con IA.
          </span>
        </div>
      </CardFooter>
    </Card>
  );
};

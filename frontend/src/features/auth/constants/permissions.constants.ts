export const PERMISSIONS = {
  // Módulos principales
  INVENTORY: {
    PRODUCTS: {
      LIST: 'inventory:products:list',
      CREATE: 'inventory:products:create',
      EDIT: 'inventory:products:edit',
      DELETE: 'inventory:products:delete',
    },
    STOCK: {
      MOVEMENTS: {
        LIST: 'inventory:stock:movements:list',
        CREATE: 'inventory:stock:movements:create',
        EDIT: 'inventory:stock:movements:edit',
        DELETE: 'inventory:stock:movements:delete',
      },
      ENTRIES: {
        LIST: 'inventory:stock:entries:list',
        CREATE: 'inventory:stock:entries:create',
        EDIT: 'inventory:stock:entries:edit',
        DELETE: 'inventory:stock:entries:delete',
      },
      OUTPUTS: {
        LIST: 'inventory:stock:outputs:list',
        CREATE: 'inventory:stock:outputs:create',
        EDIT: 'inventory:stock:outputs:edit',
        DELETE: 'inventory:stock:outputs:delete',
      },
    },
  },
  CUSTOMERS: {
    LIST: 'customers:list',
    CREATE: 'customers:create',
    EDIT: 'customers:edit',
    DELETE: 'customers:delete',
  },
  SALES: {
    LIST: 'sales:list',
    CREATE: 'sales:create',
    EDIT: 'sales:edit',
    DELETE: 'sales:delete',
    CANCEL: 'sales:cancel',
  },
  // Entidades base
  CATEGORIES: {
    LIST: 'categories:list',
    CREATE: 'categories:create',
    EDIT: 'categories:edit',
    DELETE: 'categories:delete',
  },
  LABORATORIES: {
    LIST: 'laboratories:list',
    CREATE: 'laboratories:create',
    EDIT: 'laboratories:edit',
    DELETE: 'laboratories:delete',
  },
  // Módulos de sistema
  USERS: {
    LIST: 'users:list',
    CREATE: 'users:create',
    EDIT: 'users:edit',
    DELETE: 'users:delete',
    CHANGE_PASSWORD: 'users:change-password',
  },
  REPORTS: {
    SALES: {
      LIST: 'reports:sales:list',
    },
    INVENTORY: {
      LIST: 'reports:inventory:list',
    },
  },
  SETTINGS: {
    LIST: 'settings:list',
    EDIT: 'settings:edit',
  },
  CASH: {
    LIST: 'cash:list',
    MANAGE: 'cash:manage',
  },
} as const;

export const USER_ROLES = {
  ADMIN: 'admin',
  CASHIER: 'cashier',
} as const;

export const PERMISSION_DISPLAY_NAMES = {
  // Inventario - Productos
  [PERMISSIONS.INVENTORY.PRODUCTS.LIST]: 'Listar Productos',
  [PERMISSIONS.INVENTORY.PRODUCTS.CREATE]: 'Crear Productos',
  [PERMISSIONS.INVENTORY.PRODUCTS.EDIT]: 'Editar Productos',
  [PERMISSIONS.INVENTORY.PRODUCTS.DELETE]: 'Eliminar Productos',

  // Inventario - Stock
  [PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST]: 'Ver Movimientos de Stock',
  [PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.CREATE]: 'Crear Movimiento de Stock',
  [PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.EDIT]: 'Editar Movimiento de Stock',
  [PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.DELETE]:
    'Eliminar Movimiento de Stock',
  [PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST]: 'Ver Entradas de Stock',
  [PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE]: 'Registrar Entrada de Stock',
  [PERMISSIONS.INVENTORY.STOCK.ENTRIES.EDIT]: 'Editar Entrada de Stock',
  [PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST]: 'Ver Salidas de Stock',
  [PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE]: 'Registrar Salida de Stock',
  [PERMISSIONS.INVENTORY.STOCK.OUTPUTS.EDIT]: 'Editar Salida de Stock',

  // Clientes
  [PERMISSIONS.CUSTOMERS.LIST]: 'Listar Clientes',
  [PERMISSIONS.CUSTOMERS.CREATE]: 'Crear Cliente',
  [PERMISSIONS.CUSTOMERS.EDIT]: 'Editar Cliente',
  [PERMISSIONS.CUSTOMERS.DELETE]: 'Eliminar Cliente',

  // Ventas
  [PERMISSIONS.SALES.LIST]: 'Listar Ventas',
  [PERMISSIONS.SALES.CREATE]: 'Crear Venta',
  [PERMISSIONS.SALES.EDIT]: 'Editar Venta',
  [PERMISSIONS.SALES.DELETE]: 'Eliminar Venta',
  [PERMISSIONS.SALES.CANCEL]: 'Anular Venta',

  // Categorías
  [PERMISSIONS.CATEGORIES.LIST]: 'Listar Categorías',
  [PERMISSIONS.CATEGORIES.CREATE]: 'Crear Categoría',
  [PERMISSIONS.CATEGORIES.EDIT]: 'Editar Categoría',
  [PERMISSIONS.CATEGORIES.DELETE]: 'Eliminar Categoría',

  // Laboratorios
  [PERMISSIONS.LABORATORIES.LIST]: 'Listar Laboratorios',
  [PERMISSIONS.LABORATORIES.CREATE]: 'Crear Laboratorio',
  [PERMISSIONS.LABORATORIES.EDIT]: 'Editar Laboratorio',
  [PERMISSIONS.LABORATORIES.DELETE]: 'Eliminar Laboratorio',

  // Usuarios
  [PERMISSIONS.USERS.LIST]: 'Ver Usuarios',
  [PERMISSIONS.USERS.CREATE]: 'Crear Usuario',
  [PERMISSIONS.USERS.EDIT]: 'Editar Usuario',
  [PERMISSIONS.USERS.DELETE]: 'Eliminar Usuario',
  [PERMISSIONS.USERS.CHANGE_PASSWORD]: 'Cambiar Contraseña',

  // Reportes
  [PERMISSIONS.REPORTS.SALES.LIST]: 'Ver Reportes de Ventas',
  [PERMISSIONS.REPORTS.INVENTORY.LIST]: 'Ver Reportes de Inventario',

  // Configuración
  [PERMISSIONS.SETTINGS.LIST]: 'Ver Configuración',
  [PERMISSIONS.SETTINGS.EDIT]: 'Editar Configuración',

  // Caja
  [PERMISSIONS.CASH.LIST]: 'Ver Caja',
  [PERMISSIONS.CASH.MANAGE]: 'Gestionar Caja',

  '*': 'Todos los Permisos',
} as const;

export const ROLE_DISPLAY_NAMES = {
  [USER_ROLES.ADMIN]: 'Administrador',
  [USER_ROLES.CASHIER]: 'Cajero',
} as const;

export type Permission =
  | (typeof PERMISSIONS.INVENTORY.PRODUCTS)[keyof typeof PERMISSIONS.INVENTORY.PRODUCTS]
  | (typeof PERMISSIONS.INVENTORY.STOCK.MOVEMENTS)[keyof typeof PERMISSIONS.INVENTORY.STOCK.MOVEMENTS]
  | (typeof PERMISSIONS.INVENTORY.STOCK.ENTRIES)[keyof typeof PERMISSIONS.INVENTORY.STOCK.ENTRIES]
  | (typeof PERMISSIONS.INVENTORY.STOCK.OUTPUTS)[keyof typeof PERMISSIONS.INVENTORY.STOCK.OUTPUTS]
  | (typeof PERMISSIONS.CUSTOMERS)[keyof typeof PERMISSIONS.CUSTOMERS]
  | (typeof PERMISSIONS.SALES)[keyof typeof PERMISSIONS.SALES]
  | (typeof PERMISSIONS.CATEGORIES)[keyof typeof PERMISSIONS.CATEGORIES]
  | (typeof PERMISSIONS.LABORATORIES)[keyof typeof PERMISSIONS.LABORATORIES]
  | (typeof PERMISSIONS.USERS)[keyof typeof PERMISSIONS.USERS]
  | (typeof PERMISSIONS.REPORTS)[keyof typeof PERMISSIONS.REPORTS]
  | (typeof PERMISSIONS.SETTINGS)[keyof typeof PERMISSIONS.SETTINGS]
  | (typeof PERMISSIONS.CASH)[keyof typeof PERMISSIONS.CASH]
  | (typeof PERMISSIONS.REPORTS.SALES)[keyof typeof PERMISSIONS.REPORTS.SALES]
  | (typeof PERMISSIONS.REPORTS.INVENTORY)[keyof typeof PERMISSIONS.REPORTS.INVENTORY]
  | '*'; // Para el permiso wildcard

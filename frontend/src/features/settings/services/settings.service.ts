import { api } from '@/lib/api/api';
import type { GeneralSettings, PublicSettings } from '../types/settings.types';

export const settingsService = {
  getGeneralSettings: async () => {
    const response = await api.get<GeneralSettings>('/settings/general');
    return response.data;
  },

  getPublicSettings: async () => {
    const response = await api.get<PublicSettings>('/settings/public');
    return response.data;
  },

  updateGeneralSettings: async (formData: FormData) => {
    const response = await api.put('/settings/general', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

import { useQuery, useMutation } from '@tanstack/react-query';
import {
  settingsService,
  useSettingsStore,
  type GeneralSettings,
} from '@/features/settings';
import { useToast } from '@/hooks/useToast';

export function useGeneralSettings() {
  const { toast } = useToast();
  const { setGeneralSettings, setLoading } = useSettingsStore();

  const {
    data: settings,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['settings', 'general'],
    queryFn: async () => {
      setLoading(true);
      try {
        const data = await settingsService.getGeneralSettings();
        setGeneralSettings(data);
        return data;
      } finally {
        setLoading(false);
      }
    },
  });

  const updateMutation = useMutation({
    mutationFn: (formData: FormData) => {
      return settingsService.updateGeneralSettings(formData);
    },
    onSuccess: (updatedSettings: GeneralSettings) => {
      setGeneralSettings(updatedSettings);

      toast({
        title: 'Configuraciones actualizadas',
        description:
          'Los cambios han sido guardados. Refresca la página (F5) para ver los cambios.',
        duration: 7000,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Error al actualizar las configuraciones',
        variant: 'destructive',
      });
    },
  });

  const handleUpdate = async (formData: FormData) => {
    const settingsData = formData.get('settings');
    if (settingsData) {
      const parsedSettings = JSON.parse(settingsData as string);
      // If the logo is null, ensure it's explicitly sent
      if (parsedSettings.logo === null) {
        const newFormData = new FormData();
        newFormData.append(
          'settings',
          JSON.stringify({
            ...parsedSettings,
            logo: null,
          })
        );
        return updateMutation.mutateAsync(newFormData);
      }
    }
    return updateMutation.mutateAsync(formData);
  };

  return {
    settings,
    isLoading,
    error,
    updateSettings: handleUpdate,
    isUpdating: updateMutation.isPending,
  };
}

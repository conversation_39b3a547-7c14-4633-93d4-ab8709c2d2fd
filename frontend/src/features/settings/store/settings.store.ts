import { create } from 'zustand';
import { settingsService, type GeneralSettings } from '@/features/settings';

interface SettingsState {
  generalSettings: GeneralSettings | null;
  publicSettings: {
    systemName: string | null;
    logo: string | null;
  } | null;
  isLoading: boolean;
  setGeneralSettings: (settings: GeneralSettings) => void;
  setPublicSettings: (settings: {
    systemName: string | null;
    logo: string | null;
  }) => void;
  setLoading: (loading: boolean) => void;
  resetSettings: () => void;
  fetchPublicSettings: () => Promise<void>;
  fetchGeneralSettings: () => Promise<void>;
  updateGeneralSettings: (
    settings: GeneralSettings,
    logo?: File
  ) => Promise<void>;
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  generalSettings: null,
  publicSettings: null,
  isLoading: false,

  setGeneralSettings: (settings) => set({ generalSettings: settings }),

  setPublicSettings: (settings) => set({ publicSettings: settings }),

  setLoading: (loading) => set({ isLoading: loading }),

  resetSettings: () =>
    set({
      generalSettings: null,
      publicSettings: null,
      isLoading: false,
    }),

  fetchPublicSettings: async () => {
    try {
      set({ isLoading: true });
      const settings = await settingsService.getPublicSettings();
      set({
        publicSettings: settings,
        isLoading: false,
      });
    } catch (error) {
      console.error('Error fetching public settings:', error);
      set({ isLoading: false });
    }
  },

  fetchGeneralSettings: async () => {
    try {
      set({ isLoading: true });
      const settings = await settingsService.getGeneralSettings();
      set({
        generalSettings: settings,
        isLoading: false,
      });
    } catch (error) {
      console.error('Error fetching general settings:', error);
      set({ isLoading: false });
    }
  },

  updateGeneralSettings: async (settings, logo?) => {
    try {
      set({ isLoading: true });
      const formData = new FormData();
      formData.append('settings', JSON.stringify(settings));

      if (logo instanceof File) {
        formData.append('logo', logo);
      }

      const updatedSettings = await settingsService.updateGeneralSettings(
        formData
      );
      set({
        generalSettings: updatedSettings,
        publicSettings: {
          systemName: updatedSettings.systemName,
          logo: updatedSettings.logo,
        },
        isLoading: false,
      });
    } catch (error) {
      console.error('Error updating general settings:', error);
      set({ isLoading: false });
      throw error;
    }
  },
}));

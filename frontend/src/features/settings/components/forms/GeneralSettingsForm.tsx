import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Receipt,
  FileText,
  Save,
  Upload,
  ImageIcon,
  Monitor,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import {
  generalSettingsSchema,
  type GeneralSettings,
  type GeneralSettingsFormValues,
} from '@/features/settings';
import { getImageUrl } from '@/lib/utils/image';
import { APP_CONFIG } from '@/config/app.config';
import placeholderLogo from '@/assets/images/placeholder-image.png';

interface GeneralSettingsFormProps {
  initialData?: Partial<GeneralSettings>;
  onSubmit: (data: GeneralSettingsFormValues) => Promise<void>;
  isLoading?: boolean;
}

export function GeneralSettingsForm({
  initialData,
  onSubmit,
  isLoading,
}: GeneralSettingsFormProps) {
  const [previewUrl, setPreviewUrl] = useState<string>(() =>
    initialData?.logo ? getImageUrl(initialData.logo) : placeholderLogo
  );
  const [isDragging, setIsDragging] = useState(false);

  const form = useForm<GeneralSettingsFormValues>({
    resolver: zodResolver(generalSettingsSchema),
    defaultValues: {
      systemName: initialData?.systemName || '',
      businessName: initialData?.businessName || '',
      address: initialData?.address || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      taxId: initialData?.taxId || '',
      description: initialData?.description || '',
      logo: initialData?.logo || null,
    },
  });

  const handleImageChange = useCallback(
    (
      event:
        | React.ChangeEvent<HTMLInputElement>
        | React.DragEvent<HTMLDivElement>,
      field: any
    ) => {
      let file: File | null = null;

      if ('dataTransfer' in event) {
        file = event.dataTransfer.files?.[0];
      } else {
        file = event.target.files?.[0];
      }

      if (file) {
        // Convertir MB a bytes para la validación
        const maxSizeInBytes = APP_CONFIG.files.maxLogoSize * 1024 * 1024;

        if (file.size > maxSizeInBytes) {
          form.setError('logo', {
            type: 'manual',
            message: `La imagen no debe superar los ${APP_CONFIG.files.maxLogoSize}MB`,
          });
          return;
        }

        if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
          form.setError('logo', {
            type: 'manual',
            message: 'Formato de imagen no válido. Use JPG, PNG o WebP',
          });
          return;
        }

        const reader = new FileReader();
        reader.onload = () => {
          field.onChange(file);
          setPreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    },
    [form]
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    },
    []
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    },
    []
  );

  const handleRemoveImage = useCallback((field: any) => {
    field.onChange(null);
    setPreviewUrl(placeholderLogo);
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
        <Card className='p-6'>
          <div className='grid gap-6'>
            <FormField
              control={form.control}
              name='systemName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.systemName && 'text-destructive'
                    )}
                  >
                    <Monitor className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      Nombre del Sistema
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ej: Sistema de Gestión Farmacéutica'
                      {...field}
                      className={cn(
                        form.formState.errors.systemName && 'border-red-500'
                      )}
                    />
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='businessName'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.businessName && 'text-destructive'
                    )}
                  >
                    <Building2 className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      Nombre de la Sucursal
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ej: Farmacia Central'
                      {...field}
                      className={cn(
                        form.formState.errors.businessName && 'border-red-500'
                      )}
                    />
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='address'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.address && 'text-destructive'
                    )}
                  >
                    <MapPin className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      Dirección
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ej: Av. Principal #123'
                      {...field}
                      className={cn(
                        form.formState.errors.address && 'border-red-500'
                      )}
                    />
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                control={form.control}
                name='phone'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.phone && 'text-destructive'
                      )}
                    >
                      <Phone className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Teléfono
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Ej: +1234567890'
                        {...field}
                        className={cn(
                          form.formState.errors.phone && 'border-red-500'
                        )}
                      />
                    </FormControl>
                    <FormMessage className='text-destructive text-sm' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.email && 'text-destructive'
                      )}
                    >
                      <Mail className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Correo Electrónico
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='email'
                        placeholder='<EMAIL>'
                        {...field}
                        className={cn(
                          form.formState.errors.email && 'border-red-500'
                        )}
                      />
                    </FormControl>
                    <FormMessage className='text-destructive text-sm' />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='taxId'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.taxId && 'text-destructive'
                    )}
                  >
                    <Receipt className='w-4 h-4 text-muted-foreground' />
                    <div className='flex items-center gap-1'>
                      RUC/NIT
                      <span className='text-destructive'>*</span>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Ingrese el RUC/NIT'
                      {...field}
                      className={cn(
                        form.formState.errors.taxId && 'border-red-500'
                      )}
                    />
                  </FormControl>
                  <FormMessage className='text-destructive text-sm' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <div className='flex items-center space-x-2'>
                    <FileText className='w-4 h-4 text-muted-foreground' />
                    <FormLabel>Descripción</FormLabel>
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder='Descripción de la sucursal'
                      className='min-h-[100px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='logo'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='flex items-center gap-2 mb-4'>
                    <ImageIcon className='w-4 h-4 text-muted-foreground' />
                    Logo del Sistema
                  </FormLabel>
                  <FormControl>
                    <div className='space-y-4'>
                      <div
                        className={cn(
                          'relative border-2 border-dashed rounded-xl p-8 transition-all duration-200 ease-in-out',
                          isDragging
                            ? 'border-primary bg-primary/5'
                            : 'border-muted-foreground/25',
                          'hover:border-primary/50 hover:bg-primary/5'
                        )}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => {
                          e.preventDefault();
                          setIsDragging(false);
                          handleImageChange(e, field);
                        }}
                      >
                        <div className='flex flex-col items-center justify-center gap-6'>
                          {/* Preview de la imagen */}
                          <div className='relative group'>
                            <div className='w-48 h-48 rounded-xl overflow-hidden border bg-background flex items-center justify-center'>
                              <img
                                src={previewUrl}
                                alt='Logo Preview'
                                className='w-full h-full object-contain p-2'
                                onError={() => setPreviewUrl(placeholderLogo)}
                              />
                            </div>
                            {previewUrl !== placeholderLogo && (
                              <button
                                type='button'
                                onClick={() => handleRemoveImage(field)}
                                className='absolute -top-2 -right-2 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full p-1.5 shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200'
                              >
                                <X className='w-4 h-4' />
                              </button>
                            )}
                          </div>

                          <div className='text-center space-y-2'>
                            <div className='flex flex-col items-center gap-2'>
                              <Upload className='w-8 h-8 text-muted-foreground mb-2' />
                              <p className='text-sm text-muted-foreground'>
                                Arrastra y suelta tu logo aquí o
                              </p>
                              <Input
                                type='file'
                                accept='image/jpeg,image/png,image/webp'
                                className='hidden'
                                id='logo-input'
                                onChange={(e) => handleImageChange(e, field)}
                              />
                              <Button
                                type='button'
                                variant='secondary'
                                size='sm'
                                className='mt-2'
                                onClick={() =>
                                  document.getElementById('logo-input')?.click()
                                }
                              >
                                Seleccionar archivo
                              </Button>
                              <p className='text-xs text-muted-foreground mt-2'>
                                PNG, JPG o WebP (máx.{' '}
                                {APP_CONFIG.files.maxLogoSize}MB)
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end space-x-4'>
              <Button
                type='submit'
                disabled={isLoading}
                className='flex items-center gap-2'
              >
                <Save className='w-4 h-4' />
                {isLoading ? 'Guardando...' : 'Guardar Cambios'}
              </Button>
            </div>
          </div>
        </Card>
      </form>
    </Form>
  );
}

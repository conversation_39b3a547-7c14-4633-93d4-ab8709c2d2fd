export const SETTINGS_VALIDATION = {
  SYSTEM_NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
    REQUIRED: 'El nombre del sistema es requerido',
    MIN_MESSAGE: 'El nombre del sistema debe tener al menos 3 caracteres',
    MAX_MESSAGE: 'El nombre del sistema no puede exceder los 100 caracteres',
  },
  BUSINESS_NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
    REQUIRED: 'El nombre del negocio es requerido',
    MIN_MESSAGE: 'El nombre del negocio debe tener al menos 3 caracteres',
    MAX_MESSAGE: 'El nombre del negocio no puede exceder los 100 caracteres',
  },
  ADDRESS: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 200,
    REQUIRED: 'La dirección es requerida',
    MIN_MESSAGE: 'La dirección debe tener al menos 5 caracteres',
    MAX_MESSAGE: 'La dirección no puede exceder 200 caracteres',
  },
  DESCRIPTION: {
    MAX_LENGTH: 500,
    MAX_MESSAGE: 'La descripción no puede exceder 500 caracteres',
  },
  PHONE: {
    REQUIRED: 'El teléfono es requerido',
    REGEX: /^\+?[1-9]\d{0,3}?[-.\s]?\(?([0-9]{1,3})\)?[-.\s]?([0-9]{1,4})[-.\s]?([0-9]{1,4})$/,
    INVALID_FORMAT: 'Formato de teléfono inválido',
  },
  EMAIL: {
    REQUIRED: 'El correo electrónico es requerido',
    INVALID_FORMAT: 'Correo electrónico inválido',
  },
  TAX_ID: {
    MIN_LENGTH: 1,
    REQUIRED: 'El NIT es requerido',
    MIN_MESSAGE: 'El NIT debe tener al menos 1 caracteres',
  },
} as const;
import * as z from 'zod';
import { SETTINGS_VALIDATION } from '@/features/settings';

export const generalSettingsSchema = z.object({
  systemName: z
    .string({
      required_error: SETTINGS_VALIDATION.SYSTEM_NAME.REQUIRED,
    })
    .min(
      SETTINGS_VALIDATION.SYSTEM_NAME.MIN_LENGTH,
      SETTINGS_VALIDATION.SYSTEM_NAME.MIN_MESSAGE
    )
    .max(
      SETTINGS_VALIDATION.SYSTEM_NAME.MAX_LENGTH,
      SETTINGS_VALIDATION.SYSTEM_NAME.MAX_MESSAGE
    ),
  businessName: z
    .string({
      required_error: SETTINGS_VALIDATION.BUSINESS_NAME.REQUIRED,
    })
    .min(
      SETTINGS_VALIDATION.BUSINESS_NAME.MIN_LENGTH,
      SETTINGS_VALIDATION.BUSINESS_NAME.MIN_MESSAGE
    )
    .max(
      SETTINGS_VALIDATION.BUSINESS_NAME.MAX_LENGTH,
      SETTINGS_VALIDATION.BUSINESS_NAME.MAX_MESSAGE
    ),
  address: z
    .string({
      required_error: SETTINGS_VALIDATION.ADDRESS.REQUIRED,
    })
    .min(
      SETTINGS_VALIDATION.ADDRESS.MIN_LENGTH,
      SETTINGS_VALIDATION.ADDRESS.MIN_MESSAGE
    )
    .max(
      SETTINGS_VALIDATION.ADDRESS.MAX_LENGTH,
      SETTINGS_VALIDATION.ADDRESS.MAX_MESSAGE
    ),
  phone: z
    .string({
      required_error: SETTINGS_VALIDATION.PHONE.REQUIRED,
    })
    .regex(
      SETTINGS_VALIDATION.PHONE.REGEX,
      SETTINGS_VALIDATION.PHONE.INVALID_FORMAT
    ),
  email: z
    .string({
      required_error: SETTINGS_VALIDATION.EMAIL.REQUIRED,
    })
    .email(SETTINGS_VALIDATION.EMAIL.INVALID_FORMAT),
  taxId: z
    .string({
      required_error: SETTINGS_VALIDATION.TAX_ID.REQUIRED,
    })
    .min(
      SETTINGS_VALIDATION.TAX_ID.MIN_LENGTH,
      SETTINGS_VALIDATION.TAX_ID.MIN_MESSAGE
    ),
  description: z
    .string()
    .max(
      SETTINGS_VALIDATION.DESCRIPTION.MAX_LENGTH,
      SETTINGS_VALIDATION.DESCRIPTION.MAX_MESSAGE
    )
    .optional(),
  logo: z.any().optional().nullable(),
});

export type GeneralSettingsFormValues = z.infer<typeof generalSettingsSchema>;

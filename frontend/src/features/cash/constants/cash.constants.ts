export const CASH_MOVEMENT_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  ALL: 'ALL',
} as const;

export const MOVEMENT_TYPE_LABELS = {
  [CASH_MOVEMENT_TYPES.INCOME]: 'Ingreso',
  [CASH_MOVEMENT_TYPES.EXPENSE]: 'Egreso',
  ALL: 'Todos', // Etiqueta para el filtro ALL
} as const;

export const CASH_REGISTER_STATUS = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
} as const;

export const CASH_LABELS = {
  REGISTER: {
    TITLE: 'Caja',
    OPEN: '<PERSON><PERSON><PERSON>',
    CLOSE: '<PERSON><PERSON><PERSON>',
    INITIAL_BALANCE: 'Balance Inicial',
    FINAL_BALANCE: 'Balance Final',
    CURRENT_BALANCE: 'Balance Actual',
    OBSERVATIONS: 'Observaciones',
    STATUS: {
      OPEN: 'Abierta',
      CLOSED: 'Cerrada',
    },
  },
  MOVEMENT: {
    TITLE: 'Movimiento',
    NEW: 'Nuevo Movimiento',
    EDIT: 'Editar Movimiento',
    TYPE: 'Tipo',
    AMOUNT: 'Monto',
    CONCEPT: 'Concepto',
    REFERENCE: 'Referencia',
    TYPES: {
      INCOME: 'Ingreso',
      EXPENSE: 'Egreso',
    },
  },
} as const;

export const CASH_MESSAGES = {
  SUCCESS: {
    REGISTER_OPENED: 'Caja abierta exitosamente',
    REGISTER_CLOSED: 'Caja cerrada exitosamente',
    MOVEMENT_CREATED: 'Movimiento creado exitosamente',
    MOVEMENT_UPDATED: 'Movimiento actualizado exitosamente',
    MOVEMENT_DELETED: 'Movimiento eliminado exitosamente',
  },
  ERROR: {
    REGISTER_NOT_FOUND: 'No se encontró la caja',
    REGISTER_ALREADY_OPEN: 'Ya existe una caja abierta',
    REGISTER_ALREADY_CLOSED: 'La caja ya está cerrada',
    REGISTER_NOT_OPEN: 'No hay una caja abierta',
    INVALID_BALANCE: 'El balance proporcionado no es válido',
    MOVEMENT_NOT_FOUND: 'No se encontró el movimiento',
  },
} as const;

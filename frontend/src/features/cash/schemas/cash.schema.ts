import { z } from 'zod';
import { CASH_MOVEMENT_TYPES } from '../constants/cash.constants';

// Schema para abrir caja
export const openCashRegisterSchema = z.object({
  initialBalance: z.number().min(0, 'El monto debe ser mayor o igual a 0'),
  observations: z
    .string()
    .max(200, 'Las observaciones no deben exceder los 200 caracteres')
    .nullable()
    .optional(),
});

// Schema para cerrar caja
export const closeCashRegisterSchema = z.object({
  finalBalance: z.number().min(0, 'El monto debe ser mayor o igual a 0'),
  observations: z
    .string()
    .max(200, 'Las observaciones no deben exceder los 200 caracteres')
    .nullable()
    .optional(),
});

// Schema para crear movimiento de caja
export const createCashMovementSchema = z.object({
  type: z.enum([CASH_MOVEMENT_TYPES.INCOME, CASH_MOVEMENT_TYPES.EXPENSE], {
    required_error: 'El tipo de movimiento es requerido',
  }),
  amount: z
    .number({
      required_error: 'El monto es requerido',
    })
    .min(0, 'El monto debe ser mayor o igual a 0'),
  concept: z
    .string()
    .min(3, 'El concepto debe tener al menos 3 caracteres')
    .max(100, 'El concepto no debe exceder los 100 caracteres'),
  reference: z
    .string()
    .max(50, 'La referencia no debe exceder los 50 caracteres')
    .nullable()
    .optional(),
  observations: z
    .string()
    .max(200, 'Las observaciones no deben exceder los 200 caracteres')
    .nullable()
    .optional(),
});

// Schema para actualizar movimiento de caja
export const updateCashMovementSchema = z.object({
  concept: z
    .string({
      required_error: 'El concepto es requerido',
    })
    .min(1, 'El concepto es requerido'),
  reference: z
    .string()
    .max(50, 'La referencia no debe exceder los 50 caracteres')
    .nullable()
    .optional(),
  observations: z
    .string()
    .max(200, 'Las observaciones no deben exceder los 200 caracteres')
    .nullable()
    .optional(),
});

// Schema para obtener movimientos
export const getCashMovementsSchema = z.object({
  page: z.coerce.number().optional(),
  limit: z.coerce.number().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  type: z
    .enum(Object.values(CASH_MOVEMENT_TYPES) as [string, ...string[]])
    .optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Tipos inferidos
export type OpenCashRegisterSchema = z.infer<typeof openCashRegisterSchema>;
export type CloseCashRegisterSchema = z.infer<typeof closeCashRegisterSchema>;
export type CreateCashMovementSchema = z.infer<typeof createCashMovementSchema>;
export type UpdateCashMovementSchema = z.infer<typeof updateCashMovementSchema>;
export type GetCashMovementsSchema = z.infer<typeof getCashMovementsSchema>;

import { create } from 'zustand';
import { CASH_MOVEMENT_TYPES } from '../constants/cash.constants';

type MovementFilterType =
  | (typeof CASH_MOVEMENT_TYPES)[keyof typeof CASH_MOVEMENT_TYPES]
  | 'ALL';

interface MovementsState {
  filters: {
    search: string;
    page: number;
    limit: number;
    type: MovementFilterType;
    startDate: string;
    endDate: string;
  };
  setFilters: (filters: Partial<MovementsState['filters']>) => void;
}

export const useMovementsStore = create<MovementsState>((set) => ({
  filters: {
    search: '',
    page: 1,
    limit: 10,
    type: 'ALL',
    startDate: '',
    endDate: '',
  },
  setFilters: (newFilters) =>
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    })),
}));

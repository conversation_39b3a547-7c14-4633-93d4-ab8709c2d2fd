import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, AlertCircle, Calendar, Wallet } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';
import { formatDate } from '@/lib/utils/format';
import type { CashRegister } from '../types/cash.types';

interface CashRegisterDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading: boolean;
  register: CashRegister | null;
}

export function CashRegisterDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  isLoading,
  register,
}: CashRegisterDeleteDialogProps) {
  if (!register) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='sm:max-w-[425px]'>
        <AlertDialogHeader>
          <AlertDialogTitle className='flex items-center gap-2'>
            <Trash2 className='h-5 w-5' />
            Eliminar registro de caja
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className='space-y-4'>
              <p className='text-sm'>
                Esta acción eliminará permanentemente el registro y todos sus
                movimientos asociados. Esta operación no se puede deshacer.
              </p>

              <div className='rounded-lg border bg-muted/30 p-4 space-y-4'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium'>Estado</span>
                  <Badge
                    variant={register.status === 'OPEN' ? 'success' : 'default'}
                    className='font-medium'
                  >
                    {register.status === 'OPEN' ? 'Abierta' : 'Cerrada'}
                  </Badge>
                </div>

                <div className='space-y-3 divide-y divide-border'>
                  <div className='flex items-center gap-3 pb-2'>
                    <div className='h-8 w-8 rounded-full bg-muted flex items-center justify-center'>
                      <Calendar className='h-4 w-4 text-muted-foreground' />
                    </div>
                    <div className='grid grid-cols-2 gap-2 flex-1'>
                      <div>
                        <span className='text-xs text-muted-foreground block'>
                          Apertura
                        </span>
                        <span className='text-sm font-medium'>
                          {formatDate(register.openingDate)}
                        </span>
                      </div>
                      <div>
                        <span className='text-xs text-muted-foreground block'>
                          Cierre
                        </span>
                        <span className='text-sm font-medium'>
                          {register.closingDate
                            ? formatDate(register.closingDate)
                            : '---'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center gap-3 pt-2'>
                    <div className='h-8 w-8 rounded-full bg-muted flex items-center justify-center'>
                      <Wallet className='h-4 w-4 text-muted-foreground' />
                    </div>
                    <div className='grid grid-cols-2 gap-2 flex-1'>
                      <div>
                        <span className='text-xs text-muted-foreground block'>
                          Balance inicial
                        </span>
                        <span className='text-sm font-medium'>
                          {formatCurrency(register.initialBalance)}
                        </span>
                      </div>
                      <div>
                        <span className='text-xs text-muted-foreground block'>
                          Balance final
                        </span>
                        <span className='text-sm font-medium'>
                          {register.finalBalance
                            ? formatCurrency(register.finalBalance)
                            : '---'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            disabled={isLoading}
            className='border-none hover:bg-muted'
          >
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className='bg-destructive hover:bg-destructive/90 text-destructive-foreground gap-2'
          >
            {isLoading ? (
              <>
                <span className='h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                Eliminando...
              </>
            ) : (
              <>
                <Trash2 className='h-4 w-4' />
                Eliminar registro
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

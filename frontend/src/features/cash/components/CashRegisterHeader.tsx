import { DollarSign } from 'lucide-react';
import { PageHeader } from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';

import { CashRegisterHeaderProps } from '@/features/cash';

export function CashRegisterHeader({
  title,
  description,
  onOpenRegister,
  onCloseRegister,
  canManage,
  isRegisterOpen,
}: CashRegisterHeaderProps) {
  return (
    <div className='flex justify-between items-center'>
      <PageHeader title={title} description={description} />
      {canManage && (
        <Button
          onClick={isRegisterOpen ? onCloseRegister : onOpenRegister}
          variant={isRegisterOpen ? 'destructive' : 'default'}
          className='gap-2'
        >
          <DollarSign className='h-4 w-4' />
          {isRegisterOpen ? 'Cerrar Caja' : 'Abrir Caja'}
        </Button>
      )}
    </div>
  );
}

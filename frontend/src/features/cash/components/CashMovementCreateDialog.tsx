import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PlusCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import {
  createCashMovementSchema,
  CashMovementCreateDialogProps,
  CASH_MOVEMENT_TYPES,
  type CreateCashMovementDto,
  type MovementType,
} from '@/features/cash';

export function CashMovementCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  type,
}: CashMovementCreateDialogProps) {
  const form = useForm<CreateCashMovementDto>({
    resolver: zodResolver(createCashMovementSchema),
    defaultValues: {
      type,
      amount: 0,
      concept: '',
      reference: '',
      observations: '',
    },
  });

  useEffect(() => {
    form.reset({ ...form.getValues(), type });
  }, [type, form]);

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const getTypeLabel = (type: MovementType): string => {
    switch (type) {
      case CASH_MOVEMENT_TYPES.INCOME:
        return 'Ingreso';
      case CASH_MOVEMENT_TYPES.EXPENSE:
        return 'Egreso';
      default:
        return 'Movimiento';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <PlusCircle className='w-5 h-5 text-primary' />
            Nuevo {getTypeLabel(type)}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className='space-y-4 py-4'
          >
            <FormField
              control={form.control}
              name='amount'
              render={({ field }) => (
                <FormItem className='grid gap-2'>
                  <FormLabelWithTooltip
                    label='Monto'
                    tooltip='Ingrese el monto del movimiento'
                    required
                  />
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      step='0.01'
                      placeholder='0.00'
                      className='text-lg w-full'
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value ? Number(value) : 0);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='concept'
              render={({ field }) => (
                <FormItem className='grid gap-2'>
                  <FormLabelWithTooltip
                    label='Concepto'
                    tooltip='Ingrese el concepto del movimiento'
                    required
                  />
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='Concepto del movimiento'
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='reference'
              render={({ field }) => (
                <FormItem className='grid gap-2'>
                  <FormLabelWithTooltip
                    label='Referencia'
                    tooltip='Número de referencia o comprobante (opcional)'
                  />
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='Referencia (opcional)'
                      className='w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='observations'
              render={({ field }) => (
                <FormItem className='grid gap-2'>
                  <FormLabelWithTooltip
                    label='Observaciones'
                    tooltip='Notas adicionales sobre el movimiento'
                  />
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder='Observaciones adicionales'
                      className='resize-none min-h-[100px] w-full'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-4 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Creando...' : 'Crear Movimiento'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils/format';
import {
  Edit2,
  Trash2,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

import { CASH_MOVEMENT_TYPES, CashMovementsTableProps } from '@/features/cash';

const tableVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      when: 'beforeChildren',
      staggerChildren: 0.1,
    },
  },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.3 },
  },
};

export function CashMovementsTable({
  movements,
  isLoading,
  pagination,
  permissions,
  onEdit,
  onDelete,
  showPagination = true, // valor por defecto true
}: CashMovementsTableProps) {
  if (isLoading) {
    return (
      <motion.div initial='hidden' animate='visible' variants={tableVariants}>
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Fecha</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Concepto</TableHead>
                <TableHead className='text-right'>Monto</TableHead>
                <TableHead>Usuario</TableHead>
                {permissions.canManage && (
                  <TableHead className='text-right'>Acciones</TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i} className='animate-pulse'>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[60px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[200px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[80px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px]' />
                  </TableCell>
                  {permissions.canManage && (
                    <TableCell className='text-right'>
                      <Skeleton className='h-4 w-[80px] ml-auto' />
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </motion.div>
    );
  }

  if (movements.length === 0) {
    return (
      <div className='text-center py-8 border rounded-md bg-muted/10'>
        <AlertCircle className='h-8 w-8 text-muted-foreground mx-auto mb-2' />
        <p className='text-muted-foreground font-medium'>
          No hay movimientos registrados
        </p>
      </div>
    );
  }

  return (
    <motion.div initial='hidden' animate='visible' variants={tableVariants}>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow className='bg-muted/50'>
              <TableHead className='w-[180px]'>Fecha</TableHead>
              <TableHead className='w-[120px]'>Tipo</TableHead>
              <TableHead>Concepto</TableHead>
              <TableHead className='w-[150px] text-right'>Monto</TableHead>
              <TableHead className='w-[150px]'>Usuario</TableHead>
              {permissions.canManage && (
                <TableHead className='w-[100px] text-right'>Acciones</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {movements.map((movement) => (
              <motion.tr
                key={movement._id}
                className='hover:bg-muted/50 border-b transition-colors'
                variants={rowVariants}
                initial='hidden'
                animate='visible'
              >
                <TableCell className='font-medium whitespace-nowrap'>
                  {format(new Date(movement.createdAt), 'dd/MM/yyyy HH:mm', {
                    locale: es,
                  })}
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      movement.type === CASH_MOVEMENT_TYPES.INCOME
                        ? 'success'
                        : 'destructive'
                    }
                    className='font-medium'
                  >
                    {movement.type === CASH_MOVEMENT_TYPES.INCOME
                      ? 'Ingreso'
                      : 'Egreso'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className='text-left block w-full'>
                        <div className='max-w-[300px] truncate'>
                          {movement.concept}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className='space-y-1'>
                          <p className='font-medium'>Concepto:</p>
                          <p>{movement.concept}</p>
                          {movement.reference && (
                            <>
                              <p className='font-medium mt-2'>Referencia:</p>
                              <p>{movement.reference}</p>
                            </>
                          )}
                          {movement.observations && (
                            <>
                              <p className='font-medium mt-2'>Observaciones:</p>
                              <p>{movement.observations}</p>
                            </>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell
                  className={`text-right font-medium whitespace-nowrap ${
                    movement.type === 'INCOME'
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {movement.type === 'INCOME' ? '+' : '-'}
                  {formatCurrency(movement.amount)}
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className='max-w-[150px] truncate text-muted-foreground'>
                          {movement.createdBy?.username || 'N/A'}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Usuario: {movement.createdBy?.username || 'N/A'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                {permissions.canManage && (
                  <TableCell className='text-right'>
                    <div className='flex justify-end gap-2'>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => onEdit(movement)}
                              className='hover:bg-muted'
                            >
                              <Edit2 className='h-4 w-4' />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Editar movimiento</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => onDelete(movement)}
                              className='hover:bg-destructive/10 hover:text-destructive'
                            >
                              <Trash2 className='h-4 w-4' />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Eliminar movimiento</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                )}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {showPagination && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className='flex items-center justify-between p-4'
        >
          <div className='flex items-center gap-4'>
            <div className='text-sm text-muted-foreground'>
              Total: {pagination.totalRecords} registros
            </div>
            <div className='flex items-center gap-2'>
              <span className='text-sm text-muted-foreground'>Mostrar:</span>
              <select
                className='h-8 w-[70px] rounded-md border border-input bg-background px-2 text-sm'
                value={pagination.limit}
                onChange={(e) =>
                  pagination.onLimitChange(Number(e.target.value))
                }
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage - 1)
              }
              disabled={pagination.currentPage <= 1}
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <span className='text-sm'>
              Página {pagination.currentPage} de {pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage + 1)
              }
              disabled={pagination.currentPage >= pagination.totalPages}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

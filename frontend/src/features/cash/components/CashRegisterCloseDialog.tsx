import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { DollarSign, CheckCircle2, AlertCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import {
  closeCashRegisterSchema,
  type CloseCashRegisterSchema,
  CashRegisterCloseDialogProps,
} from '@/features/cash';

export function CashRegisterCloseDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  expectedBalance,
  summary,
  currentUser,
}: CashRegisterCloseDialogProps) {
  const [showForceCloseDialog, setShowForceCloseDialog] = useState(false);
  const [formData, setFormData] = useState<CloseCashRegisterSchema | null>(
    null
  );

  const form = useForm<CloseCashRegisterSchema>({
    resolver: zodResolver(closeCashRegisterSchema),
    defaultValues: {
      finalBalance: 0,
      observations: '',
    },
  });

  const finalBalance = form.watch('finalBalance');
  const difference = finalBalance - expectedBalance;
  const hasSignificantDifference = Math.abs(difference) > 0;

  const handleSubmit = async (data: CloseCashRegisterSchema) => {
    if (hasSignificantDifference) {
      setFormData(data);
      setShowForceCloseDialog(true);
      return;
    }
    await onSubmit(data);
  };

  const handleForceClose = async () => {
    if (formData) {
      await onSubmit(formData, true);
      setShowForceCloseDialog(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto'>
          <DialogHeader className='pb-4 border-b'>
            <DialogTitle className='flex items-center gap-2 text-xl'>
              <DollarSign className='w-5 h-5 text-primary' />
              Cierre de Caja
            </DialogTitle>
          </DialogHeader>

          <div className='space-y-4'>
            {summary ? (
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Resumen del Día</CardTitle>
                </CardHeader>
                <CardContent className='space-y-2'>
                  <div className='grid grid-cols-2 gap-2 text-sm'>
                    <div>Saldo Inicial:</div>
                    <div className='text-right'>
                      ${summary.initialBalance.toFixed(2)}
                    </div>

                    <div className='font-medium'>Total Ingresos:</div>
                    <div className='text-right text-green-600'>
                      ${summary.totalIncome.toFixed(2)}
                    </div>

                    <div className='font-medium'>Total Egresos:</div>
                    <div className='text-right text-red-600'>
                      ${summary.totalExpense.toFixed(2)}
                    </div>

                    <div className='col-span-2 my-2 border-t border-gray-200' />

                    <div className='font-medium'>Saldo Esperado:</div>
                    <div className='text-right font-medium'>
                      ${expectedBalance.toFixed(2)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className='py-4'>
                  <div className='text-center text-muted-foreground'>
                    Cargando resumen...
                  </div>
                </CardContent>
              </Card>
            )}

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-4'
              >
                <Card>
                  <CardHeader>
                    <CardTitle className='text-lg'>
                      Conteo de Efectivo
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-4'>
                    <FormField
                      control={form.control}
                      name='finalBalance'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabelWithTooltip
                            label='Balance Final'
                            tooltip='Ingrese el monto total contado en caja'
                            required
                          />
                          <FormControl>
                            <Input
                              value={field.value?.toString() ?? ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(
                                  value === '' ? undefined : Number(value)
                                );
                              }}
                              type='number'
                              step='0.01'
                              placeholder='0.00'
                              className='text-lg'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {finalBalance !== undefined && (
                      <Alert
                        variant={
                          Math.abs(difference) <= 1 ? 'default' : 'destructive'
                        }
                      >
                        <AlertDescription className='flex items-center gap-2'>
                          {Math.abs(difference) <= 1 ? (
                            <>
                              <CheckCircle2 className='h-4 w-4' />
                              Balance correcto
                            </>
                          ) : (
                            <>
                              <AlertCircle className='h-4 w-4' />
                              Diferencia: ${difference.toFixed(2)}
                            </>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}

                    <FormField
                      control={form.control}
                      name='observations'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabelWithTooltip
                            label='Observaciones'
                            tooltip='Notas sobre el cierre o explicación de diferencias'
                          />
                          <FormControl>
                            <Textarea
                              placeholder='Observaciones sobre el cierre de caja...'
                              className='resize-none min-h-[100px]'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                <div className='flex justify-end gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => onOpenChange(false)}
                  >
                    Cancelar
                  </Button>
                  <Button type='submit' disabled={isLoading}>
                    {isLoading ? 'Cerrando...' : 'Cerrar Caja'}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={showForceCloseDialog}
        onOpenChange={setShowForceCloseDialog}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Forzar cierre de caja?</AlertDialogTitle>
            <AlertDialogDescription>
              Existe una diferencia de Bs {Math.abs(difference).toFixed(2)}{' '}
              entre el balance esperado y el conteo final.
              {difference > 0 ? ' Hay un sobrante.' : ' Hay un faltante.'}
              <br />
              <br />
              ¿Está seguro que desea forzar el cierre de caja con esta
              diferencia?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleForceClose}
              className='bg-destructive hover:bg-destructive/90'
            >
              Forzar Cierre
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

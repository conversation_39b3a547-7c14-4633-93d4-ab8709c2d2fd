import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Wallet } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

import {
  openCashRegisterSchema,
  type OpenCashRegisterSchema,
  CashRegisterOpenDialogProps,
} from '@/features/cash';

export function CashRegisterOpenDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: CashRegisterOpenDialogProps) {
  const form = useForm<OpenCashRegisterSchema>({
    resolver: zodResolver(openCashRegisterSchema),
    defaultValues: {
      initialBalance: undefined,
      observations: '',
    },
  });

  const handleSubmit = async (data: OpenCashRegisterSchema) => {
    try {
      await onSubmit(data);
      form.reset();
      onOpenChange(false);
    } catch (error) {
      // El error será manejado por el hook
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Wallet className='w-5 h-5 text-primary' />
            Abrir Nueva Caja
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4'
          >
            <FormField
              control={form.control}
              name='initialBalance'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Saldo Inicial'
                    tooltip='Ingrese el monto inicial en caja'
                    required
                    error={!!form.formState.errors.initialBalance}
                  />
                  <FormControl>
                    <Input
                      value={field.value?.toString() ?? ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === '' || isNaN(Number(value))) {
                          field.onChange(undefined);
                        } else {
                          field.onChange(Number(value));
                        }
                      }}
                      type='number'
                      step='0.01'
                      placeholder='0.00'
                      className='text-lg focus-visible:ring-offset-0'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='observations'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Observaciones'
                    tooltip='Notas adicionales sobre la apertura de caja'
                    error={!!form.formState.errors.observations}
                  />
                  <FormControl>
                    <Textarea
                      placeholder='Observaciones sobre la apertura de caja...'
                      className='resize-none min-h-[100px] focus-visible:ring-offset-0'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-4 pt-4 border-t mt-4 shrink-0 px-0'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Abriendo...' : 'Abrir Caja'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

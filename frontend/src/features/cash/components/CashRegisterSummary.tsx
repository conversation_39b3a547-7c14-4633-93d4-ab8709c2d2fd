import {
  Wallet,
  ArrowUpCircle,
  ArrowDownCircle,
  Calculator,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils/format';
import { CashRegisterSummaryProps } from '@/features/cash';

function SummarySkeleton() {
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardContent className='py-4'>
            <Skeleton className='h-4 w-24 mb-2' />
            <Skeleton className='h-6 w-32' />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function CashRegisterSummary({
  summary,
  isLoading,
}: CashRegisterSummaryProps) {
  if (isLoading || !summary) {
    return <SummarySkeleton />;
  }

  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
      <Card>
        <CardContent className='py-6'>
          <div className='flex items-center gap-4'>
            <Wallet className='w-8 h-8 text-blue-600' />
            <div>
              <p className='text-sm font-medium text-muted-foreground'>
                Saldo Inicial
              </p>
              <p className='text-2xl font-bold'>
                {formatCurrency(summary.initialBalance)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='py-6'>
          <div className='flex items-center gap-4'>
            <ArrowUpCircle className='w-8 h-8 text-green-600' />
            <div>
              <p className='text-sm font-medium text-muted-foreground'>
                Total Ingresos
              </p>
              <p className='text-2xl font-bold text-green-600'>
                {formatCurrency(summary.totalIncome)}
              </p>
              <p className='text-sm text-muted-foreground'>
                {summary.byType['INCOME'].count} movimientos
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='py-6'>
          <div className='flex items-center gap-4'>
            <ArrowDownCircle className='w-8 h-8 text-red-600' />
            <div>
              <p className='text-sm font-medium text-muted-foreground'>
                Total Egresos
              </p>
              <p className='text-2xl font-bold text-red-600'>
                {formatCurrency(summary.totalExpense)}
              </p>
              <p className='text-sm text-muted-foreground'>
                {summary.byType['EXPENSE'].count} movimientos
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className='py-6'>
          <div className='flex items-center gap-4'>
            <Calculator className='w-8 h-8 text-purple-600' />
            <div>
              <p className='text-sm font-medium text-muted-foreground'>
                Saldo Actual
              </p>
              <p className='text-2xl font-bold'>
                {formatCurrency(summary.currentBalance)}
              </p>
              <p className='text-sm text-muted-foreground'>
                {summary.totalMovements} movimientos totales
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

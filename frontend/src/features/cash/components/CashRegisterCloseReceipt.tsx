import { useRef, useCallback } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Printer, FileDown, X } from 'lucide-react';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils/format';
import { CashRegisterCloseReceiptProps } from '@/features/cash';

const THERMAL_PRINTER_CONFIG = {
  sizes: {
    STANDARD: {
      name: '80mm',
      widthMm: 80,
      widthPixels: 302, // 80mm en pixels
    },
    SMALL: {
      name: '58mm',
      widthMm: 58,
      widthPixels: 219, // 58mm en pixels
    },
  },
  fontSize: {
    title: '16px',
    subtitle: '14px',
    normal: '12px',
  },
  spacing: {
    padding: '16px',
    marginBottom: '8px',
  },
  borders: {
    dashed: '1px dashed #000',
  },
};

const SELECTED_PRINTER = THERMAL_PRINTER_CONFIG.sizes.STANDARD;

const RECEIPT_CONFIG = {
  width: SELECTED_PRINTER.widthPixels,
  fontSize: THERMAL_PRINTER_CONFIG.fontSize,
  padding: THERMAL_PRINTER_CONFIG.spacing.padding,
};

export function CashRegisterCloseReceipt({
  open,
  onClose,
  summary,
  finalBalance,
  observations,
  forcedClose,
  currentUser,
  closingDate,
  directPrint,
}: CashRegisterCloseReceiptProps) {
  const difference = finalBalance - summary.currentBalance;
  const receiptRef = useRef<HTMLDivElement>(null);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const generatePDF = async () => {
    if (!receiptRef.current) return null;

    try {
      const canvas = await html2canvas(receiptRef.current, {
        scale: 3,
        backgroundColor: '#ffffff',
        width: RECEIPT_CONFIG.width,
        useCORS: true,
        logging: false,
        windowWidth: RECEIPT_CONFIG.width,
      });

      const aspectRatio = canvas.height / canvas.width;
      const pdfWidth = RECEIPT_CONFIG.width;
      const pdfHeight = pdfWidth * aspectRatio;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [pdfWidth, pdfHeight],
        hotfixes: ['px_scaling'],
      });

      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        0,
        0,
        pdfWidth,
        pdfHeight,
        undefined,
        'FAST'
      );

      return pdf;
    } catch (error) {
      return null;
    }
  };

  const handlePrint = async () => {
    if (!receiptRef.current) return;

    try {
      const canvas = await html2canvas(receiptRef.current, {
        scale: 2,
        backgroundColor: '#ffffff',
        width: RECEIPT_CONFIG.width,
        useCORS: true,
        logging: false,
      });

      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('No se pudo abrir la ventana de impresión');
        return;
      }

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Imprimir Recibo</title>
            <style>
              @page {
                size: ${SELECTED_PRINTER.widthMm}mm auto !important;
                margin: 0 !important;
              }
              body {
                margin: 0;
                padding: 0;
                width: ${SELECTED_PRINTER.widthMm}mm;
              }
              img {
                width: 100%;
                height: auto;
              }
              @media print {
                html, body {
                  width: ${SELECTED_PRINTER.widthMm}mm;
                }
              }
            </style>
          </head>
          <body>
            <img src="${canvas.toDataURL('image/png')}" />
            <script>
              window.onload = function() {
                const mediaQueryList = window.matchMedia('print');
                mediaQueryList.addListener(function(mql) {
                  if (!mql.matches) {
                    window.close();
                  }
                });
                window.print();
              };
            </script>
          </body>
        </html>
      `);

      printWindow.document.close();
    } catch (error) {}
  };

  const handleExportPDF = async () => {
    try {
      const pdf = await generatePDF();
      if (pdf) {
        const fileName = `cierre-caja-${format(closingDate, 'dd-MM-yyyy')}`;
        pdf.save(`${fileName}.pdf`);
      }
    } catch (error) {}
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleClose();
        }
      }}
    >
      <DialogContent className='w-[600px] print:shadow-none'>
        <DialogHeader className='flex flex-row items-center justify-between'>
          <DialogTitle className='text-xl font-bold'>
            Comprobante de Cierre de Caja
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <div
            ref={receiptRef}
            className='receipt-content mx-auto'
            style={{
              width: `${RECEIPT_CONFIG.width}px`,
              padding: RECEIPT_CONFIG.padding,
              backgroundColor: '#ffffff',
              color: '#000000',
              fontFamily: 'Arial, sans-serif',
            }}
          >
            <div
              style={{
                textAlign: 'center',
                marginBottom: THERMAL_PRINTER_CONFIG.spacing.marginBottom,
                borderBottom: THERMAL_PRINTER_CONFIG.borders.dashed,
                paddingBottom: THERMAL_PRINTER_CONFIG.spacing.padding,
              }}
            >
              <h2
                style={{
                  fontSize: RECEIPT_CONFIG.fontSize.title,
                  fontWeight: 'bold',
                  marginBottom: '8px',
                }}
              >
                COMPROBANTE DE CIERRE
                {forcedClose && ' (FORZADO)'}
              </h2>
              <p style={{ fontSize: RECEIPT_CONFIG.fontSize.normal }}>
                {format(closingDate, 'PPP', { locale: es })}
              </p>
              <p style={{ fontSize: RECEIPT_CONFIG.fontSize.normal }}>
                {format(closingDate, 'HH:mm')}
              </p>
            </div>

            <div
              style={{
                fontSize: RECEIPT_CONFIG.fontSize.normal,
                marginBottom: '16px',
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Cajero:</span>
                <span>{currentUser.username}</span>
              </div>
            </div>

            <div
              style={{
                borderTop: '1px dashed #000',
                borderBottom: '1px dashed #000',
                padding: '16px 0',
                fontSize: RECEIPT_CONFIG.fontSize.normal,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Saldo Inicial:</span>
                <span>{formatCurrency(summary.initialBalance)}</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Total Ingresos:</span>
                <span style={{ color: '#16a34a' }}>
                  {formatCurrency(summary.totalIncome)}
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Total Egresos:</span>
                <span style={{ color: '#dc2626' }}>
                  {formatCurrency(summary.totalExpense)}
                </span>
              </div>
            </div>

            <div
              style={{
                padding: '16px 0',
                fontSize: RECEIPT_CONFIG.fontSize.normal,
                fontWeight: 'bold',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Saldo Esperado:</span>
                <span>{formatCurrency(summary.currentBalance)}</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Saldo Real:</span>
                <span>{formatCurrency(finalBalance)}</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Diferencia:</span>
                <span style={{ color: difference !== 0 ? '#dc2626' : '#000' }}>
                  {formatCurrency(difference)}
                </span>
              </div>
            </div>

            <div
              style={{
                borderTop: '1px dashed #000',
                paddingTop: '16px',
                fontSize: RECEIPT_CONFIG.fontSize.normal,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Total Movimientos:</span>
                <span>{summary.totalMovements}</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Ingresos:</span>
                <span>{summary.byType.INCOME.count} mov.</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '8px',
                }}
              >
                <span>Egresos:</span>
                <span>{summary.byType.EXPENSE.count} mov.</span>
              </div>
            </div>

            {observations && (
              <div
                style={{
                  marginTop: '16px',
                  padding: '16px 0',
                  borderTop: '1px dashed #000',
                  fontSize: RECEIPT_CONFIG.fontSize.normal,
                }}
              >
                <p style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                  Observaciones:
                </p>
                <p style={{ whiteSpace: 'pre-wrap' }}>{observations}</p>
              </div>
            )}

            <div
              style={{
                marginTop: '24px',
                textAlign: 'center',
                borderTop: '1px dashed #000',
                paddingTop: '16px',
                fontSize: RECEIPT_CONFIG.fontSize.normal,
              }}
            >
              <p>- - - Fin del Comprobante - - -</p>
            </div>
          </div>

          <div className='flex justify-end gap-2 print:hidden'>
            <Button onClick={handlePrint} variant='outline' className='gap-2'>
              <Printer className='h-4 w-4' />
              {directPrint ? 'Imprimir Directo' : 'Imprimir'}
            </Button>
            <Button
              onClick={handleExportPDF}
              variant='outline'
              className='gap-2'
            >
              <FileDown className='h-4 w-4' />
              Exportar PDF
            </Button>
            <Button onClick={handleClose} variant='outline' className='gap-2'>
              <X className='h-4 w-4' />
              Cerrar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

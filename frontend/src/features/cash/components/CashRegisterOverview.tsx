import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { PlusCircle, MinusCircle, Download } from 'lucide-react';
import { CashRegisterSummary } from './CashRegisterSummary';
import { CashMovementsTable } from './CashMovementsTable';
import {
  CashMovementsFilters,
  CashRegisterOverviewProps,
} from '@/features/cash';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { getImageUrl } from '@/lib/utils/image';
import { useToast } from '@/hooks/useToast';
import { cashService } from '@/features/cash/services/cash.service';

export function CashRegisterOverview({
  currentRegister,
  movements,
  isLoading,
  filters,
  pagination,
  permissions,
  actions,
  summary,
  showFilters = true,
  showPagination = true,
}: CashRegisterOverviewProps) {
  const { toast } = useToast();

  if (!currentRegister) {
    return (
      <Card className='p-4 sm:p-6'>
        <div className='text-center text-muted-foreground'>
          <p className='text-lg font-medium'>No hay una caja abierta</p>
          <p className='text-sm'>
            Abre una caja para comenzar a registrar movimientos
          </p>
        </div>
      </Card>
    );
  }

  const handleExportPDF = async () => {
    if (!movements.length) {
      toast({
        title: 'No hay movimientos para exportar',
        variant: 'destructive',
      });
      return;
    }

    const loadingToast = toast({
      title: 'Generando PDF',
      description: 'Por favor espere...',
      duration: null,
    });

    const publicSettings = useSettingsStore.getState().publicSettings;

    try {
      const reportContainer = document.createElement('div');
      reportContainer.style.width = '595px'; // equal to A4
      reportContainer.style.padding = '15px';
      reportContainer.style.backgroundColor = '#ffffff';
      reportContainer.style.color = '#000000';

      reportContainer.innerHTML = `
        <div style="font-family: Arial, sans-serif; color: #000000;">
          <div style="text-align: center; margin-bottom: 15px;">
            ${
              publicSettings?.logo
                ? `
              <img 
                src="${getImageUrl(publicSettings.logo)}" 
                alt="Logo" 
                style="height: 30px; margin-bottom: 6px;"
                onerror="this.src='/placeholder-logo.png';"
              />
            `
                : ''
            }
            <h1 style="font-size: 14px; margin-bottom: 4px; color: #000000;">
              ${publicSettings?.systemName || 'Reporte de Caja'}
            </h1>
            <p style="font-size: 10px; color: #000000;">Fecha de Apertura: ${format(
              new Date(currentRegister.openingDate),
              'dd/MM/yyyy HH:mm'
            )}</p>
          </div>

          <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
            <h2 style="font-size: 12px; margin-bottom: 8px; color: #000000;">Resumen</h2>
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 10px;">
              <tr>
                <td style="padding: 4px; border: 1px solid #ddd; color: #000000; font-size: 10px;">Saldo Inicial:</td>
                <td style="padding: 4px; border: 1px solid #ddd; text-align: right; color: #000000; font-size: 10px;">${formatCurrency(
                  currentRegister.initialBalance
                )}</td>
              </tr>
              <tr>
                <td style="padding: 4px; border: 1px solid #ddd; color: #000000; font-size: 10px;">Total Ingresos:</td>
                <td style="padding: 4px; border: 1px solid #ddd; text-align: right; color: #16a34a; font-size: 10px;">${formatCurrency(
                  summary?.totalIncome || 0
                )}</td>
              </tr>
              <tr>
                <td style="padding: 4px; border: 1px solid #ddd; color: #000000; font-size: 10px;">Total Egresos:</td>
                <td style="padding: 4px; border: 1px solid #ddd; text-align: right; color: #dc2626; font-size: 10px;">${formatCurrency(
                  summary?.totalExpense || 0
                )}</td>
              </tr>
              <tr>
                <td style="padding: 4px; border: 1px solid #ddd; color: #000000; font-size: 10px;">Saldo Actual:</td>
                <td style="padding: 4px; border: 1px solid #ddd; text-align: right; font-weight: bold; color: #000000; font-size: 10px;">${formatCurrency(
                  summary?.currentBalance || 0
                )}</td>
              </tr>
            </table>
          </div>

          <div style="margin-bottom: 15px;">
            <h2 style="font-size: 12px; margin-bottom: 8px; color: #000000;">Detalle de Movimientos</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background-color: #f3f4f6;">
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: left; color: #000000; font-size: 9px;">Fecha</th>
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: left; color: #000000; font-size: 9px;">Tipo</th>
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: left; color: #000000; font-size: 9px;">Concepto</th>
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: right; color: #000000; font-size: 9px;">Monto</th>
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: left; color: #000000; font-size: 9px;">Usuario</th>
                  <th style="padding: 4px; border: 1px solid #ddd; text-align: left; color: #000000; font-size: 9px;">Referencia</th>
                </tr>
              </thead>
              <tbody>
                ${movements
                  .map(
                    (movement) => `
                  <tr style="color: #000000;">
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: left; font-size: 9px;">${format(
                      new Date(movement.createdAt),
                      'dd/MM/yyyy HH:mm'
                    )}</td>
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: left; font-size: 9px;">${
                      movement.type === 'INCOME' ? 'Ingreso' : 'Egreso'
                    }</td>
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: left; font-size: 9px;">${
                      movement.concept
                    }</td>
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: right; font-size: 9px; ${
                      movement.type === 'INCOME'
                        ? 'color: #16a34a;'
                        : 'color: #dc2626;'
                    }">${formatCurrency(movement.amount)}</td>
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: left; font-size: 9px;">${
                      movement.createdBy?.username || '-'
                    }</td>
                    <td style="padding: 4px; border: 1px solid #ddd; text-align: left; font-size: 9px;">${
                      movement.reference || '-'
                    }</td>
                  </tr>
                `
                  )
                  .join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

      document.body.appendChild(reportContainer);

      const canvas = await html2canvas(reportContainer, {
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff',
        windowWidth: 595,
      });

      document.body.removeChild(reportContainer);

      // Configuration for A4 (210mm x 297mm)
      const pageWidth = 210;
      const pageHeight = 297;
      const margins = {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      };

      // Calculate effective dimensions
      const effectivePageWidth = pageWidth - (margins.left + margins.right);
      const effectivePageHeight = pageHeight - (margins.top + margins.bottom);

      // Calculate proportional height while maintaining aspect ratio
      const aspectRatio = canvas.height / canvas.width;
      const imgWidth = effectivePageWidth;
      const imgHeight = effectivePageWidth * aspectRatio;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // Calculate the number of pages needed
      const totalPages = Math.ceil(imgHeight / effectivePageHeight);
      let heightLeft = imgHeight;
      let position = 0;

      // Generate pages
      for (let i = 0; i < totalPages; i++) {
        if (i > 0) {
          pdf.addPage();
        }

        pdf.addImage(
          canvas.toDataURL('image/png'),
          'PNG',
          margins.left, // X
          margins.top - position, // Y
          imgWidth, // Width
          imgHeight, // Height total of image
          '', // Alias
          'FAST' // Compression
        );

        heightLeft -= effectivePageHeight;
        position += effectivePageHeight;
      }

      pdf.save(`reporte-caja-${format(new Date(), 'dd-MM-yyyy')}.pdf`);

      loadingToast.dismiss();
      toast({
        title: 'PDF generado',
        description: 'El PDF ha sido generado exitosamente',
      });
    } catch (error) {
      loadingToast.dismiss();
      toast({
        title: 'Error al generar PDF',
        description: 'Por favor intente nuevamente',
        variant: 'destructive',
      });
    }
  };

  const handleExportExcel = async () => {
    try {
      const loadingToast = toast({
        title: 'Generando reporte',
        description: 'Por favor espere...',
        duration: null,
      });

      const { movements, register, summary } =
        await cashService.exportCashMovements(currentRegister._id);

      if (!movements.length) {
        toast({
          title: 'No hay movimientos para exportar',
          variant: 'destructive',
        });
        loadingToast.dismiss();
        return;
      }

      const publicSettings = useSettingsStore.getState().publicSettings;

      // Prepare the header data
      const headerData = [
        [publicSettings?.systemName || 'Reporte de Caja'],
        [
          `Fecha de Apertura: ${format(
            new Date(register.openingDate),
            'dd/MM/yyyy HH:mm'
          )}`,
        ],
        [], // Blank line
        ['Resumen'],
        ['Saldo Inicial', formatCurrency(register.initialBalance)],
        ['Total Ingresos', formatCurrency(summary?.totalIncome || 0)],
        ['Total Egresos', formatCurrency(summary?.totalExpense || 0)],
        ['Saldo Actual', formatCurrency(summary?.currentBalance || 0)],
        [], // Blank line
        ['Detalle de Movimientos'],
        ['Fecha', 'Tipo', 'Concepto', 'Monto', 'Usuario', 'Referencia'],
      ];

      // Prepare the movement data
      const movementsData = movements.map((movement) => [
        format(new Date(movement.createdAt), 'dd/MM/yyyy HH:mm'),
        movement.type === 'INCOME' ? 'Ingreso' : 'Egreso',
        movement.concept,
        formatCurrency(movement.amount),
        movement.createdBy?.username || '-',
        movement.reference || '-',
      ]);

      // Combine and export
      const wsData = [...headerData, ...movementsData];
      const ws = XLSX.utils.aoa_to_sheet(wsData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Reporte');

      // Adjust styles
      ws['!cols'] = [
        { wch: 20 }, // Fecha
        { wch: 10 }, // Tipo
        { wch: 30 }, // Concepto
        { wch: 15 }, // Monto
        { wch: 15 }, // Usuario
        { wch: 20 }, // Referencia
      ];

      // Save to file
      XLSX.writeFile(
        wb,
        `reporte-caja-${format(new Date(), 'dd-MM-yyyy')}.xlsx`
      );

      loadingToast.dismiss();
      toast({
        title: 'Reporte generado',
        description: 'El reprote ha sido generado exitosamente',
      });
    } catch (error) {
      toast({
        title: 'Error al generar el reporte',
        description: 'Por favor intente nuevamente',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='space-y-4 sm:space-y-6'>
      <Card className='p-4 sm:p-6'>
        <div className='mb-4 sm:mb-6 flex justify-between items-center'>
          <div>
            <h2 className='text-lg sm:text-xl font-semibold'>Estado de Caja</h2>
            <div className='text-xs sm:text-sm text-muted-foreground'>
              <p className='break-words'>
                Apertura:{' '}
                {new Date(currentRegister.openingDate).toLocaleString('es-ES', {
                  dateStyle: 'medium',
                  timeStyle: 'short',
                })}
              </p>
            </div>
          </div>
        </div>

        <CashRegisterSummary summary={summary} isLoading={isLoading} />
      </Card>

      <Card className='p-4 sm:p-6'>
        <div className='space-y-4 sm:space-y-6'>
          <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4'>
            <div>
              <h2 className='text-lg sm:text-xl font-semibold'>Movimientos</h2>
              <p className='text-xs sm:text-sm text-muted-foreground'>
                Gestiona los movimientos de caja
              </p>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 w-full sm:w-auto'>
              {permissions.canCreate && showFilters && (
                <>
                  <Button
                    onClick={() => actions.onCreateMovement('INCOME')}
                    variant='default'
                    className='w-full sm:w-[120px] justify-center'
                    size='sm'
                  >
                    <PlusCircle className='w-4 h-4 mr-2' />
                    <span>Ingreso</span>
                  </Button>
                  <Button
                    onClick={() => actions.onCreateMovement('EXPENSE')}
                    variant='destructive'
                    className='w-full sm:w-[120px] justify-center'
                    size='sm'
                  >
                    <MinusCircle className='w-4 h-4 mr-2' />
                    <span>Egreso</span>
                  </Button>
                </>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='outline'
                    size='sm'
                    className='w-full sm:w-[120px]'
                  >
                    <Download className='w-4 h-4 mr-2' />
                    Exportar
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={handleExportPDF}>
                    Exportar PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExportExcel}>
                    Exportar Excel
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {showFilters && (
            <div className='bg-muted/30 p-3 sm:p-4 rounded-lg'>
              <CashMovementsFilters {...filters} />
            </div>
          )}

          {movements.length === 0 && !isLoading ? (
            <div className='flex flex-col items-center justify-center py-8 sm:py-12 bg-muted/10 rounded-lg'>
              <div className='text-center text-muted-foreground px-4'>
                <p className='text-base sm:text-lg font-medium mb-1'>
                  No hay movimientos registrados
                </p>
                <p className='text-xs sm:text-sm'>
                  Utiliza los botones de Ingreso o Egreso para registrar
                  movimientos
                </p>
              </div>
            </div>
          ) : (
            <div className='rounded-md border bg-card overflow-x-auto'>
              <CashMovementsTable
                movements={movements}
                isLoading={isLoading}
                pagination={{
                  currentPage: pagination.currentPage,
                  totalPages: pagination.totalPages,
                  totalRecords: pagination.totalRecords,
                  onPageChange: pagination.onPageChange,
                  onLimitChange: pagination.onLimitChange,
                  limit: pagination.limit,
                }}
                permissions={{
                  canManage: permissions.canManage,
                }}
                onEdit={actions.onEdit}
                onDelete={actions.onDelete}
                showPagination={showPagination}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { CASH_REGISTER_STATUS } from '@/features/cash/constants/cash.constants';
import type { CashRegisterHistoryTableProps } from '../types/cash.types';
import { formatCurrency } from '@/lib/utils/format';

const tableVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 },
};

export function CashRegisterHistoryTable({
  registers,
  isLoading,
  pagination,
  permissions,
  onViewDetails,
  onDeleteRegister,
}: CashRegisterHistoryTableProps) {
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return '---';
    try {
      const date = new Date(dateString);
      return format(date, 'dd/MM/yyyy HH:mm', { locale: es });
    } catch (error) {
      return '---';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case CASH_REGISTER_STATUS.OPEN:
        return <Badge variant='success'>Abierta</Badge>;
      case CASH_REGISTER_STATUS.CLOSED:
        return <Badge variant='default'>Cerrada</Badge>;
      default:
        return <Badge variant='secondary'>{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <motion.div initial='hidden' animate='visible' variants={tableVariants}>
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow className='bg-muted/50'>
                <TableHead>Fecha Apertura</TableHead>
                <TableHead>Fecha Cierre</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead className='text-right'>Saldo Inicial</TableHead>
                <TableHead className='text-right'>Saldo Final</TableHead>
                <TableHead>Abierto por</TableHead>
                <TableHead>Cerrado por</TableHead>
                <TableHead className='text-right'>Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i} className='animate-pulse'>
                  <TableCell>
                    <Skeleton className='h-4 w-[120px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[120px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[80px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px] ml-auto' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px] ml-auto' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[100px]' />
                  </TableCell>
                  <TableCell>
                    <Skeleton className='h-4 w-[40px] ml-auto' />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div initial='hidden' animate='visible' variants={tableVariants}>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow className='bg-muted/50'>
              <TableHead className='w-[180px]'>Fecha Apertura</TableHead>
              <TableHead className='w-[180px]'>Fecha Cierre</TableHead>
              <TableHead className='w-[120px]'>Estado</TableHead>
              <TableHead className='w-[150px] text-right'>
                Saldo Inicial
              </TableHead>
              <TableHead className='w-[150px] text-right'>
                Saldo Final
              </TableHead>
              <TableHead className='w-[150px]'>Abierto por</TableHead>
              <TableHead className='w-[150px]'>Cerrado por</TableHead>
              <TableHead className='w-[100px] text-right'>Acciones</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {registers.map((register) => (
              <motion.tr
                key={register._id}
                className='hover:bg-muted/50 border-b transition-colors'
                variants={rowVariants}
                initial='hidden'
                animate='visible'
              >
                <TableCell className='font-medium whitespace-nowrap'>
                  {formatDate(register.openingDate)}
                </TableCell>
                <TableCell className='whitespace-nowrap'>
                  {formatDate(register.closingDate)}
                </TableCell>
                <TableCell>{getStatusBadge(register.status)}</TableCell>
                <TableCell className='text-right font-medium'>
                  {formatCurrency(register.initialBalance)}
                </TableCell>
                <TableCell className='text-right font-medium'>
                  {register.finalBalance
                    ? formatCurrency(register.finalBalance)
                    : '---'}
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className='max-w-[150px] truncate text-muted-foreground'>
                          {register.openedBy?.username || '---'}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>ID: {register.openedBy?._id || '---'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className='max-w-[150px] truncate text-muted-foreground'>
                          {register.closedBy?.username || '---'}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>ID: {register.closedBy?._id || '---'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end gap-1'>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant='ghost'
                            size='icon'
                            onClick={() => onViewDetails(register._id)}
                            disabled={!permissions.canList}
                          >
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Ver detalles</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {permissions.canManage && onDeleteRegister && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => onDeleteRegister(register)}
                            >
                              <Trash2 className='h-4 w-4 text-destructive' />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Eliminar registro</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </TableCell>
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className='flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 gap-4'
        >
          <div className='flex flex-col sm:flex-row sm:items-center gap-4'>
            <div className='text-sm text-muted-foreground'>
              Total: {pagination.totalRecords} registros
            </div>
            <div className='flex items-center gap-2'>
              <span className='text-sm text-muted-foreground'>Mostrar:</span>
              <select
                className='h-8 w-[70px] rounded-md border border-input bg-background px-2 text-sm'
                value={pagination.limit}
                onChange={(e) =>
                  pagination.onLimitChange(Number(e.target.value))
                }
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
          <div className='flex items-center gap-2 justify-between sm:justify-end'>
            <Button
              variant='outline'
              size='icon'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage - 1)
              }
              disabled={pagination.currentPage === 1}
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <span className='text-sm whitespace-nowrap'>
              Página {pagination.currentPage} de {pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='icon'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage + 1)
              }
              disabled={pagination.currentPage === pagination.totalPages}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

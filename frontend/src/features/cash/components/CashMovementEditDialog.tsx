import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';
import { formatCurrency } from '@/lib/utils/format';
import {
  type CashMovementEditDialogProps,
  type UpdateCashMovementDto,
  updateCashMovementSchema,
} from '@/features/cash';

export function CashMovementEditDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  movement,
}: CashMovementEditDialogProps) {
  const form = useForm<UpdateCashMovementDto>({
    resolver: zodResolver(updateCashMovementSchema),
    defaultValues: {
      concept: movement?.concept || '',
      reference: movement?.reference || '',
      observations: movement?.observations || '',
    },
  });

  useEffect(() => {
    if (movement) {
      form.reset({
        concept: movement.concept,
        reference: movement.reference || '',
        observations: movement.observations || '',
      });
    }
  }, [movement, form]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Edit2 className='w-5 h-5 text-primary' />
            Editar Movimiento
          </DialogTitle>
        </DialogHeader>

        <div className='flex flex-col gap-4 py-4'>
          <div className='bg-muted/30 p-4 rounded-lg space-y-2'>
            <div className='grid grid-cols-2 gap-4'>
              <div>
                <p className='text-sm text-muted-foreground'>Tipo</p>
                <p className='font-medium'>
                  {movement?.type === 'INCOME' ? 'Ingreso' : 'Egreso'}
                </p>
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>Monto</p>
                <p className='font-medium'>
                  {movement?.amount ? formatCurrency(movement.amount) : 'N/A'}
                </p>
              </div>
            </div>
            <div>
              <p className='text-sm text-muted-foreground'>Creado por</p>
              <p className='font-medium'>{movement?.createdBy.username}</p>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <FormField
                control={form.control}
                name='concept'
                render={({ field }) => (
                  <FormItem className='grid gap-2'>
                    <FormLabelWithTooltip
                      label='Concepto'
                      tooltip='Ingrese el concepto del movimiento'
                      required
                    />
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Concepto del movimiento'
                        className='w-full'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='reference'
                render={({ field }) => (
                  <FormItem className='grid gap-2'>
                    <FormLabelWithTooltip
                      label='Referencia'
                      tooltip='Número de referencia o comprobante (opcional)'
                    />
                    <FormControl>
                      <Input
                        {...field}
                        placeholder='Referencia (opcional)'
                        className='w-full'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='observations'
                render={({ field }) => (
                  <FormItem className='grid gap-2'>
                    <FormLabelWithTooltip
                      label='Observaciones'
                      tooltip='Notas adicionales sobre el movimiento'
                    />
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder='Observaciones adicionales'
                        className='resize-none min-h-[100px] w-full'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='flex justify-end gap-4 pt-4 border-t'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button type='submit' disabled={isLoading}>
                  {isLoading ? 'Guardando...' : 'Guardar Cambios'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

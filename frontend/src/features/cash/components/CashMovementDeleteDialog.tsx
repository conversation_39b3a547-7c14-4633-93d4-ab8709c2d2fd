import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { formatCurrency } from '@/lib/utils/format';
import { CashMovementDeleteDialogProps } from '@/features/cash';

export function CashMovementDeleteDialog({
  open,
  onOpenChange,
  onConfirm,
  isLoading,
  movement,
}: CashMovementDeleteDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>¿Eliminar movimiento?</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div>
              <span>¿Está seguro que desea eliminar este movimiento?</span>
              <div className='mt-4 p-4 bg-muted/30 rounded-lg space-y-2'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <span className='text-sm font-medium block'>Tipo</span>
                    <span>
                      {movement?.type === 'INCOME' ? 'Ingreso' : 'Egreso'}
                    </span>
                  </div>
                  <div>
                    <span className='text-sm font-medium block'>Monto</span>
                    <span>
                      {movement?.amount
                        ? formatCurrency(movement.amount)
                        : 'N/A'}
                    </span>
                  </div>
                </div>
                <div>
                  <span className='text-sm font-medium block'>Concepto</span>
                  <span>{movement?.concept}</span>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className='bg-destructive hover:bg-destructive/90 text-white'
          >
            {isLoading ? 'Eliminando...' : 'Eliminar'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

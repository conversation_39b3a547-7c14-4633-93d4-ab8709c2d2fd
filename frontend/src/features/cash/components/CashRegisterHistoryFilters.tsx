import { Calendar, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { CASH_REGISTER_STATUS } from '../constants/cash.constants';
import type { CashRegisterHistoryFiltersProps } from '../types/cash.types';

export function CashRegisterHistoryFilters({
  startDate,
  endDate,
  status,
  onStartDateChange,
  onEndDateChange,
  onStatusChange,
  onReset,
}: CashRegisterHistoryFiltersProps) {
  return (
    <TooltipProvider>
      <div className='bg-card border rounded-lg p-4 space-y-4'>
        <div className='flex flex-col lg:flex-row gap-4'>
          {/* Filtros de fecha */}
          <div className='flex flex-col sm:flex-row gap-2 lg:w-[500px]'>
            <DatePicker
              placeholder='Fecha inicial'
              value={startDate}
              onChange={onStartDateChange}
              className='w-full sm:flex-1'
            />
            <DatePicker
              placeholder='Fecha final'
              value={endDate}
              onChange={onEndDateChange}
              className='w-full sm:flex-1'
            />
          </div>

          {/* Estado */}
          <div className='flex gap-2'>
            <Select value={status} onValueChange={onStatusChange}>
              <SelectTrigger className='w-[200px]'>
                <SelectValue placeholder='Estado' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='ALL'>Todos</SelectItem>
                <SelectItem value={CASH_REGISTER_STATUS.OPEN}>
                  Abierta
                </SelectItem>
                <SelectItem value={CASH_REGISTER_STATUS.CLOSED}>
                  Cerrada
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Botón de reset para móvil */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='outline'
                  onClick={onReset}
                  size='icon'
                  className='sm:hidden h-10 w-10'
                >
                  <RotateCcw className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Limpiar filtros</TooltipContent>
            </Tooltip>

            {/* Botón de reset para desktop */}
            <Button
              variant='outline'
              onClick={onReset}
              className='hidden sm:flex items-center gap-2'
            >
              <RotateCcw className='h-4 w-4' />
              <span>Limpiar</span>
            </Button>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}

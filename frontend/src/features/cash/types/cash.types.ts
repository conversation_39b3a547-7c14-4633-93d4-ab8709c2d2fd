import {
  CASH_MOVEMENT_TYPES,
  CloseCashRegisterSchema,
  OpenCashRegisterSchema,
} from '@/features/cash';

export enum CashRegisterStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
}

export interface CashRegister {
  _id: string;
  openingDate: string;
  closingDate?: string;
  initialBalance: number;
  finalBalance?: number;
  status: CashRegisterStatus;
  openedBy: {
    _id: string;
    username: string;
  };
  closedBy?: {
    _id: string;
    username: string;
  };
  observations?: string;
  createdAt: string;
  updatedAt: string;
}

export type MovementType =
  (typeof CASH_MOVEMENT_TYPES)[keyof typeof CASH_MOVEMENT_TYPES];

export interface CashMovement {
  _id: string;
  cashRegister: string;
  type: MovementType;
  amount: number;
  concept: string;
  reference?: string;
  observations?: string;
  createdBy: {
    _id: string;
    username: string;
  };
  updatedBy?: {
    _id: string;
    username: string;
  };
  createdAt: string | Date;
  updatedAt: string | Date;
}

export interface CreateCashMovementDto {
  type: MovementType;
  amount: number;
  concept: string;
  reference?: string;
  observations?: string;
}

export interface UpdateCashMovementDto {
  type?: MovementType;
  amount?: number;
  concept?: string;
  reference?: string;
  observations?: string;
}

export interface OpenCashRegisterDto {
  initialBalance: number;
  observations?: string;
}

export interface CloseCashRegisterDto {
  finalBalance: number;
  observations?: string;
  forcedClose?: boolean;
}
export interface BalanceValidation {
  expectedBalance: number;
  providedBalance: number;
  difference: number;
  isBalanced: boolean;
}

export interface CashRegisterSummary {
  registerId: string;
  openingDate: Date;
  closingDate?: Date;
  initialBalance: number;
  finalBalance?: number;
  status: 'open' | 'closed';
  currentBalance: number;
  totalIncome: number;
  totalExpense: number;
  totalMovements: number;
  byType: {
    [CASH_MOVEMENT_TYPES.INCOME]: { count: number; total: number };
    [CASH_MOVEMENT_TYPES.EXPENSE]: { count: number; total: number };
  };
  incomeCount: number;
  expenseCount: number;
}

export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  onPageChange: (page: number) => void;
}

export interface CashRegisterFilters {
  search: string;
  type: string | null;
  onSearchChange: (search: string) => void;
  onTypeChange: (type: string | null) => void;
  onReset: () => void;
}

export interface CashMovementCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateCashMovementDto) => Promise<void>;
  isLoading: boolean;
  type: MovementType;
}

export interface CashMovementDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
  movement: CashMovement | null;
}

export interface CashMovementEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: UpdateCashMovementDto) => Promise<void>;
  isLoading: boolean;
  movement: CashMovement | null;
}

export interface CashRegisterOpenDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: OpenCashRegisterSchema) => Promise<void>;
  isLoading: boolean;
}

export interface CashMovementsFiltersProps {
  search: string;
  type: string;
  onSearchChange: (value: string) => void;
  onTypeChange: (value: string) => void;
  onReset: () => void;
}

export interface CashMovementsTableProps {
  movements: CashMovement[];
  isLoading: boolean;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
    limit: number;
  };
  permissions: {
    canManage: boolean;
  };
  onEdit: (movement: CashMovement) => void;
  onDelete: (movement: CashMovement) => void;
  showPagination: boolean;
}

export interface CashRegisterCloseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (
    data: CloseCashRegisterSchema,
    forceClose?: boolean
  ) => Promise<void>;
  isLoading: boolean;
  expectedBalance: number;
  summary: CashRegisterSummary;
  currentUser: { username: string };
}

export interface CashRegisterCloseReceiptProps {
  open: boolean;
  onClose: () => void;
  summary: CashRegisterSummary;
  finalBalance: number;
  observations?: string;
  forcedClose?: boolean;
  currentUser: { username: string };
  closingDate: Date;
  directPrint?: boolean;
  onAfterClose?: () => void;
}

export interface CashRegisterHeaderProps {
  title: string;
  description: string;
  onOpenRegister: () => void;
  onCloseRegister: () => void;
  canManage: boolean;
  isRegisterOpen: boolean;
}

export interface CashRegisterOverviewProps {
  currentRegister: CashRegister | null;
  movements: CashMovement[];
  isLoading: boolean;
  filters: CashRegisterFilters;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
    limit: number;
  };
  permissions: {
    canCreate: boolean;
    canManage: boolean;
  };
  actions: {
    onCreateMovement: (type: MovementType) => void;
    onEdit: (movement: CashMovement) => void;
    onDelete: (movement: CashMovement) => void;
  };
  summary: CashRegisterSummary | null;
  showFilters: boolean;
  showPagination: boolean;
}

export interface CashRegisterSummaryProps {
  summary: CashRegisterSummary | null;
  isLoading: boolean;
}

export interface CashState {
  currentRegister: CashRegister | null;
  movements: CashMovement[];
  summary: CashRegisterSummary | null;
  balanceValidation: BalanceValidation | null;
  isLoading: boolean;
  error: string | null;

  getCurrentRegister: () => Promise<void>;
  openRegister: (
    initialBalance: number,
    observations?: string
  ) => Promise<void>;
  closeRegister: (
    id: string,
    finalBalance: number,
    observations?: string
  ) => Promise<void>;
  validateBalance: (registerId: string, finalBalance: number) => Promise<void>;
  getRegisterSummary: (registerId: string) => Promise<void>;

  getMovements: (
    registerId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      type?: string;
      startDate?: string;
      endDate?: string;
    }
  ) => Promise<void>;
  createMovement: (
    registerId: string,
    data: CreateCashMovementDto
  ) => Promise<void>;
  updateMovement: (
    id: string,
    data: { reference?: string; observations?: string }
  ) => Promise<void>;
  deleteMovement: (id: string) => Promise<void>;

  clearError: () => void;
  reset: () => void;
}

export interface CashRegisterHistoryFiltersProps {
  startDate: Date | null;
  endDate: Date | null;
  status: string;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  onStatusChange: (status: string) => void;
  onReset: () => void;
}

export interface CashRegisterHistoryTableProps {
  registers: CashRegister[];
  isLoading: boolean;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
    limit: number;
  };
  permissions: {
    canList: boolean;
    canManage: boolean;
  };
  onViewDetails: (registerId: string) => void;
  onDeleteRegister?: (register: CashRegister) => void;
}

export interface CashRegisterHistoryResponse {
  registers: CashRegister[];
  totalPages: number;
  totalRecords: number;
}

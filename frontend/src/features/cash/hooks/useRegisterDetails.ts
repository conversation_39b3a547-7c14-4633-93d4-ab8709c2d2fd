import { useQuery } from '@tanstack/react-query';
import { cashService } from '../services/cash.service';
import { useToast } from '@/hooks/useToast';
import { useQueryClient } from '@tanstack/react-query';

export function useRegisterDetails(id: string) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: registerData, isLoading } = useQuery({
    queryKey: ['register', id],
    queryFn: () => cashService.getRegisterDetails(id),
  });

  return {
    register: registerData?.register,
    movements: registerData?.movements || [],
    summary: registerData?.summary,
    isLoading,
  };
}

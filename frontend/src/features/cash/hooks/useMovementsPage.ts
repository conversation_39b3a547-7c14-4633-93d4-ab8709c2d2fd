import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/useToast';
import {
  cashService,
  type CashMovement,
  type UpdateCashMovementDto,
} from '@/features/cash';

export function useMovementsPage(registerId: string) {
  const queryClient = useQueryClient();
  const [search, setSearch] = useState('');
  const [type, setType] = useState('ALL');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const [editDialog, setEditDialog] = useState({
    isOpen: false,
    isLoading: false,
    movement: null as CashMovement | null,
  });

  const { data, isLoading } = useQuery({
    queryKey: ['cashMovements', registerId, { search, type, page, limit }],
    queryFn: () =>
      cashService.getCashMovements(registerId, {
        search,
        type,
        page,
        limit,
      }),
    enabled: !!registerId,
  });

  const refresh = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['cashMovements'] });
  }, [queryClient]);

  const handleEditDialogOpen = useCallback((movement: CashMovement) => {
    setEditDialog({
      isOpen: true,
      isLoading: false,
      movement,
    });
  }, []);

  const handleEditDialogClose = useCallback(() => {
    setEditDialog({
      isOpen: false,
      isLoading: false,
      movement: null,
    });
  }, []);

  const handleEdit = async (data: UpdateCashMovementDto) => {
    if (!editDialog.movement?._id) return;

    try {
      setEditDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.updateCashMovement(editDialog.movement._id, data);
      await queryClient.invalidateQueries({ queryKey: ['cashMovements'] });
      toast({
        title: 'Éxito',
        description: 'Movimiento actualizado correctamente',
      });
      handleEditDialogClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al actualizar el movimiento',
        variant: 'destructive',
      });
    } finally {
      setEditDialog((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const handleDelete = async (movement: CashMovement) => {
    try {
      await cashService.deleteCashMovement(movement._id);
      queryClient.invalidateQueries({ queryKey: ['cashMovements'] });
      toast({
        title: 'Éxito',
        description: 'Movimiento eliminado correctamente',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al eliminar el movimiento',
        variant: 'destructive',
      });
    }
  };

  const handleReset = useCallback(() => {
    setSearch('');
    setType('ALL');
    setPage(1);
  }, []);

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
  };

  return {
    movements: data?.movements || [],
    isLoading,
    filters: {
      search,
      type,
      onSearchChange: setSearch,
      onTypeChange: setType,
      onReset: handleReset,
    },
    pagination: {
      currentPage: page,
      totalPages: data?.totalPages || 0,
      totalRecords: data?.totalRecords || 0,
      onPageChange: setPage,
      onLimitChange: handleLimitChange,
      limit,
    },
    actions: {
      handlers: {
        onEdit: handleEditDialogOpen,
        onDelete: handleDelete,
      },
      refresh,
    },
    editDialog: {
      isOpen: editDialog.isOpen,
      isLoading: editDialog.isLoading,
      movement: editDialog.movement,
      onOpenChange: (open: boolean) => {
        if (!open) handleEditDialogClose();
        else setEditDialog((prev) => ({ ...prev, isOpen: true }));
      },
      onSubmit: handleEdit,
    },
  };
}

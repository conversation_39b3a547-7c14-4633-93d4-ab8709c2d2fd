import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { format } from 'date-fns';
import { cashService } from '@/features/cash';
import type { CashRegister } from '@/features/cash';

export function useCashHistory() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [status, setStatus] = useState('ALL');
  const [deleteDialog, setDeleteDialog] = useState({
    isOpen: false,
    isLoading: false,
    register: null as CashRegister | null,
  });

  const { data, isLoading } = useQuery({
    queryKey: ['cashRegisters', page, limit, startDate, endDate, status],
    queryFn: () =>
      cashService.getCashRegisters({
        page,
        limit,
        startDate: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
        endDate: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
        status: status === 'ALL' ? undefined : status,
      }),
  });

  const handleReset = useCallback(() => {
    setStartDate(null);
    setEndDate(null);
    setStatus('ALL');
    setPage(1);
  }, []);

  const handleViewDetails = useCallback(
    (registerId: string) => {
      navigate(`/cashier/history/${registerId}`);
    },
    [navigate]
  );

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
  };

  const handleDelete = async (registerId: string) => {
    try {
      setDeleteDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.deleteCashRegister(registerId);
      await queryClient.invalidateQueries({ queryKey: ['cashRegisters'] });
      toast({
        title: 'Éxito',
        description: 'Registro de caja eliminado correctamente',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al eliminar el registro',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialog({ isOpen: false, isLoading: false, register: null });
    }
  };

  const handleDeleteRegister = useCallback((register: CashRegister) => {
    setDeleteDialog((prev) => ({
      ...prev,
      isOpen: true,
      register,
    }));
  }, []);

  // Modificar los setters para resetear la página
  const handleStartDateChange = (date: Date | null) => {
    setStartDate(date);
    setPage(1);
  };

  const handleEndDateChange = (date: Date | null) => {
    setEndDate(date);
    setPage(1);
  };

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
    setPage(1);
  };

  return {
    registers: data?.registers || [],
    isLoading,
    filters: {
      startDate,
      endDate,
      status,
      onStartDateChange: handleStartDateChange,
      onEndDateChange: handleEndDateChange,
      onStatusChange: handleStatusChange,
      onReset: handleReset,
    },
    pagination: {
      currentPage: page,
      totalPages: data?.totalPages || 0,
      totalRecords: data?.totalRecords || 0,
      onPageChange: setPage,
      onLimitChange: handleLimitChange,
      limit,
    },
    actions: {
      viewDetails: handleViewDetails,
      deleteRegister: handleDeleteRegister,
    },
    deleteDialog: {
      isOpen: deleteDialog.isOpen,
      isLoading: deleteDialog.isLoading,
      register: deleteDialog.register,
      onOpenChange: (open: boolean) =>
        setDeleteDialog((prev) => ({ ...prev, isOpen: open })),
      onConfirm: () =>
        deleteDialog.register && handleDelete(deleteDialog.register._id),
    },
  };
}

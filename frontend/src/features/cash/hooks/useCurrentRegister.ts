import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/features/auth';
import { toast } from '@/hooks/useToast';
import {
  CashMovement,
  CreateCashMovementDto,
  UpdateCashMovementDto,
  OpenCashRegisterDto,
  CloseCashRegisterDto,
  CashRegisterSummary,
  MovementType,
  useMovementsPage,
  cashService,
} from '@/features/cash';
import { CASH_MOVEMENT_TYPES } from '../constants/cash.constants';

interface UseCurrentRegisterOptions {
  includeMovements?: boolean;
}

export function useCurrentRegister(options: UseCurrentRegisterOptions = {}) {
  const { includeMovements = true } = options;
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Dialog states
  const [openRegisterDialog, setOpenRegisterDialog] = useState({
    open: false,
    isLoading: false,
  });

  const [closeDialog, setCloseDialog] = useState({
    open: false,
    isLoading: false,
  });

  const [movementDialog, setMovementDialog] = useState({
    open: false,
    isLoading: false,
    type: 'INCOME' as MovementType,
  });

  const [editDialog, setEditDialog] = useState({
    isOpen: false,
    isLoading: false,
    movement: null as CashMovement | null,
  });

  const [deleteDialog, setDeleteDialog] = useState({
    isOpen: false,
    isLoading: false,
    movement: null as CashMovement | null,
  });

  const [receipt, setReceipt] = useState({
    show: false,
    summary: null as CashRegisterSummary | null,
    finalBalance: 0,
    observations: '',
    forcedClose: false,
  });

  // Queries
  const { data: currentRegister, isLoading: isLoadingRegister } = useQuery({
    queryKey: ['currentRegister'],
    queryFn: () => cashService.getCurrentRegister(),
  });

  const { data: summary } = useQuery({
    queryKey: ['registerSummary', currentRegister?._id],
    queryFn: () => cashService.getRegisterSummary(currentRegister!._id),
    enabled: !!currentRegister?._id,
  });

  // Use movements page hook
  const movementsPage = includeMovements
    ? useMovementsPage(currentRegister?._id || '')
    : {
        movements: [],
        filters: {},
        pagination: {},
        actions: { refresh: () => {} },
      };

  // Register handlers
  const handleOpenCurrentRegister = async (data: OpenCashRegisterDto) => {
    try {
      setOpenRegisterDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.openCashRegister(data);
      await queryClient.invalidateQueries({ queryKey: ['currentRegister'] });
      setOpenRegisterDialog({ open: false, isLoading: false });
      toast({
        title: 'Éxito',
        description: 'Caja abierta correctamente',
      });
    } catch (error: any) {
      setOpenRegisterDialog((prev) => ({ ...prev, isLoading: false }));
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  const handleCloseRegister = async (
    data: CloseCashRegisterDto,
    forcedClose?: boolean
  ) => {
    if (!currentRegister?._id) return;

    try {
      await cashService.closeCashRegister(currentRegister._id, data);
      setCloseDialog((prev) => ({ ...prev, open: false }));
      setReceipt({
        show: true,
        summary: summary!,
        finalBalance: data.finalBalance,
        observations: data.observations || '',
        forcedClose: forcedClose || false,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  // Movement handlers
  const handleCreateMovement = async (data: CreateCashMovementDto) => {
    if (!currentRegister?._id) return;

    try {
      setMovementDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.createCashMovement(currentRegister._id, {
        ...data,
        type: data.type, // Ahora esto será 'INCOME' o 'EXPENSE'
      });
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['cashMovements'] }),
        queryClient.invalidateQueries({ queryKey: ['registerSummary'] }),
      ]);
      setMovementDialog((prev) => ({ ...prev, open: false, isLoading: false }));
      toast({
        title: 'Éxito',
        description: 'Movimiento creado correctamente',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al crear el movimiento',
        variant: 'destructive',
      });
      setMovementDialog((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const handleEditMovement = useCallback((movement: CashMovement) => {
    setEditDialog({
      isOpen: true,
      isLoading: false,
      movement,
    });
  }, []);

  const handleDeleteMovement = useCallback((movement: CashMovement) => {
    setDeleteDialog({
      isOpen: true,
      isLoading: false,
      movement,
    });
  }, []);

  const handleEdit = async (data: UpdateCashMovementDto) => {
    if (!editDialog.movement?._id) return;

    try {
      setEditDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.updateCashMovement(editDialog.movement._id, data);
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['cashMovements'] }),
        queryClient.invalidateQueries({ queryKey: ['registerSummary'] }),
      ]);
      setEditDialog({ isOpen: false, isLoading: false, movement: null });
      toast({
        title: 'Éxito',
        description: 'Movimiento actualizado correctamente',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al actualizar el movimiento',
        variant: 'destructive',
      });
      setEditDialog((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const handleDelete = async () => {
    if (!deleteDialog.movement?._id) return;

    try {
      setDeleteDialog((prev) => ({ ...prev, isLoading: true }));
      await cashService.deleteCashMovement(deleteDialog.movement._id);
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['cashMovements'] }),
        queryClient.invalidateQueries({ queryKey: ['registerSummary'] }),
      ]);
      setDeleteDialog({ isOpen: false, isLoading: false, movement: null });
      toast({
        title: 'Éxito',
        description: 'Movimiento eliminado correctamente',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al eliminar el movimiento',
        variant: 'destructive',
      });
      setDeleteDialog((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const handleAfterReceiptClose = useCallback(() => {
    setReceipt({
      show: false,
      summary: null,
      finalBalance: 0,
      observations: '',
      forcedClose: false,
    });
    queryClient.invalidateQueries();
  }, [queryClient]);

  return {
    // Data
    currentRegister,
    summary,
    isLoading: isLoadingRegister,
    movements: movementsPage.movements,

    // Filters and pagination
    filters: movementsPage.filters,
    pagination: movementsPage.pagination,

    // Actions
    actions: {
      openCurrentRegister: () =>
        setOpenRegisterDialog((prev) => ({ ...prev, open: true })),
      closeRegister: () => setCloseDialog((prev) => ({ ...prev, open: true })),
      createMovement: (type: MovementType) => {
        setMovementDialog((prev) => ({ ...prev, type, open: true }));
      },
      editMovement: handleEditMovement,
      deleteMovement: handleDeleteMovement,
      refresh: movementsPage.actions.refresh,
    },

    // Dialogs
    dialogs: {
      currentRegister: {
        open: openRegisterDialog.open,
        onOpenChange: (open: boolean) =>
          setOpenRegisterDialog((prev) => ({ ...prev, open })),
        onSubmit: handleOpenCurrentRegister,
        isLoading: openRegisterDialog.isLoading,
      },
      close: {
        open: closeDialog.open,
        onOpenChange: (open: boolean) =>
          setCloseDialog((prev) => ({ ...prev, open })),
        onSubmit: handleCloseRegister,
        isLoading: closeDialog.isLoading,
        summary,
        currentUser: user,
        expectedBalance: summary?.currentBalance || 0,
      },
      movement: {
        open: movementDialog.open,
        onOpenChange: (open: boolean) =>
          setMovementDialog((prev) => ({ ...prev, open })),
        onSubmit: handleCreateMovement,
        isLoading: movementDialog.isLoading,
        type: movementDialog.type,
      },
      edit: {
        open: editDialog.isOpen,
        onOpenChange: (open: boolean) => {
          if (!open)
            setEditDialog((prev) => ({
              ...prev,
              isOpen: false,
              movement: null,
            }));
          else setEditDialog((prev) => ({ ...prev, isOpen: true }));
        },
        onSubmit: handleEdit,
        isLoading: editDialog.isLoading,
        movement: editDialog.movement,
      },
      delete: {
        open: deleteDialog.isOpen,
        onOpenChange: (open: boolean) => {
          if (!open)
            setDeleteDialog((prev) => ({
              ...prev,
              isOpen: false,
              movement: null,
            }));
          else setDeleteDialog((prev) => ({ ...prev, isOpen: true }));
        },
        onConfirm: handleDelete,
        isLoading: deleteDialog.isLoading,
        movement: deleteDialog.movement,
      },
      receipt: {
        open: receipt.show,
        onClose: handleAfterReceiptClose,
        summary: receipt.summary,
        finalBalance: receipt.finalBalance,
        observations: receipt.observations,
        forcedClose: receipt.forcedClose,
        currentUser: user,
        closingDate: new Date(),
      },
    },
  };
}

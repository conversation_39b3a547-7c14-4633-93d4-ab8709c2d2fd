import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { cashService, type CashState } from '@/features/cash';

export const useCashStore = create<CashState>()(
  devtools((set, get) => ({
    currentRegister: null,
    movements: [],
    summary: null,
    balanceValidation: null,
    isLoading: false,
    error: null,

    getCurrentRegister: async () => {
      try {
        set({ isLoading: true, error: null });
        const register = await cashService.getCurrentRegister();
        set({ currentRegister: register });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    openRegister: async (initialBalance, observations) => {
      try {
        set({ isLoading: true, error: null });
        const register = await cashService.openCashRegister({
          initialBalance,
          observations,
        });
        set({ currentRegister: register });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    closeRegister: async (id, finalBalance, observations) => {
      try {
        set({ isLoading: true, error: null });
        const register = await cashService.closeCashRegister(id, {
          finalBalance,
          observations,
        });
        set({ currentRegister: register });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    validateBalance: async (registerId, finalBalance) => {
      try {
        set({ isLoading: true, error: null });
        const validation = await cashService.validateClosingBalance(
          registerId,
          finalBalance
        );
        set({ balanceValidation: validation });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    getRegisterSummary: async (registerId) => {
      try {
        set({ isLoading: true, error: null });
        const summary = await cashService.getRegisterSummary(registerId);
        set({ summary });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    getMovements: async (registerId, params) => {
      try {
        set({ isLoading: true, error: null });
        const response = await cashService.getCashMovements(registerId, params);
        set({ movements: response.movements });
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    createMovement: async (registerId, data) => {
      try {
        set({ isLoading: true, error: null });
        await cashService.createCashMovement(registerId, data);
        await get().getMovements(registerId);
        await get().getRegisterSummary(registerId);
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    updateMovement: async (id, data) => {
      try {
        set({ isLoading: true, error: null });
        await cashService.updateCashMovement(id, data);
        const { currentRegister } = get();
        if (currentRegister) {
          await get().getMovements(currentRegister._id);
          await get().getRegisterSummary(currentRegister._id);
        }
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    deleteMovement: async (id) => {
      try {
        set({ isLoading: true, error: null });
        await cashService.deleteCashMovement(id);
        const { currentRegister } = get();
        if (currentRegister) {
          await get().getMovements(currentRegister._id);
          await get().getRegisterSummary(currentRegister._id);
        }
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ isLoading: false });
      }
    },

    clearError: () => set({ error: null }),

    reset: () =>
      set({
        currentRegister: null,
        movements: [],
        summary: null,
        balanceValidation: null,
        error: null,
      }),
  }))
);

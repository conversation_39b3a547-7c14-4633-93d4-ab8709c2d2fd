import { api } from '@/lib/api/api';
import type {
  CashRegister,
  CreateCashMovementDto,
  UpdateCashMovementDto,
  OpenCashRegisterDto,
  CloseCashRegisterDto,
  CashRegisterSummary,
  BalanceValidation,
} from '../types/cash.types';

class CashService {
  async getCurrentRegister() {
    const { data } = await api.get<CashRegister>('/cash/current');
    return data;
  }

  async getCashRegisters(params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    status?: string;
    search?: string;
  }) {
    // Formatear las fechas antes de enviar
    const formattedParams = {
      ...params,
      startDate: params?.startDate ? params.startDate.split('T')[0] : undefined,
      endDate: params?.endDate ? params.endDate.split('T')[0] : undefined,
    };

    const { data } = await api.get('/cash/history', {
      params: formattedParams,
    });
    return data;
  }

  async openCashRegister(data: OpenCashRegisterDto) {
    const response = await api.post('/cash/open', data);
    return response.data;
  }

  async closeCashRegister(id: string, data: CloseCashRegisterDto) {
    const response = await api.post(`/cash/${id}/close`, data);
    return response.data;
  }

  async validateClosingBalance(
    registerId: string,
    finalBalance: number
  ): Promise<BalanceValidation> {
    const { data } = await api.post(`/cash/${registerId}/validate-balance`, {
      finalBalance,
    });
    return data;
  }

  async getCashMovements(
    registerId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      type?: string;
      startDate?: string;
      endDate?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const { data } = await api.get(`/cash/${registerId}/movements`, {
      params,
    });
    return data;
  }

  async createCashMovement(registerId: string, data: CreateCashMovementDto) {
    const response = await api.post(`/cash/${registerId}/movements`, data);
    return response.data;
  }

  async updateCashMovement(movementId: string, data: UpdateCashMovementDto) {
    const response = await api.patch(`/cash/movements/${movementId}`, data);
    return response.data;
  }

  async deleteCashMovement(movementId: string) {
    const response = await api.delete(`/cash/movements/${movementId}`);
    return response.data;
  }

  async getRegisterSummary(registerId: string) {
    const { data } = await api.get<CashRegisterSummary>(
      `/cash/${registerId}/summary`
    );
    return data;
  }

  async exportCashMovements(registerId: string) {
    const { data } = await api.get(`/cash/${registerId}/export`);
    return data;
  }

  async getRegisterDetails(registerId: string) {
    const { data } = await api.get(`/cash/${registerId}`);
    return data;
  }

  async deleteCashRegister(id: string) {
    // Cambiamos a una ruta más RESTful y consistente
    const response = await api.delete(`/cash/${id}`);
    return response.data;
  }
}

export const cashService = new CashService();

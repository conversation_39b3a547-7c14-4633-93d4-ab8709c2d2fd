import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  ShoppingCart,
  Package,
  FileBarChart,
  Settings,
  Truck,
  AlertCircle,
  ChevronDown,
  Wallet,
  Users,
} from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

type SubMenuItem = {
  path: string;
  label: string;
  permissions?: string[];
};

type MenuItemType = {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  permissions?: string[];
  submenu?: SubMenuItem[];
};

type SidebarProps = {
  isCollapsed?: boolean;
  isMobile?: boolean;
};

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed = false,
  isMobile = false,
}) => {
  const location = useLocation();
  const [openMenus, setOpenMenus] = useState<string[]>([]);

  const toggleMenu = (path: string) => {
    setOpenMenus((prev) =>
      prev.includes(path) ? prev.filter((p) => p !== path) : [...prev, path]
    );
  };

  const menuItems: MenuItemType[] = [
    {
      path: '/',
      label: 'Dashboard',
      icon: LayoutDashboard,
    },
    {
      path: '/sales',
      label: 'Ventas',
      icon: ShoppingCart,
      permissions: ['sales.list'],
    },
    {
      path: '/customers',
      label: 'Clientes',
      icon: Users,
      permissions: ['customers.list'],
    },
    {
      path: '/inventory',
      label: 'Inventario',
      icon: Package,
      permissions: ['inventory.list'],
      submenu: [
        {
          path: '/inventory/products',
          label: 'Productos',
          permissions: ['products.list'],
        },
        {
          path: '/inventory/stock/entries',
          label: 'Entradas',
          permissions: ['inventory.stock.entry'],
        },
        {
          path: '/inventory/stock/outputs',
          label: 'Salidas',
          permissions: ['inventory.stock.output'],
        },
        {
          path: '/inventory/categories',
          label: 'Categorías',
          permissions: ['categories.list'],
        },
        {
          path: '/inventory/laboratories',
          label: 'Laboratorios',
          permissions: ['laboratories.list'],
        },
      ],
    },
    // {
    //   path: '/purchases',
    //   label: 'Compras',
    //   icon: Truck,
    //   permissions: ['purchases.list'],
    //   submenu: [
    //     { path: '/purchases/orders', label: 'Órdenes de Compra' },
    //     { path: '/purchases/reception', label: 'Recepción' },
    //     { path: '/purchases/suppliers', label: 'Proveedores' },
    //     { path: '/purchases/returns', label: 'Devoluciones' },
    //   ],
    // },
    {
      path: '/cashier',
      label: 'Caja',
      icon: Wallet,
      permissions: ['cash:list'],
      submenu: [
        {
          path: '/cashier/operations',
          label: 'Apertura/Cierre',
          permissions: ['cash:list'],
        },
        {
          path: '/cashier/history',
          label: 'Historial',
          permissions: ['cash:list'],
        },
      ],
    },
    {
      path: '/reports',
      label: 'Reportes',
      icon: FileBarChart,
      permissions: ['reports.list'],
      submenu: [
        { path: '/reports/sales', label: 'Ventas' },
        { path: '/reports/inventory', label: 'Inventario' },
      ],
    },
    // {
    //   path: '/alerts',
    //   label: 'Alertas',
    //   icon: AlertCircle,
    //   permissions: ['alerts.list'],
    // },
    {
      path: '/settings',
      label: 'Configuración',
      icon: Settings,
      permissions: ['settings.list'],
      submenu: [
        { path: '/settings/users', label: 'Usuarios' },
        { path: '/settings/general', label: 'General' },
      ],
    },
  ];

  const renderMenuItem = (item: MenuItemType) => {
    const isActive = location.pathname === item.path;
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const isOpen = openMenus.includes(item.path);

    const MenuItem = (
      <div className='w-full'>
        {hasSubmenu ? (
          <Collapsible open={isOpen && !isCollapsed}>
            <CollapsibleTrigger
              className={cn(
                'flex w-full items-center p-2 rounded-lg group transition-colors',
                isActive
                  ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-white'
                  : 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
              )}
              onClick={() => !isCollapsed && toggleMenu(item.path)}
            >
              <item.icon
                className={cn(
                  'w-5 h-5 transition-colors',
                  isActive ? 'text-primary' : 'text-gray-500 dark:text-gray-400'
                )}
              />
              {!isCollapsed && (
                <>
                  <span className='ml-3 flex-1 whitespace-nowrap text-left'>
                    {item.label}
                  </span>
                  {hasSubmenu && (
                    <div
                      className={cn(
                        'transition-transform',
                        isOpen && 'rotate-180'
                      )}
                    >
                      <ChevronDown className='w-4 h-4' />
                    </div>
                  )}
                </>
              )}
            </CollapsibleTrigger>
            <CollapsibleContent className='pl-4'>
              {item.submenu?.map((subItem) => (
                <Link
                  key={subItem.path}
                  to={subItem.path}
                  className={cn(
                    'flex items-center p-2 rounded-lg group transition-colors',
                    location.pathname === subItem.path
                      ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-white'
                      : 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                >
                  <span className='w-2 h-2 rounded-full bg-current' />
                  <span className='ml-3 flex-1 whitespace-nowrap text-left'>
                    {subItem.label}
                  </span>
                </Link>
              ))}
            </CollapsibleContent>
          </Collapsible>
        ) : (
          <Link
            to={item.path}
            className={cn(
              'flex items-center p-2 rounded-lg group transition-colors',
              isActive
                ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-white'
                : 'text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
            )}
          >
            <item.icon
              className={cn(
                'w-5 h-5 transition-colors',
                isActive ? 'text-primary' : 'text-gray-500 dark:text-gray-400'
              )}
            />
            {!isCollapsed && (
              <span className='ml-3 flex-1 whitespace-nowrap text-left'>
                {item.label}
              </span>
            )}
          </Link>
        )}
      </div>
    );

    if (isCollapsed) {
      return (
        <TooltipProvider key={item.path} delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>{MenuItem}</TooltipTrigger>
            <TooltipContent side='right' className='font-medium'>
              {item.label}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return MenuItem;
  };

  return (
    <div
      className={cn(
        'h-full bg-white dark:bg-gray-800 transition-all duration-300 ease-in-out overflow-y-auto',
        'flex flex-col',
        isMobile ? 'p-4' : 'p-3'
      )}
    >
      <nav className='space-y-1'>
        {menuItems.map((item) => (
          <div key={item.path}>{renderMenuItem(item)}</div>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;

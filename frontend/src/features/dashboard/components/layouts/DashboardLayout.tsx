import React, { useState, useEffect } from 'react';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { useTheme } from 'next-themes';
import {
  <PERSON>u,
  Bell,
  User,
  Settings,
  LogOut,
  Sun,
  Moon,
  Laptop,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import Sidebar from './Sidebar';
import { useAuthStore } from '@/features/auth';
import { Notification } from '@/features/notifications';
import { useSettingsStore } from '@/features/settings';
import { getImageUrl } from '@/lib/utils/image';

const ThemeToggle = () => {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className='p-2 hover:bg-accent/10 rounded-lg'>
          {theme === 'dark' ? (
            <Moon className='h-5 w-5' />
          ) : theme === 'light' ? (
            <Sun className='h-5 w-5' />
          ) : (
            <Laptop className='h-5 w-5' />
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem onClick={() => setTheme('light')}>
          <Sun className='mr-2 h-4 w-4' />
          <span>Claro</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          <Moon className='mr-2 h-4 w-4' />
          <span>Oscuro</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          <Laptop className='mr-2 h-4 w-4' />
          <span>Sistema</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const NotificationDropdown = ({
  notifications,
  showNotification,
}: {
  notifications: Notification[];
  showNotification: (notification: Notification) => void;
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <button className='p-2 hover:bg-accent/10 rounded-lg relative'>
        <Bell className='w-5 h-5' />
        {notifications.some((n) => n.unread) && (
          <span className='absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full'></span>
        )}
      </button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align='end' className='w-80'>
      <DropdownMenuLabel>Notificaciones</DropdownMenuLabel>
      <DropdownMenuSeparator />
      {notifications.length > 0 ? (
        notifications.map((notification) => (
          <DropdownMenuItem
            key={notification.id}
            onClick={() => showNotification(notification)}
            className='cursor-pointer hover:bg-accent/10 dark:hover:text-white'
          >
            <div className='flex flex-col space-y-1'>
              <p className='font-medium dark:text-white'>
                {notification.title}
              </p>
              <p className='text-sm text-muted-foreground dark:text-gray-300 dark:group-hover:text-white'>
                {notification.message}
              </p>
              <p className='text-xs text-muted-foreground dark:text-gray-400'>
                {notification.time}
              </p>
            </div>
          </DropdownMenuItem>
        ))
      ) : (
        <div className='p-4 text-center text-sm text-muted-foreground'>
          No hay notificaciones
        </div>
      )}
    </DropdownMenuContent>
  </DropdownMenu>
);

const UserProfileDropdown = ({ handleLogout, user }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <button className='flex items-center space-x-2 p-2 hover:bg-accent/10 rounded-lg'>
        <User className='w-5 h-5' />
        <div className='hidden md:block text-right'>
          <p className='text-sm font-medium'>{user?.username || 'Usuario'}</p>
          <p className='text-xs text-muted-foreground'>
            {user?.role === 'admin' ? 'Administrador' : 'Cajero'}
          </p>
        </div>
      </button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align='end'>
      <DropdownMenuLabel>Mi Cuenta</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem asChild>
        <Link to='/settings' className='cursor-pointer'>
          <Settings className='w-4 h-4 mr-2' />
          Configuración
        </Link>
      </DropdownMenuItem>
      <DropdownMenuItem
        onClick={handleLogout}
        className='text-red-600 cursor-pointer'
      >
        <LogOut className='w-4 h-4 mr-2' />
        Cerrar Sesión
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

export default function DashboardLayout() {
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const { user, logout, isAuthenticated } = useAuthStore();
  const publicSettings = useSettingsStore((state) => state.publicSettings);

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Stock Bajo',
      message: 'Algunos productos están por debajo del stock mínimo',
      time: 'Hace 5 minutos',
      unread: true,
    },
    {
      id: '2',
      title: 'Productos por caducar',
      message: 'Hay productos próximos a caducar',
      time: 'Hace 10 minutos',
      unread: false,
    },
  ]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const handleLogout = () => {
    logout();
    localStorage.removeItem('auth-storage');
    navigate('/auth/login', { replace: true });
  };

  const showNotification = (notification: Notification) => {
    // console.log('Notificación:', notification);
  };

  const Navbar = () => (
    <nav className='h-16 bg-white py-2.5 border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700 fixed left-0 right-0 top-0 z-50 px-4'>
      <div className='w-full flex items-center justify-between'>
        <div className='flex items-center'>
          {!isMobile && (
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className='p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg mr-2'
              title={isSidebarOpen ? 'Colapsar menú' : 'Expandir menú'}
            >
              <Menu className='w-6 h-6' />
            </button>
          )}
          {isMobile && (
            <Sheet>
              <SheetTrigger>
                <Menu className='w-6 h-6' />
              </SheetTrigger>
              <SheetContent side='left' className='p-0 w-64 max-w-[80%]'>
                <div className='h-full'>
                  <Sidebar isCollapsed={false} isMobile={true} />
                </div>
              </SheetContent>
            </Sheet>
          )}
          <div className='flex items-center gap-2'>
            {publicSettings?.logo && (
              <img
                src={getImageUrl(publicSettings.logo)}
                alt='Logo'
                className='h-8 w-auto object-contain'
                onError={(e) => {
                  const img = e.currentTarget;
                  img.src = '/placeholder-logo.png';
                  img.onerror = null;
                }}
              />
            )}
            {/* <span className='self-center text-2xl font-semibold whitespace-nowrap dark:text-white ml-2'>
              {publicSettings?.systemName || ''}
            </span> */}
          </div>
        </div>

        <div className='flex items-center gap-4'>
          <ThemeToggle />
          {/* <NotificationDropdown
            notifications={notifications}
            showNotification={showNotification}
          /> */}
          <UserProfileDropdown handleLogout={handleLogout} user={user} />
        </div>
      </div>
    </nav>
  );

  return (
    <div className='min-h-screen'>
      <Navbar />

      {!isMobile && (
        <aside
          className={`fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${
            isSidebarOpen ? 'w-64' : 'w-16'
          }`}
        >
          <Sidebar isCollapsed={!isSidebarOpen} isMobile={false} />
        </aside>
      )}

      <main
        className={`pt-16 min-h-[calc(100vh-4rem)] transition-all duration-300 ${
          !isMobile ? (isSidebarOpen ? 'ml-64' : 'ml-16') : ''
        }`}
      >
        <div className='container mx-auto p-4'>
          <Outlet />
        </div>
      </main>
    </div>
  );
}

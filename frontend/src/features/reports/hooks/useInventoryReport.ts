import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import * as XLSX from 'xlsx';
import { useToast } from '@/hooks/useToast';
import { reportService } from '../services/report.service';
import type {
  InventoryReportFilters,
  InventoryReportSummary,
  InventoryReportResponse,
} from '../types/report.types';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { formatCurrency } from '@/lib/utils/format';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

// Inicializar pdfMake con las fuentes
pdfMake.vfs = pdfFonts.pdfMake ? pdfFonts.pdfMake.vfs : pdfFonts;

const defaultSummary: InventoryReportSummary = {
  totalProducts: 0,
  totalStock: 0,
  totalValue: 0,
  lowStockProducts: 0,
  expiringProducts: 0,
  activeProducts: 0,
};

export function useInventoryReport() {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const publicSettings = useSettingsStore((state) => state.publicSettings);

  const [filters, setFilters] = useState<InventoryReportFilters>({
    search: '',
    categoryId: null,
    laboratoryId: null,
    stockAlert: false,
    expirationAlert: false,
    isActive: true, // Cambiado a true por defecto
  });

  const query = useQuery({
    queryKey: ['inventory-report', filters],
    queryFn: () => reportService.getInventoryReport(filters),
  });

  const handleFiltersChange = (newFilters: Partial<InventoryReportFilters>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }));
  };

  const resetFilters = () => {
    setFilters({
      search: '',
      categoryId: null,
      laboratoryId: null,
      stockAlert: false,
      expirationAlert: false,
      isActive: true, // Cambiado a true al resetear
    });
  };

  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      const loadingToast = toast({
        title: 'Generando Excel',
        description: 'Por favor espere...',
        duration: null,
      });

      const wb = XLSX.utils.book_new();

      // Preparar información de filtros aplicados
      const appliedFilters = [
        filters.search && `Búsqueda: ${filters.search}`,
        filters.categoryId &&
          `Categoría: ${
            query.data?.products.find(
              (p) => p.category?._id === filters.categoryId
            )?.category?.name || ''
          }`,
        filters.laboratoryId &&
          `Laboratorio: ${
            query.data?.products.find(
              (p) => p.laboratory?._id === filters.laboratoryId
            )?.laboratory?.name || ''
          }`,
        filters.stockAlert && 'Productos bajo stock mínimo',
        filters.expirationAlert && 'Productos próximos a vencer',
        !filters.isActive && 'Incluye productos inactivos',
      ].filter(Boolean);

      // HOJA 1: RESUMEN con filtros aplicados
      const headerData = [
        [publicSettings?.systemName || 'Sistema de Inventario'],
        ['REPORTE DE INVENTARIO'],
        [`Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`],
        [],
        appliedFilters.length > 0 ? ['FILTROS APLICADOS'] : [],
        ...appliedFilters.map((filter) => [filter]),
        [],
        ['RESUMEN'],
        [
          'Total Productos',
          'Stock Total',
          'Productos Bajo Stock',
          'Productos por Vencer',
          'Productos Activos',
        ],
        [
          query.data?.summary.totalProducts || 0,
          query.data?.summary.totalStock || 0,
          query.data?.summary.lowStockProducts || 0,
          query.data?.summary.expiringProducts || 0,
          query.data?.summary.activeProducts || 0,
        ].map((val) => val.toString()),
        [],
        ['DETALLE DE PRODUCTOS'],
        [
          'SKU',
          'Nombre',
          'Laboratorio',
          'Categoría',
          'Stock',
          'Stock Mínimo',
          'Stock Máximo',
          'Costo',
          'Precio',
          'Estado',
        ],
      ];

      // Datos de productos con validación para campos nulos
      const productsData =
        query.data?.products.map((product) => [
          product.sku || '-',
          product.name || '-',
          product.laboratory?.name || '-',
          product.category?.name || '-',
          (product.stock || 0).toString(), // Convertido a string
          (product.minStock || 0).toString(), // Convertido a string
          (product.maxStock || 0).toString(), // Convertido a string
          formatCurrency(product.cost || 0),
          formatCurrency(product.price || 0),
          !product.isActive
            ? 'INACTIVO'
            : product.stock <= product.minStock
            ? 'BAJO STOCK'
            : product.expirationAlert
            ? 'POR VENCER'
            : 'ACTIVO',
        ]) || [];

      // HOJA 1: Resumen y listado principal
      const ws1 = XLSX.utils.aoa_to_sheet([...headerData, ...productsData]);
      ws1['!cols'] = [
        { wch: 15 }, // SKU
        { wch: 40 }, // Nombre
        { wch: 20 }, // Laboratorio
        { wch: 20 }, // Categoría
        { wch: 10 }, // Stock
        { wch: 15 }, // Stock Mínimo
        { wch: 15 }, // Stock Máximo
        { wch: 15 }, // Costo
        { wch: 15 }, // Precio
        { wch: 15 }, // Estado
      ];
      XLSX.utils.book_append_sheet(wb, ws1, 'Resumen');

      // HOJA 2: Detalle completo con información adicional
      const detailsData = [
        [publicSettings?.systemName || 'Sistema de Inventario'],
        ['REPORTE DETALLADO DE INVENTARIO'],
        [`Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`],
        [],
      ];

      // Agrupar productos por categoría
      const productsByCategory =
        query.data?.products.reduce((acc, product) => {
          const categoryName = product.category?.name || 'Sin Categoría';
          if (!acc[categoryName]) {
            acc[categoryName] = [];
          }
          acc[categoryName].push(product);
          return acc;
        }, {} as Record<string, typeof query.data.products>) || {};

      // Agregar productos agrupados por categoría
      Object.entries(productsByCategory).forEach(([category, products]) => {
        detailsData.push(
          [],
          [`CATEGORÍA: ${category}`],
          [
            'SKU',
            'Producto',
            'Laboratorio',
            'Stock',
            'Stock Mín',
            'Stock Máx',
            'Costo',
            'Precio',
            'Estado',
          ]
        );

        products.forEach((product) => {
          detailsData.push([
            product.sku || '-',
            product.name || '-',
            product.laboratory?.name || '-',
            (product.stock || 0).toString(), // Convertido a string
            (product.minStock || 0).toString(), // Convertido a string
            (product.maxStock || 0).toString(), // Convertido a string
            formatCurrency(product.cost || 0),
            formatCurrency(product.price || 0),
            !product.isActive
              ? 'INACTIVO'
              : product.stock <= product.minStock
              ? 'BAJO STOCK'
              : product.expirationAlert
              ? 'POR VENCER'
              : 'ACTIVO',
          ]);
        });

        // Agregar totales por categoría
        const categoryTotal = products.reduce(
          (total, product) => total + (product.stock || 0),
          0
        );
        detailsData.push(
          [],
          [
            '',
            '',
            'Total Stock:',
            categoryTotal.toString(),
            '',
            '',
            '',
            '',
            '',
          ], // Convertido a string
          ['----------------------------------------']
        );
      });

      const ws2 = XLSX.utils.aoa_to_sheet(detailsData);
      ws2['!cols'] = [
        { wch: 15 }, // SKU
        { wch: 40 }, // Producto
        { wch: 20 }, // Laboratorio
        { wch: 10 }, // Stock
        { wch: 10 }, // Stock Mín
        { wch: 10 }, // Stock Máx
        { wch: 15 }, // Costo
        { wch: 15 }, // Precio
        { wch: 15 }, // Estado
      ];
      XLSX.utils.book_append_sheet(wb, ws2, 'Detalle');

      // Guardar el archivo
      XLSX.writeFile(
        wb,
        `inventario-${format(new Date(), 'yyyy-MM-dd-HHmm')}.xlsx`
      );

      loadingToast.dismiss();
      toast({
        title: 'Excel generado exitosamente',
        description: 'El reporte ha sido generado correctamente',
      });
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error al generar Excel',
        description: 'Ocurrió un error al generar el archivo',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const generatePDF = async () => {
    try {
      setIsExporting(true);
      const loadingToast = toast({
        title: 'Generando PDF',
        description: 'Por favor espere...',
        duration: null,
      });

      const docDefinition = {
        pageMargins: [40, 40, 40, 60],
        footer: function (currentPage, pageCount) {
          return {
            columns: [
              { text: format(new Date(), 'dd/MM/yyyy HH:mm'), style: 'footer' },
              {
                text: `Página ${currentPage} de ${pageCount}`,
                style: 'footer',
                alignment: 'right',
              },
            ],
            margin: [40, 20],
          };
        },
        content: [
          // Encabezado
          {
            columns: [
              {
                stack: [
                  {
                    text: publicSettings?.systemName || 'Sistema de Inventario',
                    style: 'companyName',
                  },
                ],
              },
              {
                stack: [
                  {
                    text: 'REPORTE DE INVENTARIO',
                    style: 'reportTitle',
                    alignment: 'right',
                  },
                ],
              },
            ],
            margin: [0, 0, 0, 20],
          },

          // Tarjetas de resumen
          {
            columns: [
              {
                width: '*',
                table: {
                  widths: ['*'],
                  body: [
                    [
                      {
                        stack: [
                          { text: 'Total Productos', style: 'cardTitle' },
                          {
                            text: query.data?.summary.totalProducts || 0,
                            style: 'cardValue',
                          },
                        ],
                      },
                    ],
                  ],
                },
                style: 'card',
              },
              {
                width: '*',
                table: {
                  widths: ['*'],
                  body: [
                    [
                      {
                        stack: [
                          { text: 'Stock Total', style: 'cardTitle' },
                          {
                            text: query.data?.summary.totalStock || 0,
                            style: 'cardValue',
                          },
                        ],
                      },
                    ],
                  ],
                },
                style: 'card',
              },
              {
                width: '*',
                table: {
                  widths: ['*'],
                  body: [
                    [
                      {
                        stack: [
                          { text: 'Bajo Stock', style: 'cardTitle' },
                          {
                            text: query.data?.summary.lowStockProducts || 0,
                            style: 'cardValue',
                            color: '#ef4444',
                          },
                        ],
                      },
                    ],
                  ],
                },
                style: 'card',
              },
            ],
            margin: [0, 0, 0, 20],
          },

          // Tabla principal
          {
            table: {
              headerRows: 1,
              widths: [
                'auto',
                '*',
                'auto',
                'auto',
                'auto',
                'auto',
                'auto',
                'auto',
              ],
              body: [
                [
                  { text: 'SKU', style: 'tableHeader' },
                  { text: 'Producto', style: 'tableHeader' },
                  { text: 'Laboratorio', style: 'tableHeader' },
                  { text: 'Categoría', style: 'tableHeader' },
                  { text: 'Stock', style: 'tableHeader' },
                  { text: 'Costo', style: 'tableHeader', alignment: 'right' },
                  { text: 'Precio', style: 'tableHeader', alignment: 'right' },
                  { text: 'Estado', style: 'tableHeader' },
                ],
                ...(query.data?.products.map((product) => [
                  { text: product.sku || '-', style: 'saleCell' },
                  { text: product.name, style: 'saleCell' },
                  { text: product.laboratory?.name || '-', style: 'saleCell' },
                  { text: product.category?.name || '-', style: 'saleCell' },
                  {
                    text: product.stock.toString(),
                    style: 'saleCellBold',
                    color:
                      product.stock <= product.minStock ? '#ef4444' : '#1e40af',
                  },
                  {
                    text: formatCurrency(product.cost),
                    style: 'saleCell',
                    alignment: 'right',
                  },
                  {
                    text: formatCurrency(product.price),
                    style: 'saleCell',
                    alignment: 'right',
                  },
                  {
                    text: !product.isActive
                      ? 'INACTIVO'
                      : product.stock <= product.minStock
                      ? 'BAJO STOCK'
                      : product.expirationAlert
                      ? 'POR VENCER'
                      : 'ACTIVO',
                    style: 'statusCell',
                    color: !product.isActive
                      ? '#ef4444'
                      : product.stock <= product.minStock
                      ? '#eab308'
                      : product.expirationAlert
                      ? '#eab308'
                      : '#22c55e',
                  },
                ]) || []),
              ],
            },
            layout: {
              hLineWidth: (i, node) =>
                i === 0 || i === 1 || i === node.table.body.length ? 1 : 0.5,
              vLineWidth: () => 0.5,
              hLineColor: (i) => (i === 0 || i === 1 ? '#1e40af' : '#e2e8f0'),
              vLineColor: () => '#e2e8f0',
              paddingLeft: () => 4,
              paddingRight: () => 4,
              paddingTop: () => 4,
              paddingBottom: () => 4,
            },
          },
        ],
        styles: {
          companyName: {
            fontSize: 20,
            bold: true,
            color: '#1e40af',
            margin: [0, 0, 0, 5],
          },
          reportTitle: {
            fontSize: 24,
            bold: true,
            color: '#1e40af',
          },
          card: {
            margin: [5, 0],
            fillColor: '#f8fafc',
          },
          cardTitle: {
            fontSize: 12,
            color: '#64748b',
            bold: true,
            margin: [0, 5],
          },
          cardValue: {
            fontSize: 20,
            bold: true,
            color: '#1e40af',
            margin: [0, 5],
          },
          tableHeader: {
            fontSize: 8.5, // Aumentado de 8 a 8.5
            bold: true,
            color: '#ffffff',
            fillColor: '#1e40af',
            padding: [6, 4],
          },
          saleCell: {
            fontSize: 8, // Aumentado de 7 a 8
            color: '#334155',
            padding: [4, 3],
          },
          saleCellBold: {
            fontSize: 8, // Aumentado de 7 a 8
            bold: true,
            padding: [4, 3],
          },
          statusCell: {
            fontSize: 8, // Aumentado de 7 a 8
            bold: true,
            padding: [4, 3],
          },
          footer: {
            fontSize: 8,
            color: '#64748b',
          },
        },
        defaultStyle: {
          fontSize: 8, // Se mantiene en 8
          color: '#1e293b',
        },
      };

      pdfMake
        .createPdf(docDefinition)
        .download(
          `reporte-inventario-${format(new Date(), 'yyyy-MM-dd-HHmm')}.pdf`
        );

      loadingToast.dismiss();
      toast({
        title: 'PDF generado exitosamente',
        description: 'El reporte ha sido generado correctamente',
      });
    } catch (error) {
      toast({
        title: 'Error al generar PDF',
        description: 'Ocurrió un error al generar el archivo',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return {
    filters,
    data: query.data,
    isLoading: query.isLoading,
    isExporting,
    handleFiltersChange,
    resetFilters,
    exportToExcel,
    generatePDF,
  };
}

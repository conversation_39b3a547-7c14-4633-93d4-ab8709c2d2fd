import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { reportService } from '../services/report.service';
import type { SalesReportFilters } from '../types/report.types';
import { useToast } from '@/hooks/useToast';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import {
  SALE_STATUS,
  SALE_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
} from '@/features/sales/constants/sale.constants';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import * as XLSX from 'xlsx';
import { useAuth } from '@/features/auth';

// Inicializar pdfMake con las fuentes
pdfMake.vfs = pdfFonts.pdfMake ? pdfFonts.pdfMake.vfs : pdfFonts;

const REPORT_CONFIG = {
  format: 'letter',
  width: 215.9, // mm (tamaño carta)
  height: 279.4, // mm (tamaño carta)
  scale: 2,
  margin: 20, // mm
};

export function useSalesReport() {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [filters, setFilters] = useState<SalesReportFilters>({
    search: '',
    status: undefined,
    customerId: null,
    startDate: null,
    endDate: null,
    cashRegisterId: null,
  });
  const publicSettings = useSettingsStore((state) => state.publicSettings);
  const { user } = useAuth();

  const query = useQuery({
    queryKey: ['sales-report', filters],
    queryFn: () => reportService.getSalesReport(filters),
  });

  const handleFiltersChange = (newFilters: Partial<SalesReportFilters>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }));
  };

  const summary = {
    totalAmount:
      query.data?.sales.reduce((sum, sale) => sum + (sale.total || 0), 0) ?? 0,
    totalSales: query.data?.sales.length ?? 0,
    completedSales:
      query.data?.sales.filter((sale) => sale.status === SALE_STATUS.COMPLETED)
        .length ?? 0,
    canceledSales:
      query.data?.sales.filter((sale) => sale.status === SALE_STATUS.CANCELED)
        .length ?? 0,
  };

  const generatePDF = async () => {
    try {
      setIsExporting(true);
      const loadingToast = toast({
        title: 'Generando PDF',
        description: 'Por favor espere...',
        duration: null,
      });

      const docDefinition = {
        pageMargins: [40, 40, 40, 60],
        footer: function (currentPage, pageCount) {
          return {
            columns: [
              { text: format(new Date(), 'dd/MM/yyyy HH:mm'), style: 'footer' },
              {
                text: `Página ${currentPage} de ${pageCount}`,
                style: 'footer',
                alignment: 'right',
              },
            ],
            margin: [40, 20],
          };
        },
        content: [
          // Encabezado
          {
            columns: [
              {
                stack: [
                  {
                    text: publicSettings?.systemName || 'Sistema de Ventas',
                    style: 'companyName',
                  },
                  // Eliminamos las referencias a address y phone ya que no existen en el tipo
                ],
              },
              {
                stack: [
                  {
                    text: 'REPORTE DE VENTAS',
                    style: 'reportTitle',
                    alignment: 'right',
                  },
                  // Solo mostrar período si hay fechas seleccionadas
                  ...(filters.startDate && filters.endDate
                    ? [
                        {
                          text: `Período: ${format(
                            new Date(filters.startDate),
                            'dd/MM/yyyy'
                          )} - ${format(
                            new Date(filters.endDate),
                            'dd/MM/yyyy'
                          )}`,
                          style: 'period',
                          alignment: 'right',
                        },
                      ]
                    : []),
                ],
              },
            ],
            margin: [0, 0, 0, 20],
          },

          // Resumen de Ventas
          {
            table: {
              widths: ['*', '*', '*', '*'],
              body: [
                [
                  {
                    text: 'Total Ventas',
                    style: 'summaryHeader',
                    alignment: 'center',
                  },
                  {
                    text: 'Monto Total',
                    style: 'summaryHeader',
                    alignment: 'center',
                  },
                  {
                    text: 'Completadas',
                    style: 'summaryHeader',
                    alignment: 'center',
                  },
                  {
                    text: 'Canceladas',
                    style: 'summaryHeader',
                    alignment: 'center',
                  },
                ],
                [
                  {
                    text: summary.totalSales,
                    style: 'summaryValue',
                    alignment: 'center',
                  },
                  {
                    text: formatCurrency(summary.totalAmount),
                    style: 'summaryValue',
                    alignment: 'center',
                  },
                  {
                    text: summary.completedSales,
                    style: 'summaryValue',
                    alignment: 'center',
                  },
                  {
                    text: summary.canceledSales,
                    style: 'summaryValue',
                    alignment: 'center',
                  },
                ],
              ],
            },
            layout: {
              hLineWidth: (i) => 0.5,
              vLineWidth: (i) => 0.5,
              hLineColor: (i) => '#e2e8f0',
              vLineColor: (i) => '#e2e8f0',
            },
            margin: [0, 0, 0, 20],
          },

          // Tabla Consolidada
          {
            table: {
              headerRows: 1,
              // Aumentamos el ancho de las columnas de estado y método de pago
              widths: [35, 40, '*', 25, 30, 42, 45, 35, 35, 35], // 35 -> 42 para Estado, 45 se mantiene para Método Pago
              body: [
                [
                  { text: 'N° Venta', style: 'tableHeader' },
                  { text: 'Fecha', style: 'tableHeader' },
                  { text: 'Cliente', style: 'tableHeader' },
                  { text: 'Tipo', style: 'tableHeader' },
                  { text: 'N° Doc.', style: 'tableHeader' },
                  { text: 'Estado', style: 'tableHeader' },
                  { text: 'Método Pago', style: 'tableHeader' },
                  {
                    text: 'Subtotal',
                    style: 'tableHeader',
                    alignment: 'right',
                  },
                  { text: 'Dscto.', style: 'tableHeader', alignment: 'right' },
                  { text: 'Total', style: 'tableHeader', alignment: 'right' },
                ],
                ...query.data?.sales.map((sale) => [
                  { text: sale.number, style: 'saleCell' },
                  {
                    text: format(new Date(sale.date), 'dd/MM/yyyy'),
                    style: 'saleCell',
                  },
                  {
                    text: sale.customer?.businessName || 'CONSUMIDOR FINAL',
                    style: 'saleCell',
                  },
                  {
                    text: sale.customer?.documentType || '-',
                    style: 'saleCell',
                  },
                  {
                    text: sale.customer?.documentNumber || '-',
                    style: 'saleCell',
                  },
                  { text: SALE_STATUS_LABELS[sale.status], style: 'saleCell' },
                  {
                    text: PAYMENT_METHOD_LABELS[sale.paymentMethod],
                    style: 'saleCell',
                  },
                  {
                    text: formatCurrency(sale.subtotal),
                    style: 'saleCell',
                    alignment: 'right',
                  },
                  {
                    text: formatCurrency(sale.discount),
                    style: 'saleCell',
                    alignment: 'right',
                  },
                  {
                    text: formatCurrency(sale.total),
                    style: 'saleCell',
                    alignment: 'right',
                  },
                ]),
              ],
            },
            layout: {
              hLineWidth: (i, node) =>
                i === 0 || i === 1 || i === node.table.body.length ? 1 : 0.5,
              vLineWidth: () => 0.5,
              hLineColor: (i) => (i === 0 || i === 1 ? '#1e40af' : '#e2e8f0'),
              vLineColor: () => '#e2e8f0',
              paddingLeft: (i) => 4,
              paddingRight: (i) => 4,
              paddingTop: (i) => 4,
              paddingBottom: (i) => 4,
            },
            margin: [0, 0, 0, 20],
          },

          // Total General
          {
            columns: [
              { width: '*', text: '' },
              {
                width: 'auto',
                table: {
                  widths: [100, 100],
                  body: [
                    [
                      {
                        text: 'Total General:',
                        style: 'summaryLabel',
                        alignment: 'right',
                        border: [0, 0, 0, 0],
                      },
                      {
                        text: `${formatCurrency(summary.totalAmount)}`,
                        style: 'summaryValue',
                        alignment: 'right',
                        border: [0, 0, 0, 0],
                      },
                    ],
                  ],
                },
                layout: {
                  defaultBorder: false,
                  paddingLeft: () => 4,
                  paddingRight: () => 4,
                  paddingTop: () => 4,
                  paddingBottom: () => 4,
                },
              },
            ],
            margin: [0, 10, 0, 0],
          },

          // Página nueva para detalles
          { text: '', pageBreak: 'before' },
          {
            text: 'DETALLE DE VENTAS',
            style: 'sectionHeader',
            margin: [0, 0, 0, 20],
          },

          // Detalles de cada venta
          ...query.data?.sales.flatMap((sale) => [
            // Encabezado de la venta
            {
              table: {
                widths: ['*', '*'],
                body: [
                  [
                    {
                      text: `Venta N°: ${sale.number}`,
                      style: 'saleHeader',
                      colSpan: 2,
                    },
                    {},
                  ],
                  [
                    `Cliente: ${
                      sale.customer
                        ? sale.customer.businessName
                        : 'CONSUMIDOR FINAL'
                    }`,
                    `Fecha: ${format(new Date(sale.date), 'dd/MM/yyyy HH:mm')}`,
                  ],
                  [
                    `Estado: ${SALE_STATUS_LABELS[sale.status]}`,
                    `Método de Pago: ${
                      PAYMENT_METHOD_LABELS[sale.paymentMethod]
                    }`,
                  ],
                ],
              },
              layout: 'lightHorizontalLines',
              margin: [0, 0, 0, 10],
            },

            // Detalle de productos
            {
              table: {
                headerRows: 1,
                widths: ['*', 'auto', 'auto', 'auto', 'auto'],
                body: [
                  [
                    { text: 'Producto', style: 'tableHeader' },
                    {
                      text: 'Cantidad',
                      style: 'tableHeader',
                      alignment: 'right',
                    },
                    {
                      text: 'P.Unit',
                      style: 'tableHeader',
                      alignment: 'right',
                    },
                    { text: 'Desc.', style: 'tableHeader', alignment: 'right' },
                    {
                      text: 'Subtotal',
                      style: 'tableHeader',
                      alignment: 'right',
                    },
                  ],
                  ...sale.items.map((item) => [
                    item.product.name,
                    { text: item.quantity.toString(), alignment: 'right' },
                    {
                      text: formatCurrency(item.unitPrice),
                      alignment: 'right',
                    },
                    {
                      text: formatCurrency(item.discount || 0),
                      alignment: 'right',
                    },
                    { text: formatCurrency(item.subtotal), alignment: 'right' },
                  ]),
                ],
              },
              layout: 'lightHorizontalLines',
              margin: [0, 0, 0, 20],
            },
          ]),
        ],
        styles: {
          companyName: {
            fontSize: 20,
            bold: true,
            color: '#1e40af',
            margin: [0, 0, 0, 5],
          },
          companyDetails: {
            fontSize: 10,
            color: '#64748b',
            margin: [0, 0, 0, 2],
          },
          reportTitle: {
            fontSize: 24,
            bold: true,
            color: '#1e40af',
          },
          period: {
            fontSize: 12,
            color: '#64748b',
            margin: [0, 5, 0, 0],
          },
          card: {
            margin: [5, 0],
            padding: 10,
            fillColor: '#f8fafc',
            borderRadius: 5,
          },
          cardTitle: {
            fontSize: 12,
            color: '#64748b',
            bold: true,
          },
          cardValue: {
            fontSize: 20,
            bold: true,
            color: '#1e40af',
            margin: [0, 5],
          },
          cardSubtext: {
            fontSize: 10,
            color: '#64748b',
          },
          tableHeader: {
            fontSize: 8,
            bold: true,
            color: '#ffffff',
            fillColor: '#1e40af',
            padding: [6, 4],
          },
          saleCell: {
            fontSize: 7,
            color: '#334155',
            padding: [4, 3],
          },
          saleCellBold: {
            fontSize: 7,
            bold: true,
            color: '#1e40af',
            padding: [4, 3],
          },
          statusCell: {
            fontSize: 9,
            bold: true,
            padding: [6, 4],
          },
          productHeader: {
            fontSize: 8,
            bold: true,
            color: '#64748b',
            fillColor: '#f1f5f9',
            padding: [4, 3],
          },
          productCell: {
            fontSize: 8,
            color: '#334155',
            padding: [4, 3],
          },
          productCellBold: {
            fontSize: 8,
            bold: true,
            color: '#1e40af',
            padding: [4, 3],
          },
          separator: {
            height: 15,
          },
          footer: {
            fontSize: 8,
            color: '#64748b',
          },
          sectionHeader: {
            fontSize: 16,
            bold: true,
            color: '#1e40af',
          },
          saleHeader: {
            fontSize: 14,
            bold: true,
            color: '#1e40af',
            margin: [0, 5],
          },
          summaryHeader: {
            fontSize: 8,
            bold: true,
            color: '#1e40af',
            margin: [0, 3],
            fillColor: '#f1f5f9',
          },
          summaryLabel: {
            fontSize: 10,
            bold: true,
            color: '#1e293b',
            margin: [0, 3],
          },
          summaryValue: {
            fontSize: 10,
            bold: true,
            color: '#1e293b',
            margin: [0, 3],
          },
        },
        defaultStyle: {
          fontSize: 8,
          color: '#1e293b',
        },
      };

      pdfMake
        .createPdf(docDefinition)
        .download(
          `reporte-ventas-${format(new Date(), 'yyyy-MM-dd-HHmm')}.pdf`
        );

      loadingToast.dismiss();
      toast({
        title: 'PDF generado exitosamente',
        description: 'El reporte ha sido generado correctamente',
      });
    } catch (error) {
      toast({
        title: 'Error al generar PDF',
        description: 'Ocurrió un error al generar el archivo',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      const loadingToast = toast({
        title: 'Generando Excel',
        description: 'Por favor espere...',
        duration: null,
      });

      // Crear el libro de Excel
      const wb = XLSX.utils.book_new();

      // HOJA 1: RESUMEN
      const headerData = [
        [publicSettings?.systemName || 'Sistema de Ventas'],
        ['REPORTE DE VENTAS'],
        [`Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`],
        [`Generado por: ${user?.username}`],
        filters.startDate && filters.endDate
          ? [
              `Período: ${format(
                new Date(filters.startDate),
                'dd/MM/yyyy'
              )} - ${format(new Date(filters.endDate), 'dd/MM/yyyy')}`,
            ]
          : [],
        [],
        ['RESUMEN'],
        ['Total Ventas', 'Monto Total', 'Completadas', 'Canceladas'],
        [
          summary.totalSales,
          formatCurrency(summary.totalAmount),
          summary.completedSales,
          summary.canceledSales,
        ],
        [],
        ['DETALLE DE VENTAS'],
        [
          'N° Venta',
          'Fecha',
          'Cliente',
          'Tipo Doc.',
          'N° Doc.',
          'Estado',
          'Método de Pago',
          'Total',
        ],
      ];

      const salesData =
        query.data?.sales.map((sale) => [
          sale.number,
          format(new Date(sale.date), 'dd/MM/yyyy HH:mm'),
          sale.customer?.businessName || 'CONSUMIDOR FINAL',
          sale.customer?.documentType || '-',
          sale.customer?.documentNumber || '-',
          SALE_STATUS_LABELS[sale.status],
          PAYMENT_METHOD_LABELS[sale.paymentMethod],
          formatCurrency(sale.total),
        ]) || [];

      const ws1 = XLSX.utils.aoa_to_sheet([...headerData, ...salesData]);
      ws1['!cols'] = [
        { wch: 10 }, // N° Venta
        { wch: 20 }, // Fecha
        { wch: 30 }, // Cliente
        { wch: 15 }, // Tipo Doc.
        { wch: 15 }, // N° Doc.
        { wch: 15 }, // Estado
        { wch: 15 }, // Método de Pago
        { wch: 15 }, // Total
      ];
      XLSX.utils.book_append_sheet(wb, ws1, 'Resumen');

      // HOJA 2: DETALLE COMPLETO
      const detailsData = [
        [publicSettings?.systemName || 'Sistema de Ventas'],
        ['REPORTE DETALLADO DE VENTAS'],
        [`Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`],
        [`Generado por: ${user?.username}`],
        filters.startDate && filters.endDate
          ? [
              `Período: ${format(
                new Date(filters.startDate),
                'dd/MM/yyyy'
              )} - ${format(new Date(filters.endDate), 'dd/MM/yyyy')}`,
            ]
          : [],
        [],
      ];

      query.data?.sales.forEach((sale) => {
        // Encabezado de cada venta
        detailsData.push(
          [],
          [`VENTA N°: ${sale.number}`],
          [`Cliente: ${sale.customer?.businessName || 'CONSUMIDOR FINAL'}`],
          [
            `Documento: ${
              sale.customer
                ? `${sale.customer.documentType} ${sale.customer.documentNumber}`
                : '-'
            }`,
          ],
          [`Fecha: ${format(new Date(sale.date), 'dd/MM/yyyy HH:mm')}`],
          [`Estado: ${SALE_STATUS_LABELS[sale.status]}`],
          [`Método de Pago: ${PAYMENT_METHOD_LABELS[sale.paymentMethod]}`],
          [],
          ['PRODUCTOS'],
          ['Producto', 'Cantidad', 'Precio Unit.', 'Descuento', 'Subtotal']
        );

        // Detalle de productos
        sale.items.forEach((item) => {
          detailsData.push([
            item.product.name,
            item.quantity.toString(), // Convertimos a string
            formatCurrency(item.unitPrice),
            formatCurrency(item.discount || 0),
            formatCurrency(item.subtotal),
          ]);
        });

        // Totales de la venta
        detailsData.push(
          [],
          ['', '', '', 'Subtotal:', formatCurrency(sale.subtotal)],
          ['', '', '', 'Descuento:', formatCurrency(sale.discount)],
          ['', '', '', 'Total:', formatCurrency(sale.total)],
          [],
          ['-------------------------------------------']
        );
      });

      const ws2 = XLSX.utils.aoa_to_sheet(detailsData);
      ws2['!cols'] = [
        { wch: 40 }, // Producto
        { wch: 10 }, // Cantidad
        { wch: 15 }, // Precio Unit.
        { wch: 15 }, // Descuento
        { wch: 15 }, // Subtotal
      ];
      XLSX.utils.book_append_sheet(wb, ws2, 'Detalle');

      // Guardar el archivo
      XLSX.writeFile(
        wb,
        `reporte-ventas-${format(new Date(), 'yyyy-MM-dd-HHmm')}.xlsx`
      );

      loadingToast.dismiss();
      toast({
        title: 'Excel generado exitosamente',
        description: 'El reporte ha sido generado correctamente',
      });
    } catch (error) {
      toast({
        title: 'Error al generar Excel',
        description: 'Ocurrió un error al generar el archivo',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const resetFilters = () => {
    setFilters({
      search: '',
      status: undefined,
      customerId: null,
      startDate: null,
      endDate: null,
      cashRegisterId: null,
    });
  };

  return {
    data: {
      sales: query.data?.sales || [],
      total: query.data?.total || 0,
    },
    isLoading: query.isLoading,
    isExporting,
    filters,
    setFilters: handleFiltersChange,
    resetFilters,
    summary,
    generatePDF,
    exportToExcel,
  };
}

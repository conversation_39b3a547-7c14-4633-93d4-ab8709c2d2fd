import { useState } from 'react';
import { format } from 'date-fns';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { useSalesReport } from './useSalesReport';
import { formatCurrency } from '@/lib/utils/format';

export function useSalesReportPage() {
  const { data, isLoading, filters, setFilters, pagination } = useSalesReport();
  const publicSettings = useSettingsStore((state) => state.publicSettings);

  const handleExportExcel = async () => {
    const headerData = [
      [publicSettings?.systemName || 'Reporte de Ventas'],
      [`Período: ${format(new Date(filters.startDate), 'dd/MM/yyyy')} - ${format(new Date(filters.endDate), 'dd/MM/yyyy')}`],
      [],
      ['Resumen'],
      ['Total Ventas', 'Monto Total', 'Promedio', 'Completadas', 'Canceladas'],
      [
        data.summary.totalSales,
        formatCurrency(data.summary.totalAmount),
        formatCurrency(data.summary.averageAmount),
        data.summary.completedSales,
        data.summary.canceledSales,
      ],
      [],
      ['Detalle de Ventas'],
      ['Nº Venta', 'Fecha', 'Cliente', 'Estado', 'Total'],
    ];

    const salesData = data.sales.map((sale) => [
      sale.number,
      format(new Date(sale.date), 'dd/MM/yyyy HH:mm'),
      sale.customer?.businessName || 'CONSUMIDOR FINAL',
      sale.status,
      formatCurrency(sale.total),
    ]);

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([...headerData, ...salesData]);
    XLSX.utils.book_append_sheet(wb, ws, 'Reporte de Ventas');
    XLSX.writeFile(wb, `reporte-ventas-${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
  };

  const handleExportPDF = async () => {
    // Implementar exportación a PDF
  };

  return {
    data,
    isLoading,
    filters,
    setFilters,
    pagination,
    actions: {
      onExportExcel: handleExportExcel,
      onExportPDF: handleExportPDF,
    },
  };
}
import { usePermissions } from '@/features/users';
import { PERMISSIONS } from '@/features/auth';
import type { ReportPermissions } from '../types/report.types';

export function useReportPermissions(): { permissions: ReportPermissions } {
  const { checkPermission } = usePermissions();

  return {
    permissions: {
      canViewSales: checkPermission(PERMISSIONS.REPORTS.SALES.LIST),
      canViewInventory: checkPermission(PERMISSIONS.REPORTS.INVENTORY.LIST),
    },
  };
}

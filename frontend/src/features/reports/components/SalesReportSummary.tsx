import { Card } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils/format';
import type { SaleReportSummary } from '../types/report.types';

interface SalesReportSummaryProps {
  summary: SaleReportSummary;
}

export function SalesReportSummary({ summary }: SalesReportSummaryProps) {
  return (
    <div className='grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4'>
      <Card className='p-4'>
        <h3 className='text-sm font-medium text-muted-foreground'>
          Total Ventas
        </h3>
        <p className='text-2xl font-bold'>{summary.totalSales}</p>
      </Card>

      <Card className='p-4'>
        <h3 className='text-sm font-medium text-muted-foreground'>
          Monto Total
        </h3>
        <p className='text-2xl font-bold'>
          {formatCurrency(summary.totalAmount)}
        </p>
      </Card>

      <Card className='p-4'>
        <h3 className='text-sm font-medium text-muted-foreground'>Promedio</h3>
        <p className='text-2xl font-bold'>
          {formatCurrency(summary.averageAmount)}
        </p>
      </Card>

      <Card className='p-4'>
        <h3 className='text-sm font-medium text-muted-foreground'>
          Completadas
        </h3>
        <p className='text-2xl font-bold'>{summary.completedSales}</p>
      </Card>

      <Card className='p-4'>
        <h3 className='text-sm font-medium text-muted-foreground'>
          Canceladas
        </h3>
        <p className='text-2xl font-bold'>{summary.canceledSales}</p>
      </Card>
    </div>
  );
}

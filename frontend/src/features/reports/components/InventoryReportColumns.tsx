import { memo, useState } from 'react';
import { Eye, AlertCircle, Package } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils/format';
import { ProductDetailsDialog } from '@/features/inventory/products/components/ProductDetailsDialog';
import type { InventoryReportProduct } from '../types/report.types';
import type { Product } from '@/features/inventory/products/types/product.types';

const ProductNameCell = memo(
  ({ product }: { product: InventoryReportProduct }) => (
    <div className='min-w-[250px] flex items-center gap-3'>
      <div className='w-10 h-10 rounded-md bg-muted flex items-center justify-center'>
        <Package className='w-5 h-5 text-muted-foreground' />
      </div>
      <div className='flex flex-col'>
        <span className='font-medium'>{product.name}</span>
        <span className='text-xs text-muted-foreground'>
          SKU: {product.sku}
        </span>
      </div>
    </div>
  )
);

ProductNameCell.displayName = 'ProductNameCell';

const StockCell = memo(({ product }: { product: InventoryReportProduct }) => (
  <TooltipProvider>
    <div className='min-w-[100px]'>
      <div className='flex flex-col'>
        <div className='flex items-center gap-2'>
          <Badge
            variant={
              product.stock <= product.minStock
                ? 'destructive'
                : product.stock >= product.maxStock
                ? 'secondary'
                : 'default'
            }
          >
            {product.stock}
          </Badge>
          {product.stock <= product.minStock && (
            <Tooltip>
              <TooltipTrigger>
                <AlertCircle className='w-4 h-4 text-destructive' />
              </TooltipTrigger>
              <TooltipContent>
                <p>Stock bajo mínimo</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <span className='text-xs text-muted-foreground'>
          Mín: {product.minStock} | Máx: {product.maxStock}
        </span>
      </div>
    </div>
  </TooltipProvider>
));

StockCell.displayName = 'StockCell';

const ActionsCell = memo(({ product }: { product: InventoryReportProduct }) => {
  const [showDetails, setShowDetails] = useState(false);

  const adaptedProduct: Product = {
    _id: product._id,
    sku: product.sku,
    name: product.name,
    description: '',
    image: product.image || '', // Agregamos la propiedad image
    laboratory: product.laboratory
      ? {
          _id: product.laboratory._id,
          name: product.laboratory.name,
          isActive: true,
          isProtected: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      : {
          _id: '',
          name: 'Sin laboratorio',
          isActive: true,
          isProtected: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
    category: {
      _id: product.category._id,
      name: product.category.name,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    price: product.price,
    cost: product.cost,
    stock: product.stock,
    minStock: product.minStock,
    maxStock: product.maxStock,
    location: '',
    expirationDate: new Date().toISOString(),
    batchNumber: '',
    isActive: product.isActive,
    barcode: '',
    requiresPrescription: false,
    presentation: '',
    concentration: '',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: '',
    stockAlert: product.stockAlert,
    expirationAlert: product.expirationAlert,
    createdBy: {
      _id: '',
      username: '',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  return (
    <>
      <div className='flex items-center justify-end'>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => setShowDetails(true)}
              >
                <Eye className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Ver detalles del producto</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <ProductDetailsDialog
        product={adaptedProduct}
        open={showDetails}
        onOpenChange={setShowDetails}
      />
    </>
  );
});

ActionsCell.displayName = 'ActionsCell';

export const inventoryReportColumns = [
  {
    id: 'product',
    header: 'Producto',
    cell: (product: InventoryReportProduct) => (
      <ProductNameCell product={product} />
    ),
  },
  {
    id: 'category',
    header: 'Categoría',
    cell: (product: InventoryReportProduct) => (
      <Badge variant='outline' className='font-normal'>
        {product.category?.name || 'Sin categoría'}
      </Badge>
    ),
  },
  {
    id: 'laboratory',
    header: 'Laboratorio',
    cell: (product: InventoryReportProduct) => (
      <span className='text-sm text-muted-foreground'>
        {product.laboratory?.name || 'Sin laboratorio'}
      </span>
    ),
  },
  {
    id: 'stock',
    header: 'Stock',
    cell: (product: InventoryReportProduct) => <StockCell product={product} />,
  },
  {
    id: 'cost',
    header: 'Costo',
    cell: (product: InventoryReportProduct) => (
      <span className='text-right font-medium'>
        {formatCurrency(product.cost)}
      </span>
    ),
  },
  {
    id: 'price',
    header: 'Precio',
    cell: (product: InventoryReportProduct) => (
      <span className='text-right font-medium'>
        {formatCurrency(product.price)}
      </span>
    ),
  },
  {
    id: 'totalValue',
    header: 'Valor Total',
    cell: (product: InventoryReportProduct) => (
      <span className='text-right font-medium'>
        {formatCurrency(product.cost * product.stock)}
      </span>
    ),
  },
  {
    id: 'status',
    header: 'Estado',
    cell: (product: InventoryReportProduct) => (
      <Badge
        variant={
          !product.isActive
            ? 'destructive'
            : product.stock <= product.minStock
            ? 'secondary'
            : product.expirationAlert
            ? 'secondary'
            : 'success'
        }
      >
        {!product.isActive
          ? 'INACTIVO'
          : product.stock <= product.minStock
          ? 'BAJO STOCK'
          : product.expirationAlert
          ? 'POR VENCER'
          : 'ACTIVO'}
      </Badge>
    ),
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: (product: InventoryReportProduct) => (
      <ActionsCell product={product} />
    ),
  },
] as const;

export type InventoryReportColumn = {
  id: string;
  header: string;
  cell: (product: InventoryReportProduct) => React.ReactNode;
};

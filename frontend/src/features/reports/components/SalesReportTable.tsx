import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils/format';
import { SALE_STATUS_LABELS } from '@/features/sales/constants/sale.constants';
import type { Sale } from '@/features/sales/types/sale.types';

interface SalesReportTableProps {
  sales: Sale[];
}

export function SalesReportTable({ sales }: SalesReportTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nº Venta</TableHead>
            <TableHead>Fecha</TableHead>
            <TableHead>Cliente</TableHead>
            <TableHead>Estado</TableHead>
            <TableHead className="text-right">Total</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sales.map((sale) => (
            <TableRow key={sale._id}>
              <TableCell>{sale.number}</TableCell>
              <TableCell>
                {format(new Date(sale.date), 'dd/MM/yyyy HH:mm')}
              </TableCell>
              <TableCell>
                {sale.customer?.businessName || 'CONSUMIDOR FINAL'}
              </TableCell>
              <TableCell>
                <Badge variant={sale.status === 'COMPLETED' ? 'success' : 'destructive'}>
                  {SALE_STATUS_LABELS[sale.status]}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                {formatCurrency(sale.total)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { CategorySelect } from '@/features/inventory/categories/components/CategorySelect';
import { LaboratorySelect } from '@/features/inventory/laboratories/components/LaboratorySelect';
import { Checkbox } from '@/components/ui/checkbox';
import type { InventoryReportFilters } from '../types/report.types';
import { useState, useEffect } from 'react';

interface InventoryReportFiltersProps {
  filters: InventoryReportFilters;
  onFiltersChange: (filters: InventoryReportFilters) => void;
  onReset: () => void;
}

export function InventoryReportFilters({
  filters,
  onFiltersChange,
  onReset,
}: InventoryReportFiltersProps) {
  const [localSearch, setLocalSearch] = useState(filters.search || '');
  const [localCategoryId, setLocalCategoryId] = useState(filters.categoryId);
  const [localLaboratoryId, setLocalLaboratoryId] = useState(
    filters.laboratoryId
  );
  const [localStockAlert, setLocalStockAlert] = useState(filters.stockAlert);
  const [localExpirationAlert, setLocalExpirationAlert] = useState(
    filters.expirationAlert
  );
  const [localIsActive, setLocalIsActive] = useState(filters.isActive ?? true);

  useEffect(() => {
    setLocalIsActive(filters.isActive ?? true);
  }, [filters.isActive]);

  const handleReset = () => {
    setLocalSearch('');
    setLocalCategoryId(null);
    setLocalLaboratoryId(null);
    setLocalStockAlert(false);
    setLocalExpirationAlert(false);
    setLocalIsActive(true);
    onReset();
  };

  const handleApplyFilters = () => {
    onFiltersChange({
      search: localSearch.trim(),
      categoryId: localCategoryId,
      laboratoryId: localLaboratoryId,
      stockAlert: localStockAlert,
      expirationAlert: localExpirationAlert,
      isActive: localIsActive,
    });
  };

  return (
    <div className='space-y-4'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        <div className='relative'>
          <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Buscar por nombre, SKU o código...'
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleApplyFilters();
              }
            }}
            className='pl-8'
          />
        </div>

        <CategorySelect
          value={localCategoryId}
          onChange={setLocalCategoryId}
          placeholder='Filtrar por categoría'
        />

        <LaboratorySelect
          value={localLaboratoryId}
          onChange={setLocalLaboratoryId}
          placeholder='Filtrar por laboratorio'
        />
      </div>

      <div className='flex flex-wrap gap-4'>
        <div className='flex items-center space-x-2'>
          <Checkbox
            id='stockAlert'
            checked={localStockAlert}
            onCheckedChange={(checked) => setLocalStockAlert(!!checked)}
          />
          <label
            htmlFor='stockAlert'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Stock bajo mínimo
          </label>
        </div>

        <div className='flex items-center space-x-2'>
          <Checkbox
            id='expirationAlert'
            checked={localExpirationAlert}
            onCheckedChange={(checked) => setLocalExpirationAlert(!!checked)}
          />
          <label
            htmlFor='expirationAlert'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Próximos a vencer
          </label>
        </div>

        <div className='flex items-center space-x-2'>
          <Checkbox
            id='isActive'
            checked={localIsActive}
            onCheckedChange={(checked) => setLocalIsActive(!!checked)}
          />
          <label
            htmlFor='isActive'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Solo productos activos
          </label>
        </div>
      </div>

      <div className='flex justify-end gap-2'>
        <Button variant='outline' onClick={handleReset}>
          Limpiar
        </Button>
        <Button onClick={handleApplyFilters}>Aplicar filtros</Button>
      </div>
    </div>
  );
}

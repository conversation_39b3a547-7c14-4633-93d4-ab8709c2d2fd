import { Button } from '@/components/ui/button';
import { FileSpreadsheet, File } from 'lucide-react';
import { useSalesReport } from '../hooks/useSalesReport';

export function SalesReportActions() {
  const { generatePDF, exportToExcel, isExporting } = useSalesReport();

  return (
    <div className='flex justify-end space-x-2'>
      <Button variant='outline' onClick={exportToExcel} disabled={isExporting}>
        <FileSpreadsheet className='w-4 h-4 mr-2' />
        Exportar Excel
      </Button>
      <Button variant='outline' onClick={generatePDF} disabled={isExporting}>
        <File className='w-4 h-4 mr-2' />
        Exportar PDF
      </Button>
    </div>
  );
}

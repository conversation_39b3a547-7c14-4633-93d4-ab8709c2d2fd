import { useState } from 'react';
import { Search, RotateCcw } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { CustomerCombobox } from '@/features/customers/components/CustomerCombobox';
import {
  SALE_STATUS,
  SALE_STATUS_LABELS,
} from '@/features/sales/constants/sale.constants';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface SalesReportFiltersProps {
  filters: {
    search?: string;
    status?: keyof typeof SALE_STATUS | undefined;
    customerId?: string | null;
    startDate: Date | null;
    endDate: Date | null;
  };
  onFiltersChange: (
    filters: Partial<SalesReportFiltersProps['filters']>
  ) => void;
  onReset: () => void;
}

export function SalesReportFilters({
  filters,
  onFiltersChange,
  onReset,
}: SalesReportFiltersProps) {
  const [localSearch, setLocalSearch] = useState(filters.search || '');
  const [localStartDate, setLocalStartDate] = useState<Date | null>(
    filters.startDate
  );
  const [localEndDate, setLocalEndDate] = useState<Date | null>(
    filters.endDate
  );
  const [localStatus, setLocalStatus] = useState<
    keyof typeof SALE_STATUS | undefined
  >(filters.status);
  const [localCustomerId, setLocalCustomerId] = useState(filters.customerId);

  const handleReset = () => {
    setLocalSearch('');
    setLocalStartDate(null);
    setLocalEndDate(null);
    setLocalStatus(undefined);
    setLocalCustomerId(null);
    onReset();
  };

  const handleApplyFilters = () => {
    onFiltersChange({
      search: localSearch.trim(),
      startDate: localStartDate,
      endDate: localEndDate,
      status: localStatus,
      customerId: localCustomerId,
    });
  };

  return (
    <div className='space-y-6'>
      <div className='space-y-6'>
        {/* Barra de búsqueda */}
        <div className='relative'>
          <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Buscar por número de venta...'
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleApplyFilters();
              }
            }}
            className='pl-8'
          />
        </div>

        {/* Grid de filtros */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
          <div className='space-y-2'>
            <h4 className='font-medium text-sm'>Fecha inicio</h4>
            <DatePicker
              placeholder='Fecha inicio'
              value={localStartDate}
              onChange={setLocalStartDate}
            />
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium text-sm'>Fecha fin</h4>
            <DatePicker
              placeholder='Fecha fin'
              value={localEndDate}
              onChange={setLocalEndDate}
              minDate={localStartDate}
            />
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium text-sm'>Estado</h4>
            <Select
              value={localStatus || 'ALL'}
              onValueChange={(value) =>
                setLocalStatus(
                  value === 'ALL'
                    ? undefined
                    : (value as keyof typeof SALE_STATUS)
                )
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='Seleccionar estado' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='ALL'>Todos</SelectItem>
                {Object.entries(SALE_STATUS).map(([key, value]) => (
                  <SelectItem key={value} value={value}>
                    {SALE_STATUS_LABELS[value]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium text-sm'>Cliente</h4>
            <CustomerCombobox
              value={localCustomerId}
              onChange={setLocalCustomerId}
            />
          </div>
        </div>

        {/* Botones de acción */}
        <div className='flex justify-end gap-3'>
          <Button variant='outline' onClick={handleReset} className='w-[100px]'>
            <RotateCcw className='h-4 w-4 mr-2' />
            Limpiar
          </Button>
          <Button onClick={handleApplyFilters} className='w-[100px]'>
            <Search className='h-4 w-4 mr-2' />
            Buscar
          </Button>
        </div>
      </div>
    </div>
  );
}

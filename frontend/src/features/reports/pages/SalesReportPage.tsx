import { PageHeader } from '@/components/ui/page-header';
import { Pagination } from '@/components/ui/pagination';
import { LoadingPage } from '@/components/ui/loading';
import { useSalesReportPage } from '../hooks/useSalesReportPage';
import { SalesReportFilters } from '../components/SalesReportFilters';
import { SalesReportSummary } from '../components/SalesReportSummary';
import { SalesReportTable } from '../components/SalesReportTable';
import { SalesReportActions } from '../components/SalesReportActions';

export default function SalesReportPage() {
  const {
    data,
    isLoading,
    filters,
    setFilters,
    pagination,
    actions,
  } = useSalesReportPage();

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <div className="space-y-8">
      <PageHeader
        title="Reporte de Ventas"
        description="Visualiza y analiza el historial de ventas"
      />

      <SalesReportFilters
        filters={filters}
        onFiltersChange={setFilters}
        onReset={() => setFilters({})}
      />

      <SalesReportSummary summary={data.summary} />

      <SalesReportActions
        onExportExcel={actions.onExportExcel}
        onExportPDF={actions.onExportPDF}
      />

      <SalesReportTable sales={data.sales} />

      <Pagination
        currentPage={pagination.currentPage}
        totalPages={pagination.totalPages}
        onPageChange={pagination.onPageChange}
        totalRecords={pagination.totalRecords}
        pageSize={pagination.limit}
        onPageSizeChange={pagination.onLimitChange}
      />
    </div>
  );
}
import { api } from '@/lib/api/api';
import type {
  SalesReportFilters,
  SalesReportResponse,
  InventoryReportFilters,
  InventoryReportResponse,
} from '../types/report.types';
import { format as formatDate } from 'date-fns';

export const reportService = {
  async getSalesReport(
    params: SalesReportFilters
  ): Promise<SalesReportResponse> {
    const { data } = await api.get('/reports/sales', {
      params: {
        ...params,
        startDate: params.startDate
          ? formatDate(params.startDate, 'yyyy-MM-dd')
          : undefined,
        endDate: params.endDate
          ? formatDate(params.endDate, 'yyyy-MM-dd')
          : undefined,
      },
    });
    return data;
  },

  async exportSalesReport(
    params: SalesReportFilters,
    exportFormat: 'pdf' | 'excel' | 'csv'
  ) {
    const { data } = await api.get('/reports/sales/export', {
      params: {
        ...params,
        startDate: params.startDate
          ? formatDate(params.startDate, 'yyyy-MM-dd')
          : undefined,
        endDate: params.endDate
          ? formatDate(params.endDate, 'yyyy-MM-dd')
          : undefined,
        format: exportFormat,
      },
      responseType: 'blob',
    });
    return data;
  },

  async getInventoryReport(
    params: InventoryReportFilters
  ): Promise<InventoryReportResponse> {
    // Convertimos explícitamente los booleanos a strings
    const processedParams = {
      ...params,
      stockAlert: params.stockAlert ? 'true' : 'false',
      expirationAlert: params.expirationAlert ? 'true' : 'false',
      isActive: params.isActive ? 'true' : 'false',
    };

    const { data } = await api.get('/reports/inventory', {
      params: processedParams,
    });
    return data;
  },
};

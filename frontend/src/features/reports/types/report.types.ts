import { SALE_STATUS } from '@/features/sales/constants/sale.constants';
import type { Sale } from '@/features/sales/types/sale.types';

export interface SalesReportFilters {
  search?: string;
  status?: keyof typeof SALE_STATUS;
  customerId?: string | null;
  startDate: Date | null;
  endDate: Date | null;
  cashRegisterId?: string | null;
}

export interface SalesReportResponse {
  sales: Sale[];
  total: number;
  message: string;
}

export interface SalesReportSummary {
  totalAmount: number;
  totalSales: number;
  averageAmount: number;
  completedSales: number;
  canceledSales: number;
}

export interface InventoryReportFilters {
  search?: string;
  categoryId?: string | null;
  laboratoryId?: string | null;
  stockAlert?: boolean;
  expirationAlert?: boolean;
  isActive: boolean; // Cambiado de opcional a requerido
}

export interface InventoryReportSummary {
  totalProducts: number;
  totalStock: number;
  totalValue: number;
  lowStockProducts: number;
  expiringProducts: number;
  activeProducts: number;
}

export interface InventoryReportResponse {
  products: Array<{
    _id: string;
    sku: string;
    name: string;
    laboratory: {
      _id: string;
      name: string;
    };
    category: {
      _id: string;
      name: string;
    };
    price: number;
    cost: number;
    stock: number;
    minStock: number;
    maxStock: number;
    stockAlert: boolean;
    expirationAlert: boolean;
    isActive: boolean;
  }>;
  summary: InventoryReportSummary;
}

export interface ReportPermissions {
  canViewSales: boolean;
  canViewInventory: boolean;
}

export interface InventoryReportProduct {
  _id: string;
  sku: string;
  name: string;
  image?: string; // Agregamos la propiedad image como opcional
  laboratory: {
    _id: string;
    name: string;
  };
  category: {
    _id: string;
    name: string;
  };
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  stockAlert: boolean;
  expirationAlert: boolean;
  isActive: boolean;
}

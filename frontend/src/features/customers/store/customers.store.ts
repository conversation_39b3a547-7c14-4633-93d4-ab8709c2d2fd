import { create } from 'zustand';
import type { Customer } from '@/features/customers';

interface CustomersState {
  selectedCustomer: Customer | null;
  filters: {
    search: string;
    documentType?: string;
    page: number;
    limit: number;
  };
  setSelectedCustomer: (customer: Customer | null) => void;
  setFilters: (filters: CustomersState['filters']) => void;
  resetFilters: () => void;
}

const initialFilters = {
  search: '',
  documentType: undefined,
  page: 1,
  limit: 10, // Valor por defecto
};

export const useCustomersStore = create<CustomersState>()((set) => ({
  selectedCustomer: null,
  filters: initialFilters,

  setSelectedCustomer: (customer) => set({ selectedCustomer: customer }),

  setFilters: (filters) => set({ filters }),

  resetFilters: () =>
    set(() => ({
      filters: initialFilters,
    })),
}));

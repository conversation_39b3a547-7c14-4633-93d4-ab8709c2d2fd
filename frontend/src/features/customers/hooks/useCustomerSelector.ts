import { useQuery } from '@tanstack/react-query';
import { customerService } from '@/features/customers';

export function useCustomerSelector() {
  const { data, isLoading } = useQuery({
    queryKey: ['customers', 'selector'],
    queryFn: async () => {
      const response = await customerService.getAllCustomers({
        isActive: true,
      });
      return response.customers;
    },
  });

  return {
    customers: data || [],
    isLoading,
  };
}

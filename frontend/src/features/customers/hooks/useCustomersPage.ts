import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { format } from 'date-fns';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import * as XLSX from 'xlsx';
import { getImageUrl } from '@/lib/utils/image';
import type {
  Customer,
  CreateCustomerDto,
  UpdateCustomerDto,
  CustomerFilters,
  DialogState,
} from '../types/customer.types';
import { useCustomers } from './useCustomers';
import { customerService } from '../services/customer.service';

export function useCustomersPage() {
  const { toast } = useToast();
  const publicSettings = useSettingsStore((state) => state.publicSettings);
  const queryClient = useQueryClient();

  const {
    customers,
    isLoading,
    filters,
    setFilters,
    pagination,
    createMutation,
    updateMutation,
    deleteMutation,
  } = useCustomers();

  const [dialogState, setDialogState] = useState<DialogState>({
    create: false,
    delete: false,
    edit: false,
    details: false,
  });

  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );

  // Dialog handlers
  const handleDialogChange = (dialog: keyof DialogState, isOpen: boolean) => {
    setDialogState((prev) => ({ ...prev, [dialog]: isOpen }));
    if (!isOpen) setSelectedCustomer(null);
  };

  // Action handlers
  const handleCreate = async (data: CreateCustomerDto) => {
    await createMutation.mutateAsync(data);
    handleDialogChange('create', false);
  };

  const handleEdit = async (id: string, data: UpdateCustomerDto) => {
    await updateMutation.mutateAsync({ id, data });
    handleDialogChange('edit', false);
  };

  const handleDelete = async (id: string) => {
    await deleteMutation.mutateAsync(id);
    handleDialogChange('delete', false);
  };

  const handleSelectCustomer = (
    action: keyof DialogState,
    customer: Customer
  ) => {
    setSelectedCustomer(customer);
    handleDialogChange(action, true);
  };

  // Filter handlers
  const handleFiltersChange = (updates: Partial<typeof filters>) => {
    setFilters({ ...filters, ...updates, page: 1 });
  };

  const handleResetFilters = () => {
    setFilters({
      search: '',
      documentType: undefined,
      page: 1,
      limit: filters.limit,
    });
    queryClient.invalidateQueries({ queryKey: ['customers'] });
  };

  // Export handlers
  const handleExportPDF = async () => {
    try {
      const loadingToast = toast({
        title: 'Generando PDF',
        description: 'Por favor espere...',
        duration: null,
      });

      const { customers } = await customerService.getCustomersForExport();
      const publicSettings = useSettingsStore.getState().publicSettings;

      if (!customers.length) {
        toast({
          title: 'No hay clientes para exportar',
          variant: 'destructive',
        });
        loadingToast.dismiss();
        return;
      }

      // Configuración inicial del PDF
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // Configuración de márgenes y dimensiones
      const pageWidth = doc.internal.pageSize.width;
      const margins = {
        top: 20,
        bottom: 20,
        left: 15,
        right: 15,
      };
      const contentWidth = pageWidth - margins.left - margins.right;

      // Configurar fuentes
      doc.setFont('helvetica');

      // Agregar logo si existe
      if (publicSettings?.logo) {
        const img = new Image();
        img.src = getImageUrl(publicSettings.logo);
        await new Promise((resolve) => {
          img.onload = resolve;
        });
        doc.addImage(
          img,
          'PNG',
          (pageWidth - 30) / 2,
          margins.top,
          30,
          15,
          '',
          'FAST'
        );
      }

      // Título y encabezado
      let yPosition = margins.top + (publicSettings?.logo ? 20 : 0);

      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text(
        publicSettings?.systemName || 'Listado de Clientes',
        pageWidth / 2,
        yPosition,
        { align: 'center' }
      );

      yPosition += 8;
      doc.setFontSize(9);
      doc.text(
        `Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`,
        pageWidth / 2,
        yPosition,
        { align: 'center' }
      );

      yPosition += 15;

      // Configuración de la tabla
      const headers = [
        'Nombre/Razón Social',
        'Documento',
        'Email',
        'Teléfono',
        'Dirección',
      ];

      const columnWidths = {
        0: 50, // Nombre
        1: 35, // Documento
        2: 40, // Email
        3: 25, // Teléfono
        4: 40, // Dirección
      };

      // Función helper para dibujar línea
      const drawLine = (y: number) => {
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(0.1);
        doc.line(margins.left, y, pageWidth - margins.right, y);
      };

      // Encabezados de la tabla
      doc.setFillColor(240, 240, 240);
      doc.rect(margins.left, yPosition, contentWidth, 8, 'F');
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(8);

      let xPosition = margins.left;
      headers.forEach((header, i) => {
        doc.text(header, xPosition + 2, yPosition + 5);
        xPosition += columnWidths[i];
      });

      yPosition += 8;
      drawLine(yPosition);

      // Datos de la tabla
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(7);

      customers.forEach((customer, index) => {
        // Verificar si necesitamos nueva página
        if (yPosition > doc.internal.pageSize.height - margins.bottom) {
          doc.addPage();
          yPosition = margins.top;

          // Repetir encabezados en nueva página
          doc.setFillColor(240, 240, 240);
          doc.rect(margins.left, yPosition, contentWidth, 8, 'F');
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(8);

          xPosition = margins.left;
          headers.forEach((header, i) => {
            doc.text(header, xPosition + 2, yPosition + 5);
            xPosition += columnWidths[i];
          });

          yPosition += 8;
          drawLine(yPosition);
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(7);
        }

        xPosition = margins.left;

        // Datos de cada columna
        doc.text(
          customer.businessName?.substring(0, 40) || '-',
          xPosition + 2,
          yPosition + 4
        );
        xPosition += columnWidths[0];

        doc.text(
          `${customer.documentType} ${customer.documentNumber}`,
          xPosition + 2,
          yPosition + 4
        );
        xPosition += columnWidths[1];

        doc.text(
          customer.email?.substring(0, 30) || '-',
          xPosition + 2,
          yPosition + 4
        );
        xPosition += columnWidths[2];

        doc.text(customer.phone || '-', xPosition + 2, yPosition + 4);
        xPosition += columnWidths[3];

        doc.text(
          customer.address?.substring(0, 30) || '-',
          xPosition + 2,
          yPosition + 4
        );

        yPosition += 7;
        drawLine(yPosition);
      });

      // Guardar el PDF
      doc.save(`clientes-${format(new Date(), 'dd-MM-yyyy')}.pdf`);

      loadingToast.dismiss();
      toast({
        title: 'PDF generado exitosamente',
        description: 'El archivo se ha descargado correctamente',
      });
    } catch (error) {
      console.error('Error al generar PDF:', error);
      toast({
        title: 'Error al generar PDF',
        description: 'Ocurrió un error al generar el archivo',
        variant: 'destructive',
      });
    }
  };

  const handleExportExcel = async () => {
    try {
      const loadingToast = toast({
        title: 'Generando reporte',
        description: 'Por favor espere...',
        duration: null,
      });

      const { customers } = await customerService.getCustomersForExport();

      if (!customers.length) {
        toast({
          title: 'No hay clientes para exportar',
          variant: 'destructive',
        });
        loadingToast.dismiss();
        return;
      }

      const publicSettings = useSettingsStore.getState().publicSettings;

      // Preparar los datos del encabezado
      const headerData = [
        [publicSettings?.systemName || 'Reporte de Clientes'],
        [`Fecha de generación: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`],
        [], // Línea en blanco
        ['Listado de Clientes'],
        [
          'Nombre/Razón Social',
          'Tipo Documento',
          'Número Documento',
          'Email',
          'Teléfono',
          'Dirección',
          'Fecha Creación',
          'Creado Por',
        ],
      ];

      // Preparar los datos de clientes
      const customersData = customers.map((customer) => [
        customer.businessName,
        customer.documentType,
        customer.documentNumber,
        customer.email || '',
        customer.phone || '',
        customer.address || '',
        format(new Date(customer.createdAt), 'dd/MM/yyyy'),
        customer.createdBy?.username || '',
      ]);

      // Combinar y exportar
      const wsData = [...headerData, ...customersData];
      const ws = XLSX.utils.aoa_to_sheet(wsData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Clientes');

      // Ajustar estilos
      ws['!cols'] = [
        { wch: 30 }, // Nombre
        { wch: 15 }, // Tipo Doc
        { wch: 20 }, // Num Doc
        { wch: 30 }, // Email
        { wch: 15 }, // Teléfono
        { wch: 40 }, // Dirección
        { wch: 15 }, // Fecha
        { wch: 20 }, // Creado Por
      ];

      // Guardar archivo
      XLSX.writeFile(wb, `clientes-${format(new Date(), 'dd-MM-yyyy')}.xlsx`);

      loadingToast.dismiss();
      toast({
        title: 'Reporte generado',
        description: 'El reporte ha sido generado exitosamente',
      });
    } catch (error) {
      console.error('Error al generar Excel:', error);
      toast({
        title: 'Error al generar el reporte',
        description: 'Por favor intente nuevamente',
        variant: 'destructive',
      });
    }
  };

  const handleExportCSV = async () => {
    try {
      const loadingToast = toast({
        title: 'Generando CSV',
        description: 'Por favor espere...',
        duration: null,
      });

      const { customers } = await customerService.getCustomersForExport();

      if (!customers.length) {
        toast({
          title: 'No hay clientes para exportar',
          variant: 'destructive',
        });
        loadingToast.dismiss();
        return;
      }

      // Preparar los datos para CSV
      const data = customers.map((customer) => ({
        'Nombre/Razón Social': customer.businessName,
        'Tipo Documento': customer.documentType,
        'Número Documento': customer.documentNumber,
        Email: customer.email || '',
        Teléfono: customer.phone || '',
        Dirección: customer.address || '',
        'Fecha Creación': format(new Date(customer.createdAt), 'dd/MM/yyyy'),
        'Creado Por': customer.createdBy?.username || '',
      }));

      // Convertir a CSV
      const ws = XLSX.utils.json_to_sheet(data);
      const csv = XLSX.utils.sheet_to_csv(ws);

      // Crear y descargar el archivo
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `clientes-${format(new Date(), 'dd-MM-yyyy')}.csv`;
      link.click();

      loadingToast.dismiss();
      toast({
        title: 'CSV generado',
        description: 'El archivo CSV ha sido generado exitosamente',
      });
    } catch (error) {
      console.error('Error al generar CSV:', error);
      toast({
        title: 'Error al generar CSV',
        description: 'Por favor intente nuevamente',
        variant: 'destructive',
      });
    }
  };

  return {
    // Data
    customers,
    isLoading,
    isEmpty: customers.length === 0 && !isLoading,
    selectedCustomer,

    // Filters
    filters: {
      search: filters.search,
      documentType: filters.documentType,
      onSearchChange: (search: string) =>
        setFilters({ ...filters, search, page: 1 }),
      onDocumentTypeChange: (documentType: string) =>
        setFilters({ ...filters, documentType, page: 1 }),
      onReset: handleResetFilters,
    },

    // Pagination
    pagination: {
      currentPage: pagination.page,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      limit: pagination.limit,
      onPageChange: (page: number) => setFilters({ ...filters, page }),
      onLimitChange: (limit: number) =>
        setFilters({ ...filters, limit, page: 1 }),
    },

    // Actions
    actions: {
      onCreate: () => handleDialogChange('create', true),
      onEdit: (customer: Customer) => handleSelectCustomer('edit', customer),
      onDelete: (customer: Customer) =>
        handleSelectCustomer('delete', customer),
      onViewDetails: (customer: Customer) =>
        handleSelectCustomer('details', customer),
      onExportPdf: handleExportPDF,
      onExportExcel: handleExportExcel,
      onExportCSV: handleExportCSV,
    },

    // Dialogs
    dialog: {
      create: {
        open: dialogState.create,
        onOpenChange: (open: boolean) => handleDialogChange('create', open),
        onSubmit: handleCreate,
        isLoading: createMutation.isPending,
      },
      edit: {
        customer: selectedCustomer,
        open: dialogState.edit,
        onOpenChange: (open: boolean) => handleDialogChange('edit', open),
        onSubmit: handleEdit,
        isLoading: updateMutation.isPending,
      },
      delete: {
        open: dialogState.delete,
        onOpenChange: (open: boolean) => handleDialogChange('delete', open),
        onConfirm: () => selectedCustomer && handleDelete(selectedCustomer._id),
        isLoading: deleteMutation.isPending,
        customer: selectedCustomer,
      },
      details: {
        open: dialogState.details,
        onOpenChange: (open: boolean) => handleDialogChange('details', open),
        customer: selectedCustomer,
      },
    },
  };
}

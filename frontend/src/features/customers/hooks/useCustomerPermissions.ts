import { usePermissions } from '@/features/users';
import { PERMISSIONS } from '@/features/auth';

export function useCustomerPermissions() {
  const { checkPermission } = usePermissions();

  return {
    permissions: {
      canList: checkPermission(PERMISSIONS.CUSTOMERS.LIST),
      canCreate: checkPermission(PERMISSIONS.CUSTOMERS.CREATE),
      canEdit: checkPermission(PERMISSIONS.CUSTOMERS.EDIT),
      canDelete: checkPermission(PERMISSIONS.CUSTOMERS.DELETE),
    },
  };
}

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseMutationResult,
} from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { customerService } from '../services/customer.service';
import { useCustomersStore } from '../store/customers.store';
import type {
  Customer,
  CreateCustomerDto,
  UpdateCustomerDto,
  CustomerFilters,
  CustomersResponse,
} from '../types/customer.types';

// Definimos la interfaz de respuesta
interface CustomerResponse {
  customer: Customer;
  message: string;
}

interface UseCustomersReturn {
  customers: Customer[];
  isLoading: boolean;
  filters: CustomerFilters;
  setFilters: (filters: CustomerFilters) => void;
  pagination: {
    page: number;
    totalPages: number;
    totalRecords: number;
    limit: number;
  };
  createMutation: UseMutationResult<CustomerResponse, Error, CreateCustomerDto>;
  updateMutation: UseMutationResult<
    CustomerResponse,
    Error,
    { id: string; data: UpdateCustomerDto }
  >;
  deleteMutation: UseMutationResult<void, Error, string>;
}

export function useCustomers(): UseCustomersReturn {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { filters, setFilters } = useCustomersStore();

  const query = useQuery({
    queryKey: ['customers', filters],
    queryFn: () => customerService.getCustomers(filters),
  });

  const createMutation = useMutation({
    mutationFn: customerService.createCustomer,
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Cliente creado',
        description: 'El cliente ha sido creado exitosamente',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Error al crear el cliente',
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation<
    CustomerResponse,
    Error,
    { id: string; data: UpdateCustomerDto }
  >({
    mutationFn: ({ id, data }) => customerService.updateCustomer(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Cliente actualizado',
        description: 'El cliente ha sido actualizado exitosamente',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: customerService.deleteCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Cliente eliminado',
        description: 'El cliente ha sido eliminado exitosamente',
      });
    },
  });

  return {
    customers: query.data?.customers ?? [],
    isLoading: query.isLoading,
    filters,
    setFilters,
    pagination: {
      page: query.data?.currentPage ?? 1,
      totalPages: query.data?.totalPages ?? 1,
      totalRecords: query.data?.totalRecords ?? 0,
      limit: filters.limit,
    },
    createMutation,
    updateMutation,
    deleteMutation,
  };
}

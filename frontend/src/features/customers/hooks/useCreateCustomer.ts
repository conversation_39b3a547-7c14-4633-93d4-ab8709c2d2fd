import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import type { CreateCustomerDto, Customer } from '../types/customer.types';
import { customerService } from '../services/customer.service';

export function useCreateCustomer() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: customerService.createCustomer,
    onSuccess: (response) => {
      const newCustomer = response.customer;

      // Update the selector list
      queryClient.setQueryData(
        ['customers', 'selector'],
        (oldData: Customer[] = []) => [newCustomer, ...oldData]
      );

      // Invalidate the main customers list
      queryClient.invalidateQueries({ queryKey: ['customers'] });

      toast({
        title: 'Cliente creado',
        description:
          response.message || 'El cliente ha sido creado exitosamente',
      });
    },
  });

  return {
    ...mutation,
    mutateAsync: async (data: CreateCustomerDto) => {
      const response = await mutation.mutateAsync(data);
      return response.customer;
    },
  };
}

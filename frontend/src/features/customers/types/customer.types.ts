export interface Customer {
  _id: string;
  documentType: string;
  documentNumber: string;
  complement?: string;
  businessName: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  isGeneric?: boolean;
  isProtected?: boolean;
  createdBy?: {
    _id: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Re-exportamos los tipos del schema

export interface CreateCustomerDto {
  documentType: string;
  documentNumber: string;
  complement?: string;
  businessName: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  isGeneric?: boolean;
  isProtected?: boolean;
}

export interface UpdateCustomerDto extends Partial<CreateCustomerDto> {}

export interface CustomersResponse {
  customers: Customer[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface CustomerFilters {
  search: string;
  documentType?: string;
  page: number;
  limit: number;
  isActive?: boolean;
}

export interface DialogState {
  create: boolean;
  delete: boolean;
  edit: boolean;
  details: boolean;
}

export interface CustomersFiltersProps {
  search: string;
  documentType?: string;
  onSearchChange: (value: string) => void;
  onDocumentTypeChange: (value: string) => void;
  onReset: () => void;
  onExportPdf?: () => void;
  onExportExcel?: () => void;
}

export interface CustomersActionsProps {
  canCreate: boolean;
  onCreate: () => void;
  onExportPdf: () => void;
  onExportExcel: () => void;
  onExportCSV: () => void;
}

export interface CustomersHeaderProps {
  title: string;
  description?: string;
}

export interface CustomersTableProps {
  customers: Customer[];
  isLoading: boolean;
  permissions: CustomerPermissions;
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
  onViewDetails: (customer: Customer) => void;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    limit: number;
    onLimitChange: (limit: number) => void;
    onPageChange: (page: number) => void;
  };
}

export interface CustomersTableColumnProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
  onViewDetails: (customer: Customer) => void;
}

export interface CustomerPermissions {
  canList: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

export interface CustomerCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateCustomerDto) => Promise<Customer>;
  isLoading?: boolean;
}

export interface CustomerEditDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (id: string, data: UpdateCustomerDto) => Promise<void>;
  isLoading: boolean;
}

export interface CustomerDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateCustomerDto) => Promise<void>;
  isLoading?: boolean;
}

export interface DeleteCustomerDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export interface CustomerFormProps {
  customer?: Customer;
  onSubmit: (data: CreateCustomerDto) => void;
  isLoading?: boolean;
  onCancel?: () => void;
}

export interface CustomersActionsProps {
  onCreate: () => void;
  canCreate: boolean;
}

export interface CustomersFiltersProps {
  search: string;
  onSearchChange: (search: string) => void;
  onReset: () => void;
}

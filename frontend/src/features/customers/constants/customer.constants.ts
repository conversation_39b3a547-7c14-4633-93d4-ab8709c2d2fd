export const DOCUMENT_TYPES = {
  PASSPORT: 'PAS',
  IDENTITY_CARD: 'CI',
  FOREIGN_ID: 'CEX',
  TAX_ID: 'NIT',
  OTHER: 'OD',
} as const;

export const DOCUMENT_TYPES_LABELS = {
  [DOCUMENT_TYPES.PASSPORT]: 'Pasaporte',
  [DOCUMENT_TYPES.IDENTITY_CARD]: 'Cédula de Identidad',
  [DOCUMENT_TYPES.FOREIGN_ID]: 'Carnet de Extranjería',
  [DOCUMENT_TYPES.TAX_ID]: 'NIT',
  [DOCUMENT_TYPES.OTHER]: 'Otro Documento',
} as const;

export const CUSTOMER_VALIDATION = {
  FIRST_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    MESSAGE: 'El nombre debe tener entre 2 y 50 caracteres',
    REQUIRED: 'El nombre es requerido',
  },
  LAST_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    MESSAGE: 'El apellido debe tener entre 2 y 50 caracteres',
    REQUIRED: 'El apellido es requerido',
  },
  DOCUMENT_NUMBER: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 20,
    MESSAGE: 'El número de documento debe tener entre 8 y 20 caracteres',
    REQUIRED: 'El número de documento es requerido',
  },
  EMAIL: {
    MESSAGE: 'El email debe ser válido',
  },
  PHONE: {
    MIN_LENGTH: 9,
    MAX_LENGTH: 15,
    MESSAGE: 'El teléfono debe tener entre 9 y 15 caracteres',
  },
  ADDRESS: {
    MAX_LENGTH: 200,
    MESSAGE: 'La dirección no debe exceder los 200 caracteres',
  },
} as const;

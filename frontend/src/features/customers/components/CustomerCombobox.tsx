import { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useCustomerSelector } from '../hooks/useCustomerSelector';

interface CustomerComboboxProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  disabled?: boolean;
}

export function CustomerCombobox({
  value,
  onChange,
  disabled = false,
}: CustomerComboboxProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const { customers, isLoading } = useCustomerSelector();

  const selectedCustomer = customers.find((customer) => customer._id === value);

  const filteredCustomers = customers.filter(
    (customer) =>
      customer?.businessName?.toLowerCase().includes(search.toLowerCase()) ||
      customer?.documentNumber?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled || isLoading}
        >
          {selectedCustomer ? (
            <span className="truncate">
              {selectedCustomer.businessName} ({selectedCustomer.documentNumber})
            </span>
          ) : (
            <span className="text-muted-foreground">Seleccionar cliente...</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Buscar por nombre o documento..."
            value={search}
            onValueChange={setSearch}
          />
          <CommandList>
            <CommandEmpty>No se encontraron clientes.</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-[200px]">
                <CommandItem
                  value=""
                  onSelect={() => {
                    onChange(undefined);
                    setOpen(false);
                    setSearch('');
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      !value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  <span className="text-muted-foreground">Todos</span>
                </CommandItem>
                {filteredCustomers.map((customer) => (
                  <CommandItem
                    key={customer._id}
                    value={customer._id}
                    onSelect={() => {
                      onChange(customer._id);
                      setOpen(false);
                      setSearch('');
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === customer._id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex flex-col">
                      <span>{customer.businessName}</span>
                      <span className="text-sm text-muted-foreground">
                        {customer.documentType}: {customer.documentNumber}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
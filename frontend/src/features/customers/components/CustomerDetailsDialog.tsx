import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  Building,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils/format';
import type { Customer } from '@/features/customers';

interface CustomerDetailsDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const InfoCard = ({
  icon: Icon,
  title,
  children,
}: {
  icon: any;
  title: string;
  children: React.ReactNode;
}) => (
  <div className='bg-card rounded-lg p-5 space-y-2 shadow-sm hover:shadow-md transition-all duration-200 border'>
    <div className='flex items-center gap-2 text-muted-foreground mb-3'>
      <Icon className='w-4 h-4' />
      <p className='text-sm font-medium'>{title}</p>
    </div>
    {children}
  </div>
);

export function CustomerDetailsDialog({
  customer,
  open,
  onOpenChange,
}: CustomerDetailsDialogProps) {
  if (!customer) return null;

  const createdDate = new Date(customer.createdAt);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex flex-col min-h-fit max-h-[85vh] w-full sm:w-[85vw] md:w-[75vw] lg:w-[65vw] max-w-[900px] p-4 sm:p-6'>
        <DialogHeader className='pb-3 sm:pb-4 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl font-semibold'>
            <Building className='w-4 h-4 sm:w-5 sm:h-5' />
            Detalles del Cliente
          </DialogTitle>
          <p className='text-xs sm:text-sm text-muted-foreground text-left'>
            {customer.businessName}
          </p>
        </DialogHeader>

        <ScrollArea className='flex-1 overflow-y-auto'>
          <div className='space-y-4 py-4'>
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
              <InfoCard icon={Building} title='Información Principal'>
                <p className='text-lg font-medium'>{customer.businessName}</p>
                <div className='flex items-center gap-2 mt-2'>
                  <Badge variant='outline'>{customer.documentType}</Badge>
                  <span>{customer.documentNumber}</span>
                  {customer.complement && (
                    <span className='text-sm text-muted-foreground'>
                      (Complemento: {customer.complement})
                    </span>
                  )}
                </div>
              </InfoCard>

              <InfoCard icon={Mail} title='Contacto'>
                <div className='space-y-2'>
                  <div className='flex flex-col'>
                    <span className='text-sm text-muted-foreground'>Email</span>
                    <span>{customer.email || 'No especificado'}</span>
                  </div>
                  <div className='flex flex-col'>
                    <span className='text-sm text-muted-foreground'>
                      Teléfono
                    </span>
                    <span>{customer.phone || 'No especificado'}</span>
                  </div>
                </div>
              </InfoCard>
            </div>

            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
              <InfoCard icon={MapPin} title='Ubicación'>
                <div className='flex flex-col'>
                  <span className='text-sm text-muted-foreground'>
                    Dirección
                  </span>
                  <p className='text-sm'>
                    {customer.address || 'No especificada'}
                  </p>
                </div>
              </InfoCard>

              <InfoCard icon={Calendar} title='Información del Sistema'>
                <div className='space-y-2'>
                  <div className='flex flex-col'>
                    <span className='text-sm text-muted-foreground'>
                      Fecha de registro
                    </span>
                    <span>{formatDate(createdDate)}</span>
                  </div>
                  {customer.createdBy && (
                    <div className='flex flex-col'>
                      <span className='text-sm text-muted-foreground'>
                        Registrado por
                      </span>
                      <span>{customer.createdBy.username}</span>
                    </div>
                  )}
                </div>
              </InfoCard>
            </div>

            {customer.notes && (
              <InfoCard icon={FileText} title='Notas'>
                <p className='text-sm whitespace-pre-wrap'>{customer.notes}</p>
              </InfoCard>
            )}
          </div>
        </ScrollArea>

        <DialogFooter className='mt-auto pt-3 sm:pt-4 border-t shrink-0'>
          <Button
            onClick={() => onOpenChange(false)}
            className='w-full sm:w-auto'
          >
            Cerrar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

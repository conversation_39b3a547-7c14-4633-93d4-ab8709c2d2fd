import { useState } from 'react';
import { Check, ChevronsUpDown, User } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CustomerCreateDialog } from './CustomerCreateDialog';
import type { Customer, CreateCustomerDto } from '../types/customer.types';

interface CustomerSelectorProps {
  customers: Customer[];
  value?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  showCreateDialog?: boolean;
  onShowCreateDialogChange?: (show: boolean) => void;
  onCreateCustomer?: (data: CreateCustomerDto) => Promise<Customer>;
  isCreatingCustomer?: boolean;
}

export function CustomerSelector({
  customers = [],
  value,
  onChange,
  disabled = false,
  showCreateDialog = false,
  onShowCreateDialogChange,
  onCreateCustomer,
  isCreatingCustomer = false,
}: CustomerSelectorProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');

  const selectedCustomer = customers.find((customer) => customer._id === value);

  const handleCreateCustomer = async (data: CreateCustomerDto) => {
    if (onCreateCustomer) {
      try {
        const customer = await onCreateCustomer(data);
        onChange(customer._id);
        if (onShowCreateDialogChange) {
          onShowCreateDialogChange(false);
        }
        setOpen(false);
        setSearch('');
        return customer;
      } catch (error) {
        console.error('Error creating customer:', error);
        throw error;
      }
    }
  };

  const filteredCustomers = customers.filter(
    (customer) =>
      customer?.businessName?.toLowerCase().includes(search.toLowerCase()) ||
      customer?.documentNumber?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            role='combobox'
            aria-expanded={open}
            className='w-full justify-between'
            disabled={disabled}
            type='button'
          >
            {selectedCustomer ? (
              <div className='flex items-center gap-2'>
                <User className='h-4 w-4' />
                <span>{selectedCustomer.businessName}</span>
                <span className='text-muted-foreground'>
                  ({selectedCustomer.documentType}:{' '}
                  {selectedCustomer.documentNumber})
                </span>
              </div>
            ) : (
              <span>Seleccionar cliente...</span>
            )}
            <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-[var(--radix-popover-trigger-width)] p-0'>
          <Command shouldFilter={false}>
            <CommandInput
              placeholder='Buscar por nombre o documento...'
              value={search}
              onValueChange={setSearch}
            />
            <CommandList>
              <CommandEmpty>No se encontraron clientes.</CommandEmpty>
              <CommandGroup>
                <ScrollArea className='h-[200px]'>
                  {filteredCustomers.map((customer) => (
                    <CommandItem
                      key={customer._id}
                      value={customer._id}
                      onSelect={() => {
                        onChange(customer._id);
                        setOpen(false);
                        setSearch('');
                      }}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          value === customer._id ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      <div className='flex flex-col'>
                        <span>{customer.businessName}</span>
                        <span className='text-sm text-muted-foreground'>
                          {customer.documentType}: {customer.documentNumber}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </ScrollArea>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {showCreateDialog && onShowCreateDialogChange && onCreateCustomer && (
        <CustomerCreateDialog
          open={showCreateDialog}
          onOpenChange={onShowCreateDialogChange}
          onSubmit={handleCreateCustomer}
          isLoading={isCreatingCustomer}
        />
      )}
    </>
  );
}

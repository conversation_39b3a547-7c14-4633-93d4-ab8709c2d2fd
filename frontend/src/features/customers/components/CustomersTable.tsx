import { ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  CustomersTableProps,
  getCustomerTableColumns,
} from '@/features/customers';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const tableVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      when: 'beforeChildren',
      staggerChildren: 0.1,
    },
  },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.3 },
  },
};

export function CustomersTable({
  customers,
  isLoading,
  pagination,
  permissions,
  onEdit,
  onDelete,
  onViewDetails,
}: CustomersTableProps) {
  const columns = getCustomerTableColumns({
    permissions,
    onEdit,
    onDelete,
    onViewDetails,
  });

  if (isLoading) {
    return (
      <div className='flex justify-center items-center py-8'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return (
    <motion.div initial='hidden' animate='visible' variants={tableVariants}>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id} className={`text-${column.align}`}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {customers.length === 0 && (
              <TableRow>
                <TableCell colSpan={columns.length} className='text-center'>
                  No hay clientes registrados
                </TableCell>
              </TableRow>
            )}
            {customers.map((customer) => (
              <motion.tr
                key={customer._id}
                variants={rowVariants}
                className='border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted'
              >
                {columns.map((column) => (
                  <TableCell
                    key={`${customer._id}-${column.id}`}
                    className={`text-${column.align}`}
                  >
                    {column.cell(customer)}
                  </TableCell>
                ))}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className='flex items-center justify-between px-2 py-4'
        >
          <div className='flex items-center gap-2 text-sm text-muted-foreground'>
            <Select
              value={pagination.limit?.toString()}
              onValueChange={(value) => pagination.onLimitChange(Number(value))}
            >
              <SelectTrigger className='h-8 w-[70px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={pageSize.toString()}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span>registros por página</span>
          </div>

          <div className='flex items-center gap-6'>
            <span className='text-sm text-muted-foreground'>
              Total: {pagination.totalRecords} registros
            </span>
            <div className='flex items-center space-x-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  pagination.onPageChange(pagination.currentPage - 1)
                }
                disabled={pagination.currentPage <= 1}
              >
                <ChevronLeft className='h-4 w-4' />
              </Button>
              <span className='text-sm'>
                Página {pagination.currentPage} de {pagination.totalPages || 1}
              </span>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  pagination.onPageChange(pagination.currentPage + 1)
                }
                disabled={
                  !pagination.totalPages ||
                  pagination.currentPage >= pagination.totalPages
                }
              >
                <ChevronRight className='h-4 w-4' />
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

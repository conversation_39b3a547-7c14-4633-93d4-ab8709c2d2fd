import { User } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CustomerForm,
  UpdateCustomerDto,
  type CustomerEditDialogProps,
} from '@/features/customers';

export function CustomerEditDialog({
  customer,
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: CustomerEditDialogProps) {
  if (!customer) return null;

  const handleSubmit = async (data: UpdateCustomerDto) => {
    await onSubmit(customer._id, data);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <User className='w-5 h-5 text-primary' />
            Editar <PERSON>liente
          </DialogTitle>
        </DialogHeader>
        <CustomerForm
          customer={customer}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}

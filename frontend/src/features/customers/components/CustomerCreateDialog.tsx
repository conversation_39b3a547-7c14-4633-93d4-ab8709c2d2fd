import { User } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CreateCustomerDto,
  CustomerForm,
  CustomerCreateDialogProps,
} from '@/features/customers';

export function CustomerCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: CustomerCreateDialogProps) {
  const handleSubmit = async (data: CreateCustomerDto) => {
    try {
      const result = await onSubmit(data);
      onOpenChange(false);
      return result;
    } catch (error) {
      // El error ya se maneja en el nivel superior
      console.error('Error en CustomerCreateDialog:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <User className='w-5 h-5 text-primary' />
            Nuevo Cliente
          </DialogTitle>
        </DialogHeader>
        <CustomerForm
          onSubmit={handleSubmit}
          isLoading={isLoading}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}

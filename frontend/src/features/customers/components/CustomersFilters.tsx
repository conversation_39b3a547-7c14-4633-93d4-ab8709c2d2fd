import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Search, RotateCcw } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CustomersFiltersProps,
  DOCUMENT_TYPES,
  DOCUMENT_TYPES_LABELS,
} from '@/features/customers';
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip';
import { useState } from 'react';

export function CustomersFilters({
  search: externalSearch,
  documentType,
  onSearchChange,
  onDocumentTypeChange,
  onReset,
}: CustomersFiltersProps) {
  const [localSearch, setLocalSearch] = useState(externalSearch);

  const handleSearch = () => {
    onSearchChange(localSearch.trim());
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleReset = () => {
    setLocalSearch('');
    onReset();
  };

  return (
    <TooltipProvider>
      <div className='flex flex-col sm:flex-row gap-3 w-full sm:items-center'>
        <div className='w-full sm:w-[60%] flex gap-2'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <Input
              placeholder='Buscar por NIT/CI/CEX...'
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              onKeyPress={handleKeyPress}
              className='pl-10 w-full'
            />
          </div>

          {/* Search button Mobile */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handleSearch}
                variant='default'
                size='icon'
                className='sm:hidden h-10 w-10'
              >
                <Search className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Buscar</TooltipContent>
          </Tooltip>

          {/* Search button - Desktop */}
          <Button
            onClick={handleSearch}
            variant='default'
            className='hidden sm:flex items-center gap-2 px-4'
          >
            <Search className='h-4 w-4' />
            <span>Buscar</span>
          </Button>
        </div>

        {/* Filters */}
        <div className='w-full sm:w-[40%] flex gap-2'>
          <Select
            value={documentType || 'ALL'}
            onValueChange={onDocumentTypeChange}
          >
            <SelectTrigger className='flex-1'>
              <SelectValue placeholder='Tipo de documento' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='ALL'>Todos</SelectItem>
              {Object.entries(DOCUMENT_TYPES).map(([key, value]) => (
                <SelectItem key={value} value={value}>
                  {DOCUMENT_TYPES_LABELS[value]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Reset button - mobile */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='outline'
                onClick={handleReset}
                size='icon'
                className='sm:hidden h-10 w-10'
              >
                <RotateCcw className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Limpiar filtros</TooltipContent>
          </Tooltip>

          {/* Reset button - Desktop */}
          <Button
            variant='outline'
            onClick={handleReset}
            className='hidden sm:flex items-center gap-2'
          >
            <RotateCcw className='h-4 w-4' />
            <span>Limpiar</span>
          </Button>
        </div>
      </div>
    </TooltipProvider>
  );
}

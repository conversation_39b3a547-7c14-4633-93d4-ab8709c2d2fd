import { Eye, Edit, Trash, Mail, Phone } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { Customer, CustomersTableColumnProps } from '@/features/customers';

export const getCustomerTableColumns = ({
  permissions,
  onEdit,
  onDelete,
  onViewDetails,
}: CustomersTableColumnProps) => [
  {
    id: 'name',
    header: 'Nombre',
    align: 'left' as const,
    cell: (customer: Customer) => customer.businessName,
  },
  {
    id: 'documentNumber',
    header: 'Documento',
    align: 'left' as const,
    cell: (customer: Customer) => (
      <div className='flex items-center gap-2'>
        <Badge variant='outline'>{customer.documentType}</Badge>
        <span>{customer.documentNumber}</span>
      </div>
    ),
  },
  {
    id: 'contact',
    header: 'Contacto',
    align: 'left' as const,
    cell: (customer: Customer) => (
      <div className='flex flex-col gap-1'>
        {customer.email && (
          <div className='flex items-center gap-1 text-sm'>
            <Mail className='w-3 h-3 text-muted-foreground' />
            <span>{customer.email}</span>
          </div>
        )}
        {customer.phone && (
          <div className='flex items-center gap-1 text-sm'>
            <Phone className='w-3 h-3 text-muted-foreground' />
            <span>{customer.phone}</span>
          </div>
        )}
      </div>
    ),
  },
  {
    id: 'actions',
    header: 'Acciones',
    align: 'right' as const,
    cell: (customer: Customer) => (
      <div className='flex items-center gap-2 justify-end'>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => onViewDetails(customer)}
              >
                <Eye className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Ver detalles</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {permissions.canEdit && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onEdit(customer)}
                >
                  <Edit className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Editar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {permissions.canDelete && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onDelete(customer)}
                  className='text-red-600 hover:text-red-700 dark:text-rose-500 dark:hover:text-rose-400 hover:bg-red-500/10 dark:hover:bg-rose-500/20'
                >
                  <Trash className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Eliminar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    ),
  },
];

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';
import {
  createCustomerSchema,
  CustomerFormProps,
  DOCUMENT_TYPES,
  DOCUMENT_TYPES_LABELS,
  type CreateCustomerDto,
} from '@/features/customers';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils/styles';

export function CustomerForm({
  customer,
  onSubmit,
  isLoading,
  onCancel,
}: CustomerFormProps) {
  const form = useForm<CreateCustomerDto>({
    resolver: zodResolver(createCustomerSchema),
    defaultValues: {
      documentType: customer?.documentType || '',
      documentNumber: customer?.documentNumber || '',
      complement: customer?.complement || '',
      businessName: customer?.businessName || '',
      phone: customer?.phone || '',
      email: customer?.email || '',
      address: customer?.address || '',
      notes: customer?.notes || '',
    },
  });

  const handleSubmit = async (data: CreateCustomerDto) => {
    try {
      await onSubmit(data);
      form.reset(); // Resetear el formulario
      onCancel?.(); // Cerrar el diálogo
    } catch (error: any) {
      const serverErrors = error.response?.data?.errors;
      if (serverErrors) {
        Object.keys(serverErrors).forEach((key) => {
          form.setError(key as keyof CreateCustomerDto, {
            type: 'server',
            message: serverErrors[key],
          });
        });
      }
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
        <Card className='border-none shadow-none'>
          <CardContent className='p-0 space-y-4'>
            {/* Tipo de Documento en una sola fila */}
            <FormField
              control={form.control}
              name='documentType'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Tipo de Documento'
                    required
                    error={!!form.formState.errors.documentType}
                  />
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className='focus-visible:ring-offset-0'>
                        <SelectValue placeholder='Seleccione tipo de documento' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(DOCUMENT_TYPES).map(([key, value]) => (
                        <SelectItem key={value} value={value}>
                          {DOCUMENT_TYPES_LABELS[value]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Número de Documento y Complemento en una fila */}
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='md:col-span-2'>
                <FormField
                  control={form.control}
                  name='documentNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabelWithTooltip
                        label='Número de Documento'
                        required
                        error={!!form.formState.errors.documentNumber}
                      />
                      <FormControl>
                        <Input
                          {...field}
                          className='focus-visible:ring-offset-0'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className='md:col-span-1'>
                <FormField
                  control={form.control}
                  name='complement'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        className={cn(
                          !!form.formState.errors.complement &&
                            'text-destructive'
                        )}
                      >
                        Complemento
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className='focus-visible:ring-offset-0'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name='businessName'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Nombre o Razón Social'
                    tooltip='Nombre completo o razón social del cliente'
                    required
                    error={!!form.formState.errors.businessName}
                  />
                  <FormControl>
                    <Input {...field} className='focus-visible:ring-offset-0' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Separator />

        {/* Información de Contacto */}
        <Card className='border-none shadow-none'>
          <CardContent className='p-0 space-y-4'>
            <div className='flex items-center gap-2 text-lg font-semibold text-primary'>
              Información de Contacto
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='phone'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        !!form.formState.errors.phone && 'text-destructive'
                      )}
                    >
                      Teléfono
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className='focus-visible:ring-offset-0'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        !!form.formState.errors.email && 'text-destructive'
                      )}
                    >
                      Email
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='email'
                        {...field}
                        className='focus-visible:ring-offset-0'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='address'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      !!form.formState.errors.address && 'text-destructive'
                    )}
                  >
                    Dirección
                  </FormLabel>
                  <FormControl>
                    <Input {...field} className='focus-visible:ring-offset-0' />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Notas */}
        <Card className='border-none shadow-none'>
          <CardContent className='p-0 space-y-4'>
            <div className='flex items-center gap-2 text-lg font-semibold text-primary'>
              Notas
            </div>
            <FormField
              control={form.control}
              name='notes'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      !!form.formState.errors.notes && 'text-destructive'
                    )}
                  >
                    Notas adicionales
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      className='focus-visible:ring-offset-0'
                      placeholder='Ingrese notas o comentarios adicionales'
                      rows={4}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className='flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2'>
          <Button
            type='button'
            variant='outline'
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? 'Creando...' : customer ? 'Actualizar' : 'Crear'}{' '}
            Cliente
          </Button>
        </div>
      </form>
    </Form>
  );
}

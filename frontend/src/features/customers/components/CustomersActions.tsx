import { Button } from '@/components/ui/button';
import { Download, FileIcon } from 'lucide-react';
import { CustomersActionsProps } from '@/features/customers';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function CustomersActions({
  canCreate,
  onCreate,
  onExportPdf,
  onExportExcel,
  onExportCSV,
}: CustomersActionsProps) {
  return (
    <TooltipProvider>
      <div className='flex items-center sm:justify-end justify-center w-full gap-2'>
        {/* Dropdown de exportación */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' className='flex items-center gap-2'>
              <Download className='h-4 w-4' />
              <span>Exportar</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={onExportPdf}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportExcel}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportCSV}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar CSV
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Botón de crear */}
        {canCreate && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={onCreate}>Crear Cliente</Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Crear nuevo cliente</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}

import { api } from '@/lib/api/api';
import type {
  Customer,
  CustomersResponse,
  CreateCustomerDto,
  UpdateCustomerDto,
} from '@/features/customers';

interface CustomerResponse {
  message: string;
  customer: Customer;
}

export const customerService = {
  getCustomers: async (params?: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<CustomersResponse> => {
    const response = await api.get('/customers', { params });
    return response.data;
  },

  createCustomer: async (
    customerData: CreateCustomerDto
  ): Promise<CustomerResponse> => {
    const response = await api.post<CustomerResponse>(
      '/customers',
      customerData
    );
    return response.data;
  },

  updateCustomer: async (
    id: string,
    customerData: UpdateCustomerDto
  ): Promise<CustomerResponse> => {
    const response = await api.patch<CustomerResponse>(
      `/customers/${id}`,
      customerData
    );
    return response.data;
  },

  deleteCustomer: async (id: string): Promise<void> => {
    await api.delete(`/customers/${id}`);
  },

  getCustomerById: async (id: string): Promise<Customer> => {
    const response = await api.get(`/customers/${id}`);
    return response.data;
  },

  async getCustomersForExport() {
    const response = await api.get<{ customers: Customer[] }>(
      '/customers/export'
    );
    return response.data;
  },

  getActiveCustomers: async () => {
    const response = await api.get<CustomersResponse>('/customers/active');
    return response.data;
  },

  getAllCustomers: async (params?: { isActive?: boolean }) => {
    const { data } = await api.get('/customers/all', { params });
    return data;
  },
};

import { api } from '@/lib/api/api';

export const userService = {
  getUsers: async (params) => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  createUser: async (userData) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  deleteUser: async (userId) => {
    const response = await api.delete(`/users/${userId}`);
    return response.data;
  },

  updateUser: async (userId, userData) => {
    const response = await api.patch(`/users/${userId}`, userData);
    return response.data;
  },

  changePassword: async (userId, passwordData) => {
    const response = await api.patch(
      `/users/${userId}/change-password`,
      passwordData
    );
    return response.data;
  },

  updatePermissions: async (userId, permissions) => {
    const response = await api.patch(`/users/${userId}/permissions`, {
      permissions,
    });
    return response.data;
  },

  toggleStatus: async (userId, isActive) => {
    const response = await api.patch(`/users/${userId}/status`, { isActive });
    return response.data;
  },
};

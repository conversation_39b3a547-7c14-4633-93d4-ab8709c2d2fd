import { z } from 'zod';
import { USER_ROLES } from '@/features/auth';
import { USER_VALIDATION_CONSTANTS } from '@/features/users';

export const createUserSchema = z.object({
  username: z
    .string()
    .min(USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH, {
      message: `El usuario debe tener al menos ${USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH} caracteres`,
    })
    .max(USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH, {
      message: `El usuario no puede tener más de ${USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH} caracteres`,
    }),
  password: z
    .string()
    .min(USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH, {
      message: `La contraseña debe tener al menos ${USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH} caracteres`,
    })
    .max(USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH, {
      message: `La contraseña no puede tener más de ${USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH} caracteres`,
    }),
  role: z.enum([USER_ROLES.ADMIN, USER_ROLES.CASHIER], {
    errorMap: () => ({ message: 'Rol inválido' }),
  }),
});

export const editUserSchema = z.object({
  username: z
    .string()
    .min(USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH, {
      message: `El usuario debe tener al menos ${USER_VALIDATION_CONSTANTS.USERNAME_MIN_LENGTH} caracteres`,
    })
    .max(USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH, {
      message: `El usuario no puede tener más de ${USER_VALIDATION_CONSTANTS.USERNAME_MAX_LENGTH} caracteres`,
    }),
  role: z.enum([USER_ROLES.ADMIN, USER_ROLES.CASHIER], {
    errorMap: () => ({ message: 'Rol inválido' }),
  }),
});

export const changePasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH, {
        message: `La contraseña debe tener al menos ${USER_VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH} caracteres`,
      })
      .max(USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH, {
        message: `La contraseña no puede tener más de ${USER_VALIDATION_CONSTANTS.PASSWORD_MAX_LENGTH} caracteres`,
      }),
    confirmPassword: z.string().min(1, { message: 'Confirma tu contraseña' }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  });

export type CreateUserSchema = z.infer<typeof createUserSchema>;
export type EditUserSchema = z.infer<typeof editUserSchema>;
export type ChangePasswordSchema = z.infer<typeof changePasswordSchema>;

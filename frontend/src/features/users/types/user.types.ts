import { Permission, UserRole } from '@/features/auth';
import { CreateUserSchema } from '../schemas/user.schema';
export interface User {
  _id: string;
  username: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
  permissions: Permission[];
  isSuperAdmin?: boolean;
  isActive: boolean;
}

export interface CreateUserDto {
  username: string;
  password: string;
  role: UserRole;
  permissions: string[];
}
export interface UsersFiltersProps {
  search: string;
  selectedRole: string;
  onSearchChange: (value: string) => void;
  onRoleChange: (value: string) => void;
  onReset: () => void;
}

export interface UsersActionsProps {
  canCreate: boolean;
  onCreate: () => void;
}

export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
}

export interface PageHeaderProps {
  title: string;
  description?: string;
}
export interface UsersTableProps {
  users: User[];
  isLoading: boolean;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
  };
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canManagePermissions: boolean;
    canChangePassword: boolean;
  };
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onChangePassword: (user: User) => void;
  onManagePermissions: (user: User) => void;
  onViewDetails: (user: User) => void;
  onToggleStatus: (user: User) => void;
}

export interface UsersTableColumnProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canManagePermissions: boolean;
    canChangePassword: boolean;
  };
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onChangePassword: (user: User) => void;
  onManagePermissions: (user: User) => void;
  onViewDetails: (user: User) => void;
  onToggleStatus: (user: User) => void;
}

export interface UserPermissionsDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSavePermissions: (userId: string, permissions: string[]) => Promise<void>;
  isLoading?: boolean;
}

export interface UserEditDialogProps {
  user: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (
    userId: string,
    userData: { username: string; role: UserRole }
  ) => Promise<void>;
  isLoading?: boolean;
}

export interface UserCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateUserSchema) => void;
  isLoading: boolean;
}

export interface UserPasswordDialogProps {
  user: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (userId: string, password: string) => Promise<void>;
  isLoading?: boolean;
}

export interface UserDetailsDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface DeleteUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading: boolean;
}

export interface UserStatusDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

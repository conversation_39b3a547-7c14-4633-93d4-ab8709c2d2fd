import { User, CreateUserSchema } from '@/features/users';
import { UserRole } from '@/features/auth';

export interface UserPermissionsDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSavePermissions: (userId: string, permissions: string[]) => Promise<void>;
  isLoading?: boolean;
}

export interface UserEditDialogProps {
  user: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (
    userId: string,
    userData: { username: string; role: UserRole }
  ) => Promise<void>;
  isLoading?: boolean;
}

export interface UserCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateUserSchema) => void;
  isLoading: boolean;
}

export interface UserPasswordDialogProps {
  user: User | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (userId: string, password: string) => Promise<void>;
  isLoading?: boolean;
}

export interface UserDetailsDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface DeleteUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading: boolean;
}

export interface UserStatusDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

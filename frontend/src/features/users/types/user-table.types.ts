import { User } from '@/features/users';

export interface UsersTableProps {
  users: User[];
  isLoading: boolean;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
  };
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canManagePermissions: boolean;
    canChangePassword: boolean;
  };
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onChangePassword: (user: User) => void;
  onManagePermissions: (user: User) => void;
  onViewDetails: (user: User) => void;
  onToggleStatus: (user: User) => void;
}

export interface UsersTableColumnProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canManagePermissions: boolean;
    canChangePassword: boolean;
  };
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onChangePassword: (user: User) => void;
  onManagePermissions: (user: User) => void;
  onViewDetails: (user: User) => void;
  onToggleStatus: (user: User) => void;
}

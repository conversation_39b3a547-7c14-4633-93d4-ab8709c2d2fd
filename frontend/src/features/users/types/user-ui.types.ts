export interface UsersFiltersProps {
  search: string;
  selectedRole: string;
  onSearchChange: (value: string) => void;
  onRoleChange: (value: string) => void;
  onReset: () => void;
}

export interface UsersActionsProps {
  canCreate: boolean;
  onCreate: () => void;
}

export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
}

export interface PageHeaderProps {
  title: string;
  description?: string;
}

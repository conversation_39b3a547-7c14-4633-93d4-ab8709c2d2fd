import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import {
  userService,
  useUsersStore,
  usePermissions,
  type CreateUserDto,
} from '@/features/users';
import type { UserRole } from '@/features/auth';

export function useUsers() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { checkPermission } = usePermissions();
  const { filters, setFilters } = useUsersStore();

  // Query principal
  const { data, isLoading } = useQuery({
    queryKey: ['users', filters],
    queryFn: async () => {
      try {
        return await userService.getUsers({
          search: filters.search,
          role: filters.role === 'ALL' ? '' : filters.role,
          page: filters.page,
          limit: 10,
        });
      } catch (error: any) {
        toast({
          title: 'Error',
          description: 'Error al cargar usuarios',
          variant: 'destructive',
        });
        throw error;
      }
    },
  });

  const users = data?.users || [];
  const totalPages = data?.totalPages || 1;
  const totalUsers = data?.totalRecords || 0;

  // Mutations
  const createMutation = useMutation({
    mutationFn: (data: CreateUserDto) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Usuario creado',
        description: 'El usuario ha sido creado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al crear usuario',
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (userId: string) => userService.deleteUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Usuario eliminado',
        description: 'El usuario ha sido eliminado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al eliminar usuario',
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: {
      userId: string;
      userData: { username: string; role: UserRole };
    }) => userService.updateUser(data.userId, data.userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Usuario actualizado',
        description: 'Los datos del usuario han sido actualizados exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al actualizar usuario',
        variant: 'destructive',
      });
    },
  });

  const changePasswordMutation = useMutation({
    mutationFn: (data: {
      userId: string;
      newPassword: string;
      confirmPassword: string;
    }) => userService.changePassword(data.userId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Contraseña actualizada',
        description: 'La contraseña ha sido actualizada exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al cambiar la contraseña',
        variant: 'destructive',
      });
    },
  });

  const updatePermissionsMutation = useMutation({
    mutationFn: (data: { userId: string; permissions: string[] }) =>
      userService.updatePermissions(data.userId, data.permissions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Permisos actualizados',
        description: 'Los permisos han sido actualizados exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al actualizar permisos',
        variant: 'destructive',
      });
    },
  });

  const toggleStatusMutation = useMutation({
    mutationFn: (data: { userId: string; isActive: boolean }) =>
      userService.toggleStatus(data.userId, data.isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast({
        title: 'Estado actualizado',
        description: 'El estado ha sido actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: 'Error al actualizar estado',
        variant: 'destructive',
      });
    },
  });

  return {
    users,
    isLoading,
    createMutation,
    deleteMutation,
    updateMutation,
    changePasswordMutation,
    updatePermissionsMutation,
    toggleStatusMutation,
    checkPermission,
    filters,
    setFilters,
    pagination: {
      totalPages,
      totalRecords: totalUsers,
    },
  };
}

import { useState } from 'react';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/features/auth';
import { useUsers } from '@/features/users/hooks/useUsers';
import type { User, CreateUserDto } from '@/features/users/types/user.types';
import { PERMISSIONS, UserRole } from '@/features/auth';
import { useQueryClient } from '@tanstack/react-query';

export function useUsersPage() {
  const { user: currentUser } = useAuth();
  const { toast } = useToast();
  const {
    users,
    isLoading,
    createMutation,
    deleteMutation,
    updateMutation,
    changePasswordMutation,
    updatePermissionsMutation,
    toggleStatusMutation,
    checkPermission,
    filters,
    setFilters,
    pagination,
  } = useUsers();
  const queryClient = useQueryClient();

  // States
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedUserForDetails, setSelectedUserForDetails] =
    useState<User | null>(null);
  const [editDialogUser, setEditDialogUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [selectedUserForPassword, setSelectedUserForPassword] =
    useState<User | null>(null);
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [selectedUserForPermissions, setSelectedUserForPermissions] =
    useState<User | null>(null);
  const [selectedUserForStatus, setSelectedUserForStatus] =
    useState<User | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  // Handlers
  const handleCreate = () => setIsDialogOpen(true);

  const handleDelete = (user: User) => {
    if (user.isSuperAdmin) {
      toast({
        title: 'Acción no permitida',
        description:
          'No es posible eliminar al administrador principal del sistema.',
        variant: 'destructive',
      });
      return;
    }

    // Verificar si el usuario intenta eliminarse a sí mismo
    if (currentUser?._id === user._id) {
      toast({
        title: 'Acción no permitida',
        description: 'No es posible eliminarse a sí mismo.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const handleEdit = (user: User) => {
    if (user.isSuperAdmin) {
      toast({
        title: 'Acción no permitida',
        description:
          'No es posible modificar al administrador principal del sistema.',
        variant: 'destructive',
      });
      return;
    }

    // Verify if the user is trying to edit themselves
    if (currentUser?._id === user._id) {
      toast({
        title: 'Acción no permitida',
        description: 'No es posible modificar tu propio usuario.',
        variant: 'destructive',
      });
      return;
    }

    setEditDialogUser(user);
    setIsEditDialogOpen(true);
  };

  const handleChangePassword = (user: User) => {
    // If the current user is superadmin, they can change any password
    if (currentUser?.isSuperAdmin) {
      setSelectedUserForPassword(user);
      setIsPasswordDialogOpen(true);
      return;
    }

    // If the target user is superadmin and the current user is not superadmin
    if (user.isSuperAdmin) {
      toast({
        title: 'Acción no permitida',
        description:
          'No es posible modificar la contraseña del administrador principal del sistema.',
        variant: 'destructive',
      });
      return;
    }

    // If the current user is trying to change their own password
    if (currentUser?._id === user._id) {
      setSelectedUserForPassword(user);
      setIsPasswordDialogOpen(true);
      return;
    }

    setSelectedUserForPassword(user);
    setIsPasswordDialogOpen(true);
  };

  const handleViewDetails = (user: User) => {
    setSelectedUserForDetails(user);
    setIsDetailsDialogOpen(true);
  };

  const handleManagePermissions = (user: User) => {
    if (user.isSuperAdmin) {
      toast({
        title: 'Acción no permitida',
        description:
          'No es posible modificar los permisos del administrador principal.',
        variant: 'destructive',
      });
      return;
    }
    setSelectedUserForPermissions(user);
    setIsPermissionsDialogOpen(true);
  };

  const handleToggleStatus = (user: User) => {
    if (user.isSuperAdmin) {
      toast({
        title: 'Acción no permitida',
        description:
          'No es posible cambiar el estado del administrador principal.',
        variant: 'destructive',
      });
      return;
    }

    // Verify if the user is trying to deactivate themselves
    if (currentUser?._id === user._id) {
      toast({
        title: 'Acción no permitida',
        description: 'No es posible cambiar tu propio estado.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedUserForStatus(user);
    setIsStatusDialogOpen(true);
  };

  // Submit handlers
  const handleSubmitCreate = async (data: CreateUserDto) => {
    await createMutation.mutateAsync(data);
  };

  const handleConfirmDelete = async () => {
    if (!selectedUser) return;
    try {
      await deleteMutation.mutateAsync(selectedUser._id);
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  const editDialog = {
    user: editDialogUser,
    isOpen: isEditDialogOpen,
    onOpenChange: setIsEditDialogOpen,
    onUpdate: async (
      userId: string,
      userData: { username: string; role: UserRole }
    ) => {
      setIsEditLoading(true);
      try {
        await updateMutation.mutateAsync({ userId, userData });
      } finally {
        setIsEditLoading(false);
      }
    },
    isLoading: isEditLoading,
  };

  const handleSubmitChangePassword = async (
    userId: string,
    newPassword: string,
    confirmPassword: string
  ) => {
    await changePasswordMutation.mutateAsync({
      userId,
      newPassword,
      confirmPassword,
    });
  };

  const handleSavePermissions = async (
    userId: string,
    permissions: string[]
  ) => {
    await updatePermissionsMutation.mutateAsync({
      userId,
      permissions,
    });
  };

  const handleConfirmToggleStatus = async () => {
    if (!selectedUserForStatus) return;
    await toggleStatusMutation.mutateAsync({
      userId: selectedUserForStatus._id,
      isActive: !selectedUserForStatus.isActive,
    });
  };

  const canEditUser = (user: User) => {
    if (user.isSuperAdmin) return false;
    return checkPermission(PERMISSIONS.USERS.EDIT);
  };

  const resetFiltersAndReload = () => {
    setFilters({
      search: '',
      role: 'ALL',
      page: 1,
    });
    // Invalidate the query to force a reload
    queryClient.invalidateQueries({ queryKey: ['users'] });
  };

  return {
    users,
    isLoading,

    filters: {
      search: filters.search,
      setSearch: (search: string) =>
        setFilters({ ...filters, search, page: 1 }),
      selectedRole: filters.role,
      setSelectedRole: (role: string) =>
        setFilters({ ...filters, role, page: 1 }),
      resetFiltersAndReload,
    },

    pagination: {
      currentPage: filters.page,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      onPageChange: (page: number) => {
        setFilters({ ...filters, page });
      },
    },

    actions: {
      handlers: {
        onCreate: handleCreate,
        onDelete: handleDelete,
        onEdit: handleEdit,
        onChangePassword: handleChangePassword,
        onViewDetails: handleViewDetails,
        onManagePermissions: handleManagePermissions,
        onSavePermissions: handleSavePermissions,
        onToggleStatus: handleToggleStatus,
      },
      permissions: {
        canCreate: checkPermission(PERMISSIONS.USERS.CREATE),
        canEdit: checkPermission(PERMISSIONS.USERS.EDIT),
        canDelete: checkPermission(PERMISSIONS.USERS.DELETE),
        canChangePassword: checkPermission(PERMISSIONS.USERS.CHANGE_PASSWORD),
        canManagePermissions: checkPermission(PERMISSIONS.USERS.EDIT),
        canEditUser,
      },
    },

    dialog: {
      isOpen: isDialogOpen,
      onOpenChange: setIsDialogOpen,
      onSubmit: handleSubmitCreate,
      isLoading: createMutation.isPending,
    },

    deleteDialog: {
      user: selectedUser,
      isOpen: isDeleteDialogOpen,
      onOpenChange: setIsDeleteDialogOpen,
      onConfirm: handleConfirmDelete,
      isLoading: deleteMutation.isPending,
    },

    detailsDialog: {
      user: selectedUserForDetails,
      isOpen: isDetailsDialogOpen,
      onOpenChange: setIsDetailsDialogOpen,
    },

    editDialog,

    passwordDialog: {
      user: selectedUserForPassword,
      isOpen: isPasswordDialogOpen,
      onOpenChange: setIsPasswordDialogOpen,
      onSubmit: handleSubmitChangePassword,
      isLoading: changePasswordMutation.isPending,
    },

    permissionsDialog: {
      user: selectedUserForPermissions,
      isOpen: isPermissionsDialogOpen,
      onOpenChange: setIsPermissionsDialogOpen,
      onSubmit: handleSavePermissions,
      isLoading: updatePermissionsMutation.isPending,
    },

    statusDialog: {
      user: selectedUserForStatus,
      isOpen: isStatusDialogOpen,
      onOpenChange: setIsStatusDialogOpen,
      onConfirm: handleConfirmToggleStatus,
      isLoading: toggleStatusMutation.isPending,
    },
  };
}

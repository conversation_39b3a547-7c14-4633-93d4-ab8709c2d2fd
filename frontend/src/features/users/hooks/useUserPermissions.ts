import { useAuth, PERMISSIONS } from '@/features/auth';

export function usePermissions() {
  const { checkPermission } = useAuth();

  return {
    checkPermission,
    permissions: {
      canList: checkPermission(PERMISSIONS.USERS.LIST),
      canCreate: checkPermission(PERMISSIONS.USERS.CREATE),
      canEdit: checkPermission(PERMISSIONS.USERS.EDIT),
      canDelete: checkPermission(PERMISSIONS.USERS.DELETE),
      canManagePermissions: checkPermission(PERMISSIONS.USERS.EDIT),
      canChangePassword: checkPermission(PERMISSIONS.USERS.EDIT),
    },
  };
}

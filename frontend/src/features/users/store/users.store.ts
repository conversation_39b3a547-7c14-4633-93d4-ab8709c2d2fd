import { create } from 'zustand';
import type { User } from '@/features/users';

interface UsersState {
  selectedUser: User | null;
  setSelectedUser: (user: User | null) => void;
  filters: {
    search: string;
    role: string;
    page: number;
  };
  setFilters: (filters: Partial<UsersState['filters']>) => void;
  resetFilters: () => void;
}

export const useUsersStore = create<UsersState>((set) => ({
  selectedUser: null,
  setSelectedUser: (user) => set({ selectedUser: user }),
  filters: {
    search: '',
    role: 'ALL',
    page: 1,
  },
  setFilters: (newFilters) =>
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    })),
  resetFilters: () =>
    set({
      filters: {
        search: '',
        role: 'ALL',
        page: 1,
      },
    }),
}));

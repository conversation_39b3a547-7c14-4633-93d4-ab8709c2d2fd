import { Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { UsersActionsProps } from '@/features/users';

export function UsersActions({ canCreate, onCreate }: UsersActionsProps) {
  return (
    <TooltipProvider>
      <div className='flex items-center sm:justify-end justify-center w-full'>
        {canCreate && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={onCreate}
                  size='icon'
                  className='sm:hidden h-10 w-10'
                >
                  <Plus className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Nuevo Usuario</TooltipContent>
            </Tooltip>

            {/* Botón desktop con texto */}
            <Button
              onClick={onCreate}
              className='hidden sm:flex items-center gap-2'
            >
              <Plus className='h-4 w-4' />
              <span>Nuevo Usuario</span>
            </Button>
          </>
        )}
      </div>
    </TooltipProvider>
  );
}

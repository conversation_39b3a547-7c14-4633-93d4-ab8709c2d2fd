import {
  User as UserIcon,
  Shield,
  Calendar,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  PERMISSION_DISPLAY_NAMES,
  PERMISSIONS,
  ROLE_DISPLAY_NAMES,
  USER_ROLES,
} from '@/features/auth';
import { UserDetailsDialogProps } from '@/features/users';
import { useMemo } from 'react';

// Mover fuera del componente ya que es constante
const PERMISSION_GROUPS = {
  Inventario: [
    PERMISSIONS.INVENTORY.PRODUCTS.LIST,
    PERMISSIONS.INVENTORY.PRODUCTS.CREATE,
    PERMISSIONS.INVENTORY.PRODUCTS.EDIT,
    PERMISSIONS.INVENTORY.PRODUCTS.DELETE,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.CREATE,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.EDIT,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.DELETE,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.EDIT,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.DELETE,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.EDIT,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.DELETE,
  ],
  Ventas: [
    PERMISSIONS.SALES.LIST,
    PERMISSIONS.SALES.CREATE,
    PERMISSIONS.SALES.EDIT,
    PERMISSIONS.SALES.DELETE,
    PERMISSIONS.SALES.CANCEL,
  ],
  Clientes: [...Object.values(PERMISSIONS.CUSTOMERS)],
  'Entidades Base': [
    ...Object.values(PERMISSIONS.CATEGORIES),
    ...Object.values(PERMISSIONS.LABORATORIES),
  ],
  Caja: [...Object.values(PERMISSIONS.CASH)],
  Sistema: [
    ...Object.values(PERMISSIONS.USERS),
    PERMISSIONS.REPORTS.SALES.LIST,
    PERMISSIONS.REPORTS.INVENTORY.LIST,
    ...Object.values(PERMISSIONS.SETTINGS),
  ],
} as const;

// Componentes más pequeños para mejor organización
const UserInfoCard = ({
  icon: Icon,
  label,
  value,
}: {
  icon: any;
  label: string;
  value: string;
}) => (
  <div className='bg-card rounded-lg p-5 space-y-2 shadow-sm hover:shadow-md transition-all duration-200 border'>
    <div className='flex items-center gap-2 text-muted-foreground mb-3'>
      <Icon className='w-4 h-4' />
      <p className='text-sm font-medium'>{label}</p>
    </div>
    <p className='text-lg font-medium'>{value}</p>
  </div>
);

const PermissionItem = ({
  permission,
  hasPermission,
}: {
  permission: string;
  hasPermission: boolean;
}) => {
  const displayName = PERMISSION_DISPLAY_NAMES[permission];
  if (!displayName) return null;

  return (
    <div className='flex items-center justify-between bg-muted/30 px-2.5 py-1.5 rounded-md hover:bg-muted/50 transition-all duration-200'>
      <span className='text-xs flex-grow mr-2'>{displayName}</span>
      {hasPermission ? (
        <CheckCircle2 className='text-primary h-3.5 w-3.5 flex-shrink-0' />
      ) : (
        <XCircle className='text-muted-foreground h-3.5 w-3.5 opacity-50 flex-shrink-0' />
      )}
    </div>
  );
};

export function UserDetailsDialog({
  user,
  open,
  onOpenChange,
}: UserDetailsDialogProps) {
  if (!user) return null;

  const hasAllPermissions = user.permissions.includes('*');
  const isAdmin = user.role === USER_ROLES.ADMIN;

  // Memoizar la fecha para evitar recálculos innecesarios
  const formattedDate = useMemo(() => {
    const date = new Date(user.createdAt);
    return date.toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }, [user.createdAt]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[85vh] overflow-hidden w-[95vw] md:w-[90vw] lg:w-[85vw] xl:w-[70vw] max-w-[1200px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-xl font-semibold'>
            <UserIcon className='w-5 h-5' />
            Detalles del Usuario - {user.username}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className='h-full max-h-[calc(85vh-10rem)]'>
          <div className='space-y-6 p-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <UserInfoCard
                icon={UserIcon}
                label='Nombre de usuario'
                value={user.username}
              />
              <div className='bg-card rounded-lg p-5 space-y-2 shadow-sm hover:shadow-md transition-all duration-200 border'>
                <div className='flex items-center gap-2 text-muted-foreground mb-3'>
                  <Shield className='w-4 h-4' />
                  <p className='text-sm font-medium'>Rol</p>
                </div>
                <div className='flex items-center gap-2'>
                  <Badge
                    variant={isAdmin ? 'default' : 'secondary'}
                    className='text-sm'
                  >
                    {ROLE_DISPLAY_NAMES[user.role]}
                  </Badge>
                </div>
              </div>
              <UserInfoCard
                icon={Calendar}
                label='Fecha de Creación'
                value={formattedDate}
              />
            </div>

            {/* Sección de permisos */}
            <div className='space-y-4'>
              <div className='flex items-center justify-between gap-2 border-b pb-2'>
                <div className='flex items-center gap-2'>
                  <Shield className='w-5 h-5 text-primary' />
                  <h3 className='text-xl font-semibold'>
                    Permisos del Usuario
                  </h3>
                </div>
                {hasAllPermissions && (
                  <Badge
                    variant='outline'
                    className='bg-primary/10 text-primary border-primary/20'
                  >
                    Acceso Completo
                  </Badge>
                )}
              </div>

              {hasAllPermissions ? (
                <div className='bg-card rounded-lg p-4 space-y-3 shadow-sm border'>
                  <div className='flex items-center gap-2'>
                    <ShieldAlert className='w-5 h-5 text-primary' />
                    <p className='text-lg font-medium'>
                      Este usuario tiene acceso completo al sistema
                    </p>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    El usuario puede realizar todas las acciones disponibles en
                    la plataforma sin restricciones.
                  </p>
                </div>
              ) : (
                <div className='grid grid-cols-1 lg:grid-cols-3 gap-4'>
                  {Object.entries(PERMISSION_GROUPS).map(
                    ([groupName, permissions]) => {
                      const totalPermissions = permissions.length;
                      const activePermissions = permissions.filter((p) =>
                        user.permissions.includes(p)
                      ).length;

                      return (
                        <div
                          key={groupName}
                          className='bg-card rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200 border'
                        >
                          <div className='flex items-center justify-between border-b pb-2 mb-3'>
                            <div className='flex items-center gap-2'>
                              <Shield className='w-4 h-4 text-primary' />
                              <h4 className='font-medium text-sm'>
                                {groupName}
                              </h4>
                            </div>
                            <Badge
                              variant={
                                activePermissions === 0
                                  ? 'destructive'
                                  : activePermissions === totalPermissions
                                  ? 'default'
                                  : 'secondary'
                              }
                              className='text-xs'
                            >
                              {activePermissions}/{totalPermissions}
                            </Badge>
                          </div>

                          <div className='flex flex-col gap-1'>
                            {permissions
                              .filter(
                                (permission) =>
                                  permission &&
                                  PERMISSION_DISPLAY_NAMES[permission]
                              )
                              .map((permission) => (
                                <PermissionItem
                                  key={permission}
                                  permission={permission}
                                  hasPermission={user.permissions.includes(
                                    permission
                                  )}
                                />
                              ))}
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
              )}
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className='pt-4 border-t'>
          <Button onClick={() => onOpenChange(false)}>Cerrar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils/styles';
import { UserStatusDialogProps } from '@/features/users';

export function UserStatusDialog({
  user,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: UserStatusDialogProps) {
  if (!user) return null;

  const actionText = user.isActive ? 'desactivar' : 'activar';
  const actionTextCapitalized = user.isActive
    ? 'Desactivar acceso'
    : 'Activar acceso';
  const actionLoadingText = user.isActive ? 'Desactivando...' : 'Activando...';

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {}
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='w-[95vw] max-w-[500px]'>
        <AlertDialogHeader>
          <AlertDialogTitle>¿{actionTextCapitalized} usuario?</AlertDialogTitle>
          <AlertDialogDescription>
            ¿Estás seguro que deseas {actionText} el acceso al usuario "
            {user.username}"?{' '}
            {user.isActive
              ? 'El usuario no podrá acceder al sistema hasta que sea reactivado.'
              : 'El usuario podrá volver a acceder al sistema.'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <Button
            variant={user.isActive ? 'destructive' : 'default'}
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              user.isActive &&
                'dark:bg-rose-600 dark:hover:bg-rose-700 dark:text-white',
              isLoading && 'dark:opacity-80'
            )}
          >
            {isLoading ? actionLoadingText : actionTextCapitalized}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

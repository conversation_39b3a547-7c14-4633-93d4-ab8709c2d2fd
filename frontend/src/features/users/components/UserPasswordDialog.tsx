import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Lock, Eye, EyeOff } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { User } from '@/features/users';
import { cn } from '@/lib/utils/styles';
import {
  changePasswordSchema,
  type ChangePasswordSchema,
} from '@/features/users';

interface UserPasswordDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit?: (
    userId: string,
    newPassword: string,
    confirmPassword: string
  ) => Promise<void>;
  isLoading?: boolean;
}

export function UserPasswordDialog({
  user,
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: UserPasswordDialogProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const form = useForm<ChangePasswordSchema>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
      setShowPassword(false);
      setShowConfirmPassword(false);
    }
  }, [open, form]);

  const handleSubmit = async (data: ChangePasswordSchema) => {
    if (!user) return;

    try {
      if (onSubmit) {
        await onSubmit(user._id, data.newPassword, data.confirmPassword);
        form.reset();
        onOpenChange(false);
      }
    } catch (error) {}
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg font-semibold'>
            <Lock className='w-5 h-5 text-primary' />
            Cambiar Contraseña - {user.username}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6 py-4'
          >
            <FormField
              control={form.control}
              name='newPassword'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.newPassword && 'text-destructive'
                    )}
                  >
                    <Lock className='w-4 h-4 text-muted-foreground' />
                    Nueva Contraseña
                    <span className='text-destructive'>*</span>
                  </FormLabel>
                  <FormControl>
                    <div className='relative'>
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Ingrese la nueva contraseña'
                        className={cn(
                          'focus-visible:ring-2 pr-10',
                          form.formState.errors.newPassword &&
                            'border-destructive'
                        )}
                        disabled={isLoading}
                        autoComplete='new-password'
                      />
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        className='absolute right-0 top-0 h-full px-3 hover:bg-transparent'
                        onClick={() => setShowPassword(!showPassword)}
                        tabIndex={-1}
                      >
                        {showPassword ? (
                          <EyeOff className='h-4 w-4 text-muted-foreground' />
                        ) : (
                          <Eye className='h-4 w-4 text-muted-foreground' />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage className='text-destructive' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='confirmPassword'
              render={({ field }) => (
                <FormItem>
                  <FormLabel
                    className={cn(
                      'flex items-center gap-2',
                      form.formState.errors.confirmPassword &&
                        'text-destructive'
                    )}
                  >
                    <Lock className='w-4 h-4 text-muted-foreground' />
                    Confirmar Contraseña
                    <span className='text-destructive'>*</span>
                  </FormLabel>
                  <FormControl>
                    <div className='relative'>
                      <Input
                        {...field}
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder='Confirme la nueva contraseña'
                        className={cn(
                          'focus-visible:ring-2 pr-10',
                          form.formState.errors.confirmPassword &&
                            'border-destructive'
                        )}
                        disabled={isLoading}
                        autoComplete='new-password'
                      />
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        className='absolute right-0 top-0 h-full px-3 hover:bg-transparent'
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                        tabIndex={-1}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className='h-4 w-4 text-muted-foreground' />
                        ) : (
                          <Eye className='h-4 w-4 text-muted-foreground' />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage className='text-destructive' />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-3 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
                className='min-w-[100px]'
              >
                Cancelar
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='min-w-[100px]'
              >
                {isLoading ? (
                  <span className='flex items-center gap-2'>
                    <span className='animate-spin h-4 w-4 border-2 border-white border-opacity-50 border-t-white rounded-full' />
                    Guardando
                  </span>
                ) : (
                  'Cambiar contraseña'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

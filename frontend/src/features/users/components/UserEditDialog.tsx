import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { User as UserIcon, UserCog } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { USER_ROLES, ROLE_DISPLAY_NAMES, UserRole } from '@/features/auth';
import {
  editUserSchema,
  type EditUserSchema,
  UserEditDialogProps,
} from '@/features/users';

export function UserEditDialog({
  user,
  isOpen,
  onOpenChange,
  onUpdate,
  isLoading = false,
}: UserEditDialogProps) {
  const form = useForm<EditUserSchema>({
    resolver: zodResolver(editUserSchema),
    defaultValues: {
      username: '',
      role: USER_ROLES.CASHIER,
    },
  });

  useEffect(() => {
    if (user) {
      form.reset({
        username: user.username,
        role: user.role,
      });
    }
  }, [user, form]);

  useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [isOpen, form]);

  const handleSubmit = async (data: { username: string; role: UserRole }) => {
    if (!user || !data.username || !data.role) return;

    try {
      await onUpdate(user._id, {
        username: data.username,
        role: data.role,
      });
      onOpenChange(false);
    } catch (error) {}
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg font-semibold'>
            <UserCog className='w-5 h-5 text-primary' />
            Editar Usuario: {user.username}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6 py-4'
          >
            <FormField
              control={form.control}
              name='username'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='flex items-center gap-2'>
                    <UserIcon className='w-4 h-4 text-muted-foreground' />
                    Nombre de usuario
                    <span className='text-destructive'>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='Ingrese el nombre de usuario'
                      disabled={isLoading}
                      autoComplete='off'
                      className='focus-visible:ring-2'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='role'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='flex items-center gap-2'>
                    <UserCog className='w-4 h-4 text-muted-foreground' />
                    Rol del usuario
                    <span className='text-destructive'>*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger className='focus-visible:ring-2'>
                        <SelectValue placeholder='Seleccionar rol' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(USER_ROLES).map((role) => (
                        <SelectItem
                          key={role}
                          value={role}
                          className='cursor-pointer'
                        >
                          {ROLE_DISPLAY_NAMES[role]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-3 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
                className='min-w-[100px]'
              >
                Cancelar
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='min-w-[100px]'
              >
                {isLoading ? (
                  <span className='flex items-center gap-2'>
                    <span className='animate-spin h-4 w-4 border-2 border-white border-opacity-50 border-t-white rounded-full' />
                    Guardando
                  </span>
                ) : (
                  'Guardar cambios'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

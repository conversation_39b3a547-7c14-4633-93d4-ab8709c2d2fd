import { useEffect, useState, useCallback, useMemo } from 'react';
import { UserCog, Check, Info, ShieldAlert } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  PERMISSIONS,
  PERMISSION_DISPLAY_NAMES,
  USER_ROLES,
  type Permission,
} from '@/features/auth';
import { UserPermissionsDialogProps } from '@/features/users';

type PermissionString = Permission | '*';

const GROUP_NAMES: Record<string, string> = {
  INVENTORY_PRODUCTS: 'Inventario - Productos',
  INVENTORY_STOCK: 'Inventario - Stock',
  SALES: 'Ventas',
  CATEGORIES: 'Categorías',
  LABORATORIES: 'Laboratorios',
  USERS: 'Usuarios',
  REPORTS: 'Reportes',
  SETTINGS: 'Configuración',
} as const;

const PERMISSION_GROUPS = {
  INVENTORY_PRODUCTS: [
    PERMISSIONS.INVENTORY.PRODUCTS.LIST,
    PERMISSIONS.INVENTORY.PRODUCTS.CREATE,
    PERMISSIONS.INVENTORY.PRODUCTS.EDIT,
    PERMISSIONS.INVENTORY.PRODUCTS.DELETE,
  ],
  INVENTORY_STOCK: [
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.CREATE,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.EDIT,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.DELETE,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.EDIT,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.EDIT,
  ],
  SALES: [
    PERMISSIONS.SALES.LIST,
    PERMISSIONS.SALES.CREATE,
    PERMISSIONS.SALES.EDIT,
    PERMISSIONS.SALES.DELETE,
    PERMISSIONS.SALES.CANCEL,
  ],
  CATEGORIES: [
    PERMISSIONS.CATEGORIES.LIST,
    PERMISSIONS.CATEGORIES.CREATE,
    PERMISSIONS.CATEGORIES.EDIT,
    PERMISSIONS.CATEGORIES.DELETE,
  ],
  LABORATORIES: [
    PERMISSIONS.LABORATORIES.LIST,
    PERMISSIONS.LABORATORIES.CREATE,
    PERMISSIONS.LABORATORIES.EDIT,
    PERMISSIONS.LABORATORIES.DELETE,
  ],
  USERS: [
    PERMISSIONS.USERS.LIST,
    PERMISSIONS.USERS.CREATE,
    PERMISSIONS.USERS.EDIT,
    PERMISSIONS.USERS.DELETE,
    PERMISSIONS.USERS.CHANGE_PASSWORD,
  ],
  REPORTS: [PERMISSIONS.REPORTS.SALES.LIST, PERMISSIONS.REPORTS.INVENTORY.LIST],
  SETTINGS: [PERMISSIONS.SETTINGS.LIST, PERMISSIONS.SETTINGS.EDIT],
} as const;

export function UserPermissionsDialog({
  user,
  open,
  onOpenChange,
  onSavePermissions,
  isLoading = false,
}: UserPermissionsDialogProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<
    PermissionString[]
  >([]);
  const [hasAllPermissions, setHasAllPermissions] = useState(false);

  const isAdmin = user?.role === USER_ROLES.ADMIN;
  const allPermissions = useMemo(
    () => Object.values(PERMISSION_GROUPS).flat(),
    []
  );

  useEffect(() => {
    if (!user || !open) return;

    const hasAll = user.permissions.includes('*') || isAdmin;
    setHasAllPermissions(hasAll);
    setSelectedPermissions(
      hasAll ? ['*', ...allPermissions] : user.permissions
    );
  }, [user, open, isAdmin, allPermissions]);

  const handleTogglePermission = useCallback(
    (permission: Permission) => {
      if (isAdmin) return;

      setSelectedPermissions((prev) => {
        if (permission === '*') {
          return prev.includes('*') ? [] : ['*', ...allPermissions];
        }

        if (prev.includes('*')) {
          return allPermissions.filter((p) => p !== permission);
        }

        return prev.includes(permission)
          ? prev.filter((p) => p !== permission)
          : [...prev, permission];
      });
    },
    [isAdmin, allPermissions]
  );

  const handleSelectAllInGroup = useCallback(
    (groupPermissions: readonly Permission[]) => {
      if (isAdmin) return;

      setSelectedPermissions((prev) => {
        const allSelected = groupPermissions.every((p) => prev.includes(p));

        if (allSelected) {
          const newPermissions = prev.filter(
            (p) => !groupPermissions.includes(p)
          );
          return prev.includes('*')
            ? newPermissions.filter((p) => p !== '*')
            : newPermissions;
        }

        return [...new Set([...prev, ...groupPermissions])];
      });
    },
    [isAdmin]
  );

  const handleSave = useCallback(async () => {
    if (!user || isAdmin) return;

    try {
      await onSavePermissions(user._id, selectedPermissions as string[]);
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving permissions:', error);
    }
  }, [user, isAdmin, selectedPermissions, onSavePermissions, onOpenChange]);

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[600px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <UserCog className='w-5 h-5' />
            Gestionar Permisos - {user.username}
          </DialogTitle>
        </DialogHeader>

        {renderAlert({ isAdmin, hasAllPermissions })}

        <div className='py-4 max-h-[60vh] overflow-y-auto'>
          <PermissionCheckbox
            id='all-permissions'
            label='Acceso completo al sistema'
            checked={selectedPermissions.includes('*') || isAdmin}
            onChange={() => handleTogglePermission('*')}
            disabled={isLoading || isAdmin}
            className='mb-6 border-b pb-4'
          />

          {Object.entries(PERMISSION_GROUPS).map(([group, permissions]) => (
            <PermissionGroup
              key={group}
              group={group}
              permissions={permissions}
              selectedPermissions={selectedPermissions}
              isAdmin={isAdmin}
              isLoading={isLoading}
              onTogglePermission={handleTogglePermission}
              onSelectAll={handleSelectAllInGroup}
            />
          ))}
        </div>

        <DialogFooter
          isAdmin={isAdmin}
          isLoading={isLoading}
          onCancel={() => onOpenChange(false)}
          onSave={handleSave}
        />
      </DialogContent>
    </Dialog>
  );
}

// Componentes auxiliares
const PermissionCheckbox = ({
  id,
  label,
  checked,
  onChange,
  disabled,
  className = '',
}) => (
  <div className={`flex items-center space-x-2 ${className}`}>
    <Checkbox
      id={id}
      checked={checked}
      onCheckedChange={onChange}
      disabled={disabled}
    />
    <label
      htmlFor={id}
      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
    >
      {label}
    </label>
  </div>
);

const PermissionGroup = ({
  group,
  permissions,
  selectedPermissions,
  isAdmin,
  isLoading,
  onTogglePermission,
  onSelectAll,
}) => (
  <div className='mb-6'>
    <div className='flex items-center justify-between mb-2'>
      <h3 className='text-md font-medium'>{GROUP_NAMES[group]}</h3>
      {!isAdmin && (
        <Button
          variant='ghost'
          size='sm'
          onClick={() => onSelectAll(permissions)}
          disabled={isLoading}
          className='h-7 text-xs'
        >
          {permissions.every((p) => selectedPermissions.includes(p))
            ? 'Desmarcar todos'
            : 'Marcar todos'}
        </Button>
      )}
    </div>
    <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
      {permissions.map((permission) => (
        <PermissionCheckbox
          key={permission}
          id={permission}
          label={PERMISSION_DISPLAY_NAMES[permission] || permission}
          checked={selectedPermissions.includes(permission) || isAdmin}
          onChange={() => onTogglePermission(permission)}
          disabled={isLoading || isAdmin}
        />
      ))}
    </div>
  </div>
);

const DialogFooter = ({ isAdmin, isLoading, onCancel, onSave }) => (
  <div className='flex justify-end gap-2 pt-4 border-t'>
    <Button
      type='button'
      variant='outline'
      onClick={onCancel}
      disabled={isLoading}
    >
      Cancelar
    </Button>
    {!isAdmin && (
      <Button
        onClick={onSave}
        disabled={isLoading}
        className='flex items-center gap-2'
      >
        {isLoading ? (
          <span className='flex items-center gap-2'>
            <span className='animate-spin h-4 w-4 border-2 border-white border-opacity-50 border-t-white rounded-full' />
            Guardando...
          </span>
        ) : (
          <>
            <Check className='w-4 h-4' />
            Guardar Permisos
          </>
        )}
      </Button>
    )}
  </div>
);

const renderAlert = ({ isAdmin, hasAllPermissions }) => {
  if (isAdmin) {
    return (
      <Alert className='bg-primary/10 border-primary/20 mb-4'>
        <ShieldAlert className='h-4 w-4 text-primary' />
        <AlertDescription className='text-sm'>
          Este usuario es administrador y tiene acceso completo al sistema. Los
          permisos no pueden ser modificados.
        </AlertDescription>
      </Alert>
    );
  }

  if (hasAllPermissions) {
    return (
      <Alert className='bg-primary/10 border-primary/20 mb-4'>
        <Info className='h-4 w-4 text-primary' />
        <AlertDescription className='text-sm'>
          Este usuario tiene acceso completo al sistema. Puedes modificar
          permisos específicos o mantener el acceso total.
        </AlertDescription>
      </Alert>
    );
  }

  return null;
};

import { Key, UserCog, Trash, Edit, Eye, Power } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UsersTableColumnProps, User } from '@/features/users';
import { ROLE_DISPLAY_NAMES } from '@/features/auth';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export const getUserTableColumns = ({
  permissions,
  onEdit,
  onDelete,
  onChangePassword,
  onViewDetails,
  onManagePermissions,
  onToggleStatus,
}: UsersTableColumnProps) => [
  {
    id: 'username',
    header: 'Usuario',
    cell: (user: User) => user.username,
  },
  {
    id: 'role',
    header: 'Rol',
    cell: (user: User) => ROLE_DISPLAY_NAMES[user.role],
  },
  {
    id: 'status',
    header: 'Estado',
    cell: (user: User) => (
      <Badge variant={user.isActive ? 'success' : 'destructive'}>
        {user.isActive ? 'Activo' : 'Inactivo'}
      </Badge>
    ),
  },
  {
    id: 'createdAt',
    header: 'Fecha de registro',
    cell: (user: User) =>
      new Date(user.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: (user: User) => (
      <div className='flex items-center gap-2'>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant='ghost'
                size='icon'
                onClick={() => onViewDetails(user)}
              >
                <Eye className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Detalles</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {permissions.canEdit && (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onEdit(user)}
                  >
                    <Edit className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Modificar usuario</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onChangePassword(user)}
                  >
                    <Key className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Actualizar credenciales</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onToggleStatus(user)}
                    className={
                      user.isActive
                        ? 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-500/10'
                        : 'text-green-600 hover:text-green-700 hover:bg-green-500/10'
                    }
                  >
                    <Power className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{user.isActive ? 'Desactivar' : 'Activar'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        )}

        {permissions.canManagePermissions && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onManagePermissions(user)}
                >
                  <UserCog className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Configurar permisos</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {permissions.canDelete && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onDelete(user)}
                  className='text-red-600 hover:text-red-700 dark:text-rose-500 dark:hover:text-rose-400 hover:bg-red-500/10 dark:hover:bg-rose-500/20'
                >
                  <Trash className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Dar de baja</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    ),
  },
];

import { User, <PERSON>, <PERSON>, <PERSON>Off } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  createUserSchema,
  type CreateUserSchema,
  UserCreateDialogProps,
} from '@/features/users';
import { USER_ROLES, ROLE_DISPLAY_NAMES } from '@/features/auth';
import { cn } from '@/lib/utils/styles';

export function UserCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: UserCreateDialogProps) {
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<CreateUserSchema>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      username: '',
      password: '',
      role: USER_ROLES.CASHIER,
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
      setShowPassword(false);
    }
  }, [open, form]);

  const handleSubmit = async (data: CreateUserSchema) => {
    try {
      await onSubmit(data);
      form.reset();
      onOpenChange(false);
    } catch (error) {}
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            Crear nuevo usuario
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 sm:space-y-6 py-2'
          >
            <div className='grid gap-6'>
              <FormField
                control={form.control}
                name='username'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.username && 'text-destructive'
                      )}
                    >
                      <User className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Usuario
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <div className='relative'>
                        <Input
                          {...field}
                          placeholder='Ingrese el nombre de usuario'
                          className={cn(
                            form.formState.errors.username && 'border-red-500'
                          )}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className='text-destructive text-sm' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.password && 'text-destructive'
                      )}
                    >
                      <Eye className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Contraseña
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <div className='relative'>
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder='Ingrese la contraseña'
                          className={cn(
                            form.formState.errors.password && 'border-red-500'
                          )}
                        />
                        <button
                          type='button'
                          onClick={() => setShowPassword(!showPassword)}
                          className='absolute right-3 top-2.5'
                        >
                          {showPassword ? (
                            <EyeOff className='h-5 w-5 text-gray-400' />
                          ) : (
                            <Eye className='h-5 w-5 text-gray-400' />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className='text-destructive text-sm' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='role'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.role && 'text-destructive'
                      )}
                    >
                      <Shield className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Rol
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          className={cn(
                            form.formState.errors.role && 'border-red-500'
                          )}
                        >
                          <SelectValue placeholder='Seleccionar rol' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(USER_ROLES).map(([key, value]) => (
                          <SelectItem key={value} value={value}>
                            {ROLE_DISPLAY_NAMES[value]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage className='text-destructive text-sm' />
                  </FormItem>
                )}
              />

              <div className='flex justify-end gap-2 pt-4 border-t'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                >
                  Cancelar
                </Button>
                <Button
                  type='submit'
                  disabled={isLoading}
                  className='flex items-center gap-2'
                >
                  {isLoading ? 'Creando...' : 'Crear usuario'}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

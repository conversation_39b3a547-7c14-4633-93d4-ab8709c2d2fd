import { Product } from '@/features/inventory/products';
import { Customer, CreateCustomerDto } from '@/features/customers';
import { CashRegister } from '@/features/cash';
import { SALE_STATUS, PAYMENT_METHODS } from '../constants/sale.constants';
import { UseMutationResult } from '@tanstack/react-query';
import { SubmitHandler, UseFormReturn } from 'react-hook-form';

export interface SaleItem {
  _id?: string;
  productId: string;
  product: {
    _id: string;
    name: string;
    sku: string;
    price: number;
    description?: string;
  };
  name: string;
  quantity: number;
  unitPrice: number;
  stock: number;
  subtotal: number;
  discount?: number;
}

export interface Sale {
  _id: string;
  number: string;
  date: string;
  customer?: {
    _id: string;
    businessName: string;
    documentNumber: string;
    documentType: string;
  };
  items: SaleItem[];
  subtotal: number;
  discount: number;
  total: number;
  status: keyof typeof SALE_STATUS;
  paymentMethod: keyof typeof PAYMENT_METHODS;
  cashRegister: CashRegister;
  notes?: string;
  createdBy: {
    _id: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateSaleDto {
  customerId?: string;
  items: {
    productId: string;
    quantity: number;
    unitPrice: number;
    discount: number;
  }[];
  discount: number;
  paymentMethod: keyof typeof PAYMENT_METHODS;
  cashRegisterId: string;
  notes?: string;
  amountReceived?: number;
}

export interface UpdateSaleDto extends Partial<CreateSaleDto> {}

export interface SalesResponse {
  sales: Sale[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface SaleResponse {
  sale: Sale;
  message: string;
}

export interface SalesState {
  sales: SalesResponse;
  isLoading: boolean;
  filters: SaleFilters;
  setFilters: (filters: SaleFilters) => void;
  invalidateSales: () => void;
  showErrorToast: (error: unknown, defaultMessage: string) => void;
}

export interface SaleFilters {
  search?: string;
  status?: keyof typeof SALE_STATUS;
  customerId?: string | null;
  startDate?: string | null;
  endDate?: string | null;
  page?: number;
  limit?: number;
}

export interface SalePagination {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  limit: number;
}

export interface UseSalesReturn {
  sales: Sale[];
  isLoading: boolean;
  filters: SaleFilters;
  setFilters: (filters: SaleFilters) => void;
  pagination: SalePagination;
  createMutation: UseMutationResult<Sale, unknown, CreateSaleDto, unknown>;
  cancelMutation: UseMutationResult<
    Sale,
    unknown,
    { id: string; reason: string },
    unknown
  >;
}

export interface SalePermissions {
  canList: boolean;
  canCreate: boolean;
  canCancel: boolean;
  canDelete: boolean; // Nuevo permiso
  canViewDetails: boolean;
}

export interface SalesFiltersProps {
  search: string;
  status?: keyof typeof SALE_STATUS;
  customerId?: string;
  page: number;
  limit: number;
  setFilters: (filters: SaleFilters) => void;
}

export interface SalesActionsProps {
  canCreate: boolean;
  onExportPdf: () => void;
  onExportExcel: () => void;
  onExportCSV: () => void;
}

export interface SaleRowActionsProps {
  sale: Sale;
  permissions: {
    canCancel: boolean;
  };
  onViewDetails: (sale: Sale) => void;
  onCancel: (sale: Sale) => void;
  onPrint?: (sale: Sale) => void;
}

export interface SalesTableProps {
  data: SalesResponse;
  isLoading: boolean;
  permissions: SalePermissions;
  onViewDetails: (sale: Sale) => void;
  onCancel: (sale: Sale) => void;
  onDelete: (sale: Sale) => void;
  dialogProps: {
    details: SaleDetailsDialogProps;
    cancel: SaleCancelDialogProps;
    delete: SaleDeleteDialogProps;
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    limit: number;
    onLimitChange: (limit: number) => void;
    onPageChange: (page: number) => void;
  };
}

export interface SalesTableColumnProps {
  permissions: SalePermissions;
  onCancel: (sale: Sale) => void;
  onDelete: (sale: Sale) => void; // Nueva prop
  onViewDetails: (sale: Sale) => void;
}

export interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface SaleCreateDialogProps extends DialogProps {
  onSubmit: (data: CreateSaleDto) => Promise<void>;
  isLoading?: boolean;
}

export interface SaleDetailsDialogProps extends DialogProps {
  sale: Sale | null;
}

export interface SaleCancelDialogProps extends DialogProps {
  sale: Sale | null;
  onConfirm: (reason: string) => void;
  isLoading?: boolean;
}

export interface SaleDeleteDialogProps {
  sale: Sale | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export interface TableColumn {
  id: string;
  header: string;
  align: 'left' | 'right' | 'center';
  cell: (sale: Sale) => React.ReactNode;
}

export interface SaleCustomer {
  _id: string;
  businessName: string;
  documentType: string;
  documentNumber: string;
  isGeneric?: boolean;
  isProtected?: boolean;
}

export interface SaleFormProps {
  form: UseFormReturn<CreateSaleSchema>;
  products: Product[];
  customers: Customer[];
  onSubmit: (data: CreateSaleSchema) => Promise<void>;
  isLoading: boolean;
  onCancel: () => void;
  showCreateCustomer: boolean;
  setShowCreateCustomer: (show: boolean) => void;
  handleCreateCustomer: (data: CreateCustomerDto) => Promise<Customer>;
  isCreatingCustomer: boolean;
  onResetForm: () => void;
  // Removemos completedSale ya que no lo necesitamos aquí
}

export interface CreateSaleSchema {
  customer?: string;
  items: {
    product?: string;
    quantity?: number;
    unitPrice?: number;
    discount?: number;
  }[];
  paymentMethod: keyof typeof PAYMENT_METHODS;
  discount?: number;
  notes?: string;
  amountReceived?: number;
}

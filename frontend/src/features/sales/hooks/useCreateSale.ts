import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import type { CreateSaleDto } from '../types/sale.types';
import { saleService } from '../services/sale.service';
import { useAuth } from '@/features/auth';

interface CreateSaleResponse {
  sale: {
    number: string;
    _id: string;
  };
  message: string;
}

export const useCreateSale = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { token, isAuthenticated } = useAuth();

  const mutation = useMutation<CreateSaleResponse, Error, CreateSaleDto>({
    mutationFn: async (data: CreateSaleDto) => {
      if (!isAuthenticated || !token) {
        throw new Error('No estás autenticado');
      }
      return await saleService.createSale(data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast({
        title: 'Venta completada',
        description: `Venta #${data.sale.number} registrada exitosamente`,
      });
    },
    onError: (error: Error) => {
      const errorMessage = error.message.includes('validation failed')
        ? 'El descuento no puede ser mayor que el total de la venta'
        : error.message || 'No se pudo completar la venta';

      toast({
        variant: 'destructive',
        title: 'Error',
        description: errorMessage,
      });
    },
  });

  return {
    createSale: mutation.mutateAsync,
    isLoading: mutation.isPending,
  };
};

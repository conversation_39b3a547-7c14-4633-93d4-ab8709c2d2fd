import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { saleService } from '../services/sale.service';
import { useSalesStore } from '../store/sales.store';
import type { Sale, SaleFilters } from '../types/sale.types';

export function useSales() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { filters, setFilters } = useSalesStore();

  const query = useQuery({
    queryKey: ['sales', filters],
    queryFn: () => saleService.getSales(filters),
  });

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  const handleLimitChange = (limit: number) => {
    setFilters({ ...filters, page: 1, limit });
  };

  const cancelMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      saleService.cancelSale(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast({
        title: 'Venta anulada',
        description: 'La venta ha sido anulada exitosamente',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Error al anular la venta',
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => saleService.deleteSale(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast({
        title: 'Venta eliminada',
        description: 'La venta ha sido eliminada permanentemente',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Error al eliminar la venta',
        variant: 'destructive',
      });
    },
  });

  return {
    data: {
      sales: query.data?.sales?.sales || [],
      totalRecords: query.data?.sales?.totalRecords || 0,
      totalPages: query.data?.sales?.totalPages || 1,
      currentPage: query.data?.sales?.currentPage || 1,
    },
    isLoading: query.isLoading,
    filters,
    setFilters,
    pagination: {
      currentPage: query.data?.sales?.currentPage || 1,
      totalPages: query.data?.sales?.totalPages || 1,
      totalRecords: query.data?.sales?.totalRecords || 0,
      limit: filters.limit,
      onPageChange: handlePageChange,
      onLimitChange: handleLimitChange,
    },
    cancelMutation,
    deleteMutation,
  };
}

import { usePermissions } from '@/features/users';
import { PERMISSIONS } from '@/features/auth';
import type { SalePermissions } from '../types/sale.types';

export function useSalePermissions(): { permissions: SalePermissions } {
  const { checkPermission } = usePermissions();

  return {
    permissions: {
      canList: checkPermission(PERMISSIONS.SALES.LIST),
      canCreate: checkPermission(PERMISSIONS.SALES.CREATE),
      canCancel: checkPermission(PERMISSIONS.SALES.CANCEL),
      canDelete: checkPermission(PERMISSIONS.SALES.DELETE),
      canViewDetails: checkPermission(PERMISSIONS.SALES.LIST),
    },
  };
}

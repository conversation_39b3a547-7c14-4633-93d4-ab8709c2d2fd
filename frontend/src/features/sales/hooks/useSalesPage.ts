import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { saleService } from '../services/sale.service';
import { useSales } from './useSales';
import { useToast } from '@/hooks/useToast';
import type { Sale, CreateSaleDto } from '../types/sale.types';
import { useSalePermissions } from '@/features/sales';

export const useSalesPage = () => {
  const { toast } = useToast();
  const { permissions } = useSalePermissions();
  const {
    data,
    isLoading,
    filters,
    setFilters,
    pagination,
    invalidateSales,
    showErrorToast,
  } = useSales();

  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [dialogState, setDialogState] = useState({
    details: false,
    cancel: false,
    delete: false, // Nuevo estado
  });

  const handleDialogChange =
    (dialog: keyof typeof dialogState) => (open: boolean) => {
      setDialogState((prev) => ({ ...prev, [dialog]: open }));
      if (!open) {
        setSelectedSale(null);
      }
    };

  const mutations = {
    cancel: useMutation({
      mutationFn: ({ id, reason }: { id: string; reason: string }) =>
        saleService.cancelSale(id, reason),
      onSuccess: () => {
        invalidateSales();
        handleDialogChange('cancel')(false);
        toast({
          title: 'Venta anulada',
          description: 'La venta ha sido anulada exitosamente',
        });
      },
      onError: (error) => showErrorToast(error, 'Error al anular la venta'),
    }),
    delete: useMutation({
      mutationFn: (id: string) => saleService.deleteSale(id),
      onSuccess: () => {
        invalidateSales();
        handleDialogChange('delete')(false); // Solo cerramos después del éxito
        toast({
          title: 'Venta eliminada',
          description: 'La venta ha sido eliminada permanentemente',
        });
      },
      onError: (error) => {
        // No cerramos el modal en caso de error
        showErrorToast(error, 'Error al eliminar la venta');
      },
    }),
  };

  const dialogProps = {
    details: {
      open: dialogState.details,
      onOpenChange: (open: boolean) => handleDialogChange('details')(open),
      sale: selectedSale,
      permissions: {
        canCancel: permissions.canCancel,
        canDelete: permissions.canDelete,
      },
    },
    cancel: {
      open: dialogState.cancel,
      onOpenChange: (open: boolean) => handleDialogChange('cancel')(open),
      sale: selectedSale,
      onConfirm: (reason: string) =>
        mutations.cancel.mutateAsync({
          id: selectedSale?._id ?? '',
          reason,
        }),
      isLoading: mutations.cancel.isPending,
    },
    delete: {
      open: dialogState.delete,
      onOpenChange: (open: boolean) => handleDialogChange('delete')(open),
      sale: selectedSale,
      onConfirm: () => mutations.delete.mutateAsync(selectedSale?._id ?? ''),
      isLoading: mutations.delete.isPending,
    },
  };

  const actions = {
    onCancel: (sale: Sale) => {
      setSelectedSale(sale);
      handleDialogChange('cancel')(true);
    },
    onViewDetails: (sale: Sale) => {
      setSelectedSale(sale);
      handleDialogChange('details')(true);
    },
    onExportPdf: () => {
      toast({
        title: 'Exportando PDF',
        description: 'El archivo se está generando',
      });
    },
    onExportExcel: () => {
      toast({
        title: 'Exportando Excel',
        description: 'El archivo se está generando',
      });
    },
    onExportCSV: () => {
      toast({
        title: 'Exportando CSV',
        description: 'El archivo se está generando',
      });
    },
    handleDelete: (sale: Sale) => {
      setSelectedSale(sale);
      handleDialogChange('delete')(true);
    },
  };

  return {
    data,
    isLoading,
    isEmpty: data.sales.length === 0,
    selectedSale,
    filters: { ...filters, setFilters },
    pagination,
    permissions,
    dialogProps: {
      details: dialogProps.details,
      cancel: dialogProps.cancel,
      delete: dialogProps.delete,
    },
    actions: {
      ...actions,
      onCancel: actions.onCancel,
      onViewDetails: actions.onViewDetails,
      handleDelete: actions.handleDelete,
    },
  };
};

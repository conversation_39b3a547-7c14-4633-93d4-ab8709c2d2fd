import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import type { Sale } from '../types/sale.types';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils/format';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { getImageUrl } from '@/lib/utils/image';

const RECEIPT_WIDTH_MM = 80;
const RECEIPT_SCALE = 2;

export const useSaleReceipt = () => {
  const { generalSettings } = useSettingsStore();

  const generatePDF = async (sale: Sale) => {
    const receiptHtml = getReceiptHtml(sale);
    const container = document.createElement('div');
    container.innerHTML = receiptHtml;
    document.body.appendChild(container);

    try {
      const canvas = await html2canvas(container, {
        scale: RECEIPT_SCALE,
        backgroundColor: '#ffffff',
        width: RECEIPT_WIDTH_MM * 3.78, // Convertir mm a px (aproximado)
        useCORS: true,
        logging: false,
      });

      const aspectRatio = canvas.height / canvas.width;
      const pdfWidth = RECEIPT_WIDTH_MM;
      const pdfHeight = pdfWidth * aspectRatio;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: [pdfWidth, pdfHeight],
        hotfixes: ['px_scaling'],
      });

      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        0,
        0,
        pdfWidth,
        pdfHeight,
        undefined,
        'FAST'
      );

      pdf.save(`recibo-${sale.number}.pdf`);
      document.body.removeChild(container);
    } catch (error) {
      console.error('Error generando PDF:', error);
      document.body.removeChild(container);
    }
  };

  const printPDF = async (sale: Sale) => {
    const receiptHtml = getReceiptHtml(sale);
    const container = document.createElement('div');
    container.innerHTML = receiptHtml;
    document.body.appendChild(container);

    try {
      const canvas = await html2canvas(container, {
        scale: RECEIPT_SCALE,
        backgroundColor: '#ffffff',
        width: RECEIPT_WIDTH_MM * 3.78,
        useCORS: true,
        logging: false,
      });

      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('No se pudo abrir la ventana de impresión');
      }

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Imprimir Recibo</title>
            <style>
              @page {
                size: ${RECEIPT_WIDTH_MM}mm auto;
                margin: 0;
              }
              body {
                margin: 0;
                padding: 0;
                width: ${RECEIPT_WIDTH_MM}mm;
              }
              img {
                width: 100%;
                height: auto;
              }
            </style>
          </head>
          <body>
            <img src="${canvas.toDataURL('image/png')}" />
            <script>
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);

      printWindow.document.close();
      document.body.removeChild(container);
    } catch (error) {
      console.error('Error imprimiendo:', error);
      document.body.removeChild(container);
    }
  };

  const getReceiptHtml = (sale: Sale) => {
    // Calculamos el descuento y el total
    const subtotal = sale.subtotal;
    const discount = sale.discount || 0;
    const total = sale.total; // Usamos el total calculado del backend

    // Función para formatear el descuento en el recibo
    const formatDiscountDisplay = (discount: number, subtotal: number) => {
      // Si el descuento es mayor que el subtotal, probablemente sea un monto fijo
      if (discount > subtotal) {
        return `${formatCurrency(discount)} (M/F)`;
      }
      // Si es menor, podemos mostrar también el porcentaje
      const percentage = ((discount / subtotal) * 100).toFixed(2);
      return `${formatCurrency(discount)} (${percentage}%)`;
    };

    const numberToWords = (num: number): string => {
      const absNum = Math.abs(num);
      const integerPart = Math.floor(absNum);
      const decimalPart = Math.round((absNum - integerPart) * 100);

      // Convertir a palabras el número entero
      const units = [
        '',
        'UN',
        'DOS',
        'TRES',
        'CUATRO',
        'CINCO',
        'SEIS',
        'SIETE',
        'OCHO',
        'NUEVE',
        'DIEZ',
        'ONCE',
        'DOCE',
        'TRECE',
        'CATORCE',
        'QUINCE',
        'DIECISEIS',
        'DIECISIETE',
        'DIECIOCHO',
        'DIECINUEVE',
      ];
      const tens = [
        '',
        '',
        'VEINTE',
        'TREINTA',
        'CUARENTA',
        'CINCUENTA',
        'SESENTA',
        'SETENTA',
        'OCHENTA',
        'NOVENTA',
      ];
      const hundreds = [
        '',
        'CIENTO',
        'DOSCIENTOS',
        'TRESCIENTOS',
        'CUATROCIENTOS',
        'QUINIENTOS',
        'SEISCIENTOS',
        'SETECIENTOS',
        'OCHOCIENTOS',
        'NOVECIENTOS',
      ];

      const toWords = (n: number): string => {
        if (n === 0) return 'CERO';
        if (n === 100) return 'CIEN';
        if (n < 20) return units[n];
        if (n < 100) {
          const unit = n % 10;
          const ten = Math.floor(n / 10);
          return unit === 0 ? tens[ten] : `${tens[ten]} Y ${units[unit]}`;
        }
        const hundred = Math.floor(n / 100);
        const remainder = n % 100;
        return remainder === 0
          ? hundreds[hundred]
          : `${hundreds[hundred]} ${toWords(remainder)}`;
      };

      // Formatear la parte decimal
      const centavos =
        decimalPart === 0
          ? '00/100'
          : decimalPart === 50
          ? '50/100'
          : `${decimalPart}/100`;

      return `${toWords(integerPart)} ${centavos} BOLIVIANOS`;
    };

    // Preparar el HTML del logo
    const logoHtml = generalSettings?.logo
      ? `<div class="logo-container">
           <img src="${getImageUrl(generalSettings.logo)}" 
                alt="Logo">
         </div>`
      : '';

    return `
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Recibo de Venta</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 9px;
                margin: 0;
                padding: 0;
                width: ${RECEIPT_WIDTH_MM}mm;
                color: black;
                line-height: 1.5;
            }
            .container {
                width: 100%;
                padding: 3mm;
                box-sizing: border-box;
            }
            .header {
                text-align: center;
                margin-bottom: 3mm;
            }
            .logo-container {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 3mm;
            }
            .logo-container img {
                max-width: 60mm;
                max-height: 20mm;
                object-fit: contain;
                display: block;
                margin: 0 auto;
            }
            .system-name {
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 2mm;
            }
            .branch-info {
                font-size: 9px;
                margin: 2mm 0;
            }
            .branch-name {
                font-weight: bold;
            }
            hr {
                border: none;
                border-top: 1px dashed #000;
                margin: 3mm 0;
            }
            .info p {
                margin: 2mm 0;
                line-height: 1.8;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 3mm 0;
            }
            th {
                padding: 1mm 0.5mm;
                text-align: left;
                font-size: 9px;
                font-weight: bold;
            }
            td {
                padding: 1.5mm 0.5mm;
                text-align: left;
                font-size: 9px;
                line-height: 1.4;
            }
            .totals {
                margin-top: 3mm;
            }
            .totals td {
                text-align: right;
                padding: 1mm 0.5mm;
            }
            .totals tr:last-child td {
                font-size: 10px;
                font-weight: bold;
                padding-top: 2mm;
            }
            .footer {
                text-align: center;
                font-size: 10px;
                margin-top: 3mm;
                line-height: 1.6;
            }
            .footer p {
                margin: 1mm 0;
            }
            .business-name {
                font-size: 11px;
                font-weight: bold;
                margin-bottom: 1mm;
            }
            .system-name {
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 2mm;
            }
            .branch-info {
                font-size: 9px;
                margin: 2mm 0;
            }
            .branch-name {
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                ${logoHtml}
                <div class="system-name">${
                  generalSettings?.systemName || 'Sistema'
                }</div>
                <div class="branch-info">
                    <div class="branch-name">Sucursal: ${
                      generalSettings?.businessName || 'Principal'
                    }</div>
                    ${
                      generalSettings?.address || 'Dirección no configurada'
                    }<br>
                    Tel: ${generalSettings?.phone || 'No disponible'}<br>
                    ${
                      generalSettings?.email
                        ? `Email: ${generalSettings.email}<br>`
                        : ''
                    }
                    NIT: ${generalSettings?.taxId || 'No configurado'}
                </div>
                <hr>
                <strong>RECIBO DE VENTA N° ${sale.number}</strong>
            </div>
            <hr>
            <div class="info">
                <p>Señor(es): ${
                  sale.customer?.businessName || 'CONSUMIDOR FINAL'
                }</p>
                <p>NIT/CI: ${sale.customer?.documentNumber || '-'}</p>
                <p>Fecha de Emisión: ${format(
                  new Date(sale.date),
                  'dd/MM/yyyy HH:mm'
                )}</p>
            </div>
            <hr>
            <table class="details">
                <thead>
                    <tr>
                        <th>DETALLE</th>
                        <th>CANT</th>
                        <th>PRECIO</th>
                        <th>TOTAL</th>
                    </tr>
                </thead>
                <tbody>
                    ${sale.items
                      .map(
                        (item) => `
                        <tr>
                            <td>${item.product.name}</td>
                            <td>${item.quantity}</td>
                            <td>${formatCurrency(item.unitPrice)}</td>
                            <td>${formatCurrency(
                              item.quantity * item.unitPrice
                            )}</td>
                        </tr>
                        `
                      )
                      .join('')}
                </tbody>
            </table>
            <hr>
            <table class="totals">
                <tr>
                    <td>Subtotal</td>
                    <td>${formatCurrency(subtotal)}</td>
                </tr>
                <tr>
                    <td>Descuento</td>
                    <td>${formatDiscountDisplay(discount, subtotal)}</td>
                </tr>
                <tr>
                    <td><strong>Total a Pagar</strong></td>
                    <td><strong>${formatCurrency(total)}</strong></td>
                </tr>
            </table>
            <p style="margin: 3mm 0; font-size: 9px;">SON: ${numberToWords(
              total
            )}</p>
            <div class="footer">
                <p>¡Gracias por su compra!</p>
                <p>Su salud es nuestra prioridad</p>
                <p>¡Que tenga un excelente día!</p>
            </div>
        </div>
    </body>
    </html>
    `;
  };

  return { generatePDF, printPDF };
};

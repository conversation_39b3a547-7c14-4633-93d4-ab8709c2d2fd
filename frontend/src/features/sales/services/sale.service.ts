import { api } from '@/lib/api/api';
import type { CreateSaleDto } from '../types/sale.types';

export const saleService = {
  async getSales(params: {
    search?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    customerId?: string;
    page: number;
    limit: number;
  }) {
    const { data } = await api.get('/sales', { params });
    return data;
  },

  async createSale(saleData: CreateSaleDto) {
    const { data } = await api.post('/sales', saleData);
    return data;
  },

  async cancelSale(id: string, reason: string) {
    const { data } = await api.post(`/sales/${id}/cancel`, { reason });
    return data;
  },

  async deleteSale(id: string) {
    const { data } = await api.delete(`/sales/${id}`);
    return data;
  },

  async getSaleById(id: string) {
    const { data } = await api.get(`/sales/${id}`);
    return data;
  },
};

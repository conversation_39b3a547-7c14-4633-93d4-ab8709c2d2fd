import { create } from 'zustand';
import type { SaleFilters } from '../types/sale.types';

interface SalesStore {
  filters: SaleFilters;
  setFilters: (filters: Partial<SaleFilters>) => void;
}

const initialFilters: SaleFilters = {
  search: '',
  status: undefined, // Inicialmente sin filtro de estado
  page: 1,
  limit: 10,
};

export const useSalesStore = create<SalesStore>((set) => ({
  filters: initialFilters,
  setFilters: (newFilters) =>
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    })),
}));

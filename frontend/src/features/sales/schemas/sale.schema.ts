import * as z from 'zod';
import { PAYMENT_METHODS } from '../constants/sale.constants';

const saleItemSchema = z.object({
  product: z.string({
    required_error: 'El producto es requerido',
  }),
  quantity: z
    .number({
      required_error: 'La cantidad es requerida',
    })
    .min(1, 'La cantidad debe ser mayor a 0'),
  unitPrice: z
    .number({
      required_error: 'El precio es requerido',
    })
    .min(0, 'El precio debe ser mayor o igual a 0'),
  discount: z
    .number()
    .min(0, 'El descuento debe ser mayor o igual a 0')
    .optional(),
});

export const createSaleSchema = z.object({
  customer: z.string().optional(),
  items: z.array(saleItemSchema).min(1, 'Debe incluir al menos un producto'),
  paymentMethod: z.enum(
    Object.values(PAYMENT_METHODS) as [string, ...string[]],
    {
      required_error: 'El método de pago es requerido',
    }
  ),
  discountType: z.enum(['percentage', 'fixed']).default('fixed'),
  discount: z.number().min(0).default(0),
  notes: z.string().optional(),
  amountReceived: z
    .number()
    .min(0, 'El monto recibido debe ser mayor o igual a 0')
    .nullable()
    .optional(),
});

export type CreateSaleSchema = z.infer<typeof createSaleSchema>;

export const cancelSaleSchema = z.object({
  reason: z
    .string({
      required_error: 'El motivo de anulación es requerido',
    })
    .min(1, 'El motivo de anulación es requerido')
    .max(500, 'El motivo de anulación no puede exceder los 500 caracteres'),
  notes: z.string().optional(),
});

export type CancelSaleSchema = z.infer<typeof cancelSaleSchema>;

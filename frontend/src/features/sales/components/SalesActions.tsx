import { useNavigate } from 'react-router-dom';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Download, FileIcon } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { SalesActionsProps } from '../types/sale.types';

export function SalesActions({
  canCreate,
  onExportPdf,
  onExportExcel,
  onExportCSV,
}: SalesActionsProps) {
  const navigate = useNavigate();

  return (
    <TooltipProvider>
      <div className='flex items-center sm:justify-end justify-center w-full gap-2'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' className='flex items-center gap-2'>
              <Download className='h-4 w-4' />
              <span>Exportar</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={onExportPdf}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportExcel}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportCSV}>
              <FileIcon className='mr-2 h-4 w-4' />
              Exportar CSV
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {canCreate && (
          <Button
            onClick={() => navigate('/sales/create')}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            Nueva Venta
          </Button>
        )}
      </div>
    </TooltipProvider>
  );
}

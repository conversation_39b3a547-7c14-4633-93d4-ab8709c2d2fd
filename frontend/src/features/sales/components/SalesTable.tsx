import { memo } from 'react';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getSaleTableColumns } from './SalesTableColumns';
import { TableAnimations } from './TableAnimations';
import type { SalesTableProps } from '../types/sale.types';
import { SaleDeleteDialog } from './SaleDeleteDialog';

export const SalesTable = memo(function SalesTable({
  data,
  isLoading,
  permissions,
  onViewDetails,
  onCancel,
  onDelete,
  dialogProps,
  pagination,
}: SalesTableProps) {
  const columns = getSaleTableColumns({
    permissions,
    onViewDetails,
    onCancel,
    onDelete,
  });

  if (isLoading) {
    return (
      <div className='flex justify-center items-center py-8'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return (
    <>
      <motion.div
        initial='hidden'
        animate='visible'
        variants={TableAnimations.container}
      >
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column.id} className={`text-${column.align}`}>
                    {column.header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {!data.sales.length ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className='h-24 text-center'
                  >
                    No se encontraron ventas
                  </TableCell>
                </TableRow>
              ) : (
                data.sales.map((sale) => (
                  <motion.tr
                    key={sale._id}
                    variants={TableAnimations.row}
                    className='border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted'
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={`${sale._id}-${column.id}`}
                        className={`text-${column.align}`}
                      >
                        {column.cell(sale)}
                      </TableCell>
                    ))}
                  </motion.tr>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {data.sales.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-2 py-4'
          >
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <Select
                value={pagination.limit.toString()}
                onValueChange={(value) =>
                  pagination.onLimitChange(Number(value))
                }
              >
                <SelectTrigger className='h-8 w-[70px]'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={pageSize.toString()}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <span>registros por página</span>
            </div>

            <div className='flex flex-col sm:flex-row sm:items-center gap-4'>
              <span className='text-sm text-muted-foreground text-center sm:text-left'>
                Total: {data.totalRecords} registros
              </span>

              <div className='flex items-center justify-center sm:justify-end gap-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => pagination.onPageChange(data.currentPage - 1)}
                  disabled={data.currentPage <= 1}
                >
                  <ChevronLeft className='h-4 w-4' />
                  <span className='sr-only'>Página anterior</span>
                </Button>

                <div className='text-sm whitespace-nowrap'>
                  Página {data.currentPage} de {data.totalPages || 1}
                </div>

                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => pagination.onPageChange(data.currentPage + 1)}
                  disabled={
                    !data.totalPages || data.currentPage >= data.totalPages
                  }
                >
                  <ChevronRight className='h-4 w-4' />
                  <span className='sr-only'>Página siguiente</span>
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      <SaleDeleteDialog
        open={dialogProps.delete.open}
        onOpenChange={dialogProps.delete.onOpenChange}
        sale={dialogProps.delete.sale}
        onConfirm={dialogProps.delete.onConfirm}
        isLoading={dialogProps.delete.isLoading}
      />
    </>
  );
});

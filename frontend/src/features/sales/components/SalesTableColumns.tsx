import { memo } from 'react';
import {
  Eye,
  XCircle,
  Trash2,
  Receipt,
  User2,
  Calendar,
  CreditCard,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { formatDate, formatCurrency } from '@/lib/utils/format';
import {
  SALE_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
} from '../constants/sale.constants';
import type {
  Sale,
  SalesTableColumnProps,
  TableColumn,
} from '../types/sale.types';
import { cn } from '@/lib/utils/styles';

const ActionButton = memo(function ActionButton({
  icon: Icon,
  tooltip,
  onClick,
  variant = 'ghost',
  disabled = false,
}: {
  icon: typeof Eye;
  tooltip: string;
  onClick: () => void;
  variant?: 'ghost' | 'destructive';
  disabled?: boolean;
}) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size='icon'
            onClick={onClick}
            disabled={disabled}
            className={cn(
              'text-muted-foreground',
              variant === 'ghost' &&
                'hover:text-destructive hover:bg-destructive/10',
              disabled && 'cursor-not-allowed opacity-50'
            )}
          >
            <Icon className='h-4 w-4' />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});

// Componente para el número de venta
const SaleNumber = memo(({ sale }: { sale: Sale }) => (
  <div className='flex items-center gap-2'>
    <Receipt className='w-4 h-4 text-primary' />
    <span className='font-medium'>{sale.number}</span>
  </div>
));

// Componente para la fecha
const SaleDate = memo(({ date }: { date: string }) => (
  <div className='flex items-center gap-2'>
    <Calendar className='w-4 h-4 text-muted-foreground' />
    <span>{formatDate(date)}</span>
  </div>
));

// Componente para el cliente
const CustomerInfo = memo(({ customer }: { customer?: Sale['customer'] }) => (
  <div className='flex items-center gap-2'>
    <User2 className='w-4 h-4 text-muted-foreground' />
    <div className='flex flex-col'>
      <span className='font-medium'>
        {customer?.businessName || 'Sin cliente'}
      </span>
      {customer && (
        <span className='text-xs text-muted-foreground'>
          {customer.documentNumber}
        </span>
      )}
    </div>
  </div>
));

// Componente para el pago
const PaymentInfo = memo(
  ({ paymentMethod, total }: { paymentMethod: string; total: number }) => (
    <div className='flex items-center gap-3'>
      <CreditCard className='w-4 h-4 text-muted-foreground' />
      <div className='flex flex-col gap-1'>
        <Badge variant='outline' className='w-fit'>
          {PAYMENT_METHOD_LABELS[paymentMethod]}
        </Badge>
        <span className='font-medium text-primary'>
          {formatCurrency(total)}
        </span>
      </div>
    </div>
  )
);

// Componente para el estado
const StatusBadge = memo(({ status }: { status: Sale['status'] }) => (
  <Badge
    variant={status === 'CANCELED' ? 'destructive' : 'default'}
    className='w-fit'
  >
    {SALE_STATUS_LABELS[status]}
  </Badge>
));

export const getSaleTableColumns = ({
  permissions,
  onCancel,
  onDelete,
  onViewDetails,
}: SalesTableColumnProps): TableColumn[] => [
  {
    id: 'number',
    header: 'N° Venta',
    align: 'left',
    cell: (sale) => <SaleNumber sale={sale} />,
  },
  {
    id: 'date',
    header: 'Fecha',
    align: 'left',
    cell: (sale) => <SaleDate date={sale.date} />,
  },
  {
    id: 'customer',
    header: 'Cliente',
    align: 'left',
    cell: (sale) => <CustomerInfo customer={sale.customer} />,
  },
  {
    id: 'payment',
    header: 'Pago',
    align: 'left',
    cell: (sale) => (
      <PaymentInfo paymentMethod={sale.paymentMethod} total={sale.total} />
    ),
  },
  {
    id: 'status',
    header: 'Estado',
    align: 'left',
    cell: (sale) => <StatusBadge status={sale.status} />,
  },
  {
    id: 'actions',
    header: 'Acciones',
    align: 'right',
    cell: (sale) => (
      <div className='flex items-center gap-2 justify-end'>
        <ActionButton
          icon={Eye}
          tooltip='Ver detalles de la venta'
          onClick={() => onViewDetails(sale)}
        />
        {permissions.canCancel && (
          <ActionButton
            icon={XCircle}
            tooltip={
              sale.status === 'CANCELED'
                ? 'Esta venta ya está cancelada'
                : 'Cancelar venta'
            }
            onClick={() => onCancel(sale)}
            disabled={sale.status === 'CANCELED'}
          />
        )}
        {permissions.canDelete && (
          <ActionButton
            icon={Trash2}
            tooltip={
              sale.status !== 'CANCELED'
                ? 'Solo se pueden eliminar ventas canceladas'
                : 'Eliminar venta permanentemente'
            }
            onClick={() => onDelete(sale)}
            disabled={sale.status !== 'CANCELED'}
          />
        )}
      </div>
    ),
  },
];

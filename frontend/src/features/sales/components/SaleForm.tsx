import { useState, useEffect } from 'react';
import { useForm, UseFormReturn, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Save,
  Loader2,
  Trash2,
  Receipt,
  Plus,
  InfoIcon,
  Package,
  ListTree,
  AlertCircle,
} from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
  CardDescription,
} from '@/components/ui/card';
import { SaleProductSelector } from './SaleProductSelector';
import {
  createSaleSchema,
  type CreateSaleSchema,
} from '../schemas/sale.schema';
import {
  PAYMENT_METHODS,
  PAYMENT_METHOD_LABELS,
} from '../constants/sale.constants';
import { formatCurrency } from '@/lib/utils/format';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils/styles';
import { CustomerSelector } from '@/features/customers/components/CustomerSelector';
import type { SaleCustomer } from '../types/sale.types';
import type { CreateCustomerDto, Customer } from '@/features/customers';
import { CustomerCreateDialog } from '@/features/customers/components/CustomerCreateDialog';
import { useCreateSale } from '../hooks/useCreateSale';
import { useCustomers } from '@/features/customers/hooks/useCustomers';
import { Product } from '@/features/inventory/products';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { SaleItem } from '../types/sale.types';
import { Switch } from '@/components/ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { SaleConfirmDialog } from './SaleConfirmDialog';
import { SaleSuccessDialog } from './SaleSuccessDialog';
interface SaleFormProps {
  form: UseFormReturn<CreateSaleSchema>;
  products: Product[];
  customers: Customer[];
  onSubmit: (data: CreateSaleSchema) => Promise<void>; // Cambiado de Promise<SaleResponse>
  isLoading: boolean;
  onCancel: () => void;
  showCreateCustomer: boolean;
  setShowCreateCustomer: (show: boolean) => void;
  handleCreateCustomer: (data: CreateCustomerDto) => Promise<Customer>;
  isCreatingCustomer: boolean;
  onResetForm: () => void;
}

interface SaleFormData {
  customer?: string; // ID del cliente
  items: Array<{
    product: string; // ID del producto
    quantity: number;
    unitPrice: number;
    discount?: number;
  }>;
  paymentMethod: string;
  discountType: 'fixed' | 'percentage';
  discount: number;
  notes?: string;
  amountReceived?: number | null;
  total?: number;
}

interface SaleResponse {
  sale: {
    number: string;
    _id: string;
  };
  message: string;
}

export function SaleForm({
  form,
  products,
  customers,
  onSubmit,
  isLoading,
  onCancel,
  showCreateCustomer,
  setShowCreateCustomer,
  handleCreateCustomer,
  isCreatingCustomer,
  onResetForm,
}: SaleFormProps) {
  const [selectedItems, setSelectedItems] = useState<SaleItem[]>([]);
  const [quantityErrors, setQuantityErrors] = useState<Record<string, string>>(
    {}
  );
  const [generalDiscountType, setGeneralDiscountType] = useState<
    'fixed' | 'percentage'
  >('fixed');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formData, setFormData] = useState<CreateSaleSchema | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<SaleCustomer | null>(
    null
  );

  // Efecto para monitorear reseteos
  useEffect(() => {
    const values = form.getValues();
    if (values.items?.length === 0 && !values.customer) {
      resetLocalState();
    }
  }, [form.getValues()]);

  const resetLocalState = () => {
    setSelectedItems([]);
    setQuantityErrors({});
    setGeneralDiscountType('fixed');
    setShowConfirmDialog(false);
    setFormData(null);
    setSelectedCustomer(null);
  };

  const handleFullReset = () => {
    resetLocalState();
    onResetForm();
  };

  useEffect(() => {
    // Encontrar el cliente genérico de la lista de clientes
    const genericCustomer = customers.find(
      (customer) => customer.isGeneric && customer.isProtected
    );

    if (genericCustomer) {
      form.setValue('customer', genericCustomer._id);
      setSelectedCustomer(genericCustomer);
    }
  }, [customers, form]);

  // Sincronizar selectedItems con el formulario
  useEffect(() => {
    const formattedItems = selectedItems.map((item) => ({
      product: item.productId,
      quantity: item.quantity,
      unitPrice: item.unitPrice, // Convertir price a unitPrice
      discount: item.discount || 0,
    }));

    form.setValue('items', formattedItems, {
      shouldValidate: true,
      shouldDirty: true,
    });
  }, [selectedItems, form]);

  // Función para validar el formulario con logs
  const isFormValid = () => {
    const formValues = form.getValues();
    const formErrors = form.formState.errors;

    // Validar que haya items y no tengan errores
    const hasValidItems =
      selectedItems.length > 0 && Object.keys(quantityErrors).length === 0;

    // Validar monto recibido si es pago en efectivo
    const isCashPayment = formValues.paymentMethod === PAYMENT_METHODS.CASH;
    const hasValidCashAmount = isCashPayment
      ? formValues.amountReceived && formValues.amountReceived >= total
      : true;

    // Validar que no haya errores en el formulario
    const hasNoFormErrors = Object.keys(formErrors).length === 0;

    return hasValidItems && hasValidCashAmount && hasNoFormErrors;
  };

  // Manejar el submit del formulario con validación explícita
  const onFormSubmit = (data: CreateSaleSchema) => {
    onSubmit(data);
  };

  // Función para manejar el click en "Finalizar Venta"
  const handleFinalizeSale = () => {
    const data = form.getValues();
    setFormData(data);
    setShowConfirmDialog(true); // Mostrar diálogo de confirmación primero
  };

  // Función para manejar la confirmación
  const handleConfirmSale = async () => {
    if (!formData) return;

    try {
      await onSubmit(formData);
      setShowConfirmDialog(false); // Cerrar diálogo de confirmación
      setFormData(null);
    } catch (error) {
      console.error('Error al confirmar la venta:', error);
    }
  };

  // Función para prevenir entrada de caracteres no numéricos
  const handleNumericInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Permitir combinaciones con Ctrl/Cmd
    if (e.ctrlKey || e.metaKey) {
      // Permitir: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
      if (['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
        return;
      }
    }

    // Permitir: números, punto decimal, teclas de navegación y edición
    const allowedKeys = [
      'Backspace',
      'Delete',
      'ArrowLeft',
      'ArrowRight',
      'Tab',
      'Home',
      'End',
      '.',
    ];

    if (!allowedKeys.includes(e.key) && !/[0-9]/.test(e.key)) {
      e.preventDefault();
    }

    // Evitar múltiples puntos decimales
    if (e.key === '.' && e.currentTarget.value.includes('.')) {
      e.preventDefault();
    }
  };

  // Asegurarse de que items exista antes de usarlo
  useEffect(() => {
    const currentItems = form.getValues('items') || [];
    if (!Array.isArray(currentItems)) {
      form.setValue('items', []);
    }
  }, [form]);

  const handleCustomerCreate = async (customerData: CreateCustomerDto) => {
    try {
      const newCustomer = await handleCreateCustomer(customerData);
      // Asegurarnos de que el cliente se seleccione después de crearlo
      form.setValue('customer', newCustomer._id);
      return newCustomer;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  };

  const calculateItemSubtotal = (
    quantity: number,
    price: number,
    discount: number
  ) => {
    const subtotalBeforeDiscount = quantity * price;
    return subtotalBeforeDiscount - subtotalBeforeDiscount * (discount / 100);
  };

  const handleDiscountChange = (productId: string, discount: number) => {
    if (discount < 0 || discount > 100) return;

    setSelectedItems((items) =>
      items.map((item) =>
        item.productId === productId
          ? {
              ...item,
              discount,
              subtotal: calculateItemSubtotal(
                item.quantity,
                item.unitPrice,
                discount
              ),
            }
          : item
      )
    );

    // Actualizar el form
    const updatedItems = selectedItems.map((item) => ({
      product: item.productId,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discount: item.productId === productId ? discount : item.discount || 0,
    }));
    form.setValue('items', updatedItems);
  };

  const handlePriceChange = (productId: string, value: string) => {
    if (value === '') {
      setSelectedItems((items) =>
        items.map((item) =>
          item.productId === productId
            ? {
                ...item,
                unitPrice: 0,
                subtotal: calculateItemSubtotal(
                  item.quantity,
                  0,
                  item.discount || 0
                ),
              }
            : item
        )
      );
      return;
    }

    const newPrice = Number(value);
    if (isNaN(newPrice) || newPrice < 0) return;

    setSelectedItems((items) =>
      items.map((item) =>
        item.productId === productId
          ? {
              ...item,
              unitPrice: newPrice,
              subtotal: calculateItemSubtotal(
                item.quantity,
                newPrice,
                item.discount || 0
              ),
            }
          : item
      )
    );

    const updatedItems = selectedItems.map((item) => ({
      product: item.productId,
      quantity: item.quantity,
      unitPrice: item.productId === productId ? newPrice : item.unitPrice,
      discount: item.discount || 0,
    }));
    form.setValue('items', updatedItems);
  };

  const totalItems = selectedItems.reduce(
    (acc, item) => acc + item.quantity,
    0
  );
  const subtotalBeforeDiscount = selectedItems.reduce(
    (acc, item) => acc + item.quantity * item.unitPrice,
    0
  );
  const itemsDiscountTotal = selectedItems.reduce(
    (acc, item) =>
      acc + (item.quantity * item.unitPrice * (item.discount || 0)) / 100,
    0
  );
  const subtotal = subtotalBeforeDiscount - itemsDiscountTotal;

  const additionalDiscountAmount =
    form.watch('discountType') === 'percentage'
      ? subtotal * (form.watch('discount') / 100)
      : form.watch('discount') || 0;

  const total = subtotal - additionalDiscountAmount;

  const handleAddProduct = (product: Product) => {
    const existingItem = selectedItems.find(
      (item) => item.productId === product._id
    );

    if (existingItem) {
      return;
    }

    const newItem: SaleItem = {
      productId: product._id,
      name: product.name,
      quantity: 1,
      unitPrice: product.price,
      stock: product.stock,
      subtotal: product.price,
    };

    setSelectedItems((prev) => [...prev, newItem]);
  };

  const handleQuantityChange = (productId: string, quantity: number) => {
    if (quantity < 1) return;

    const product = products.find((p) => p._id === productId);
    if (!product) return;

    if (quantity > product.stock) {
      setQuantityErrors((prev) => ({
        ...prev,
        [productId]: 'Cantidad excede el stock disponible',
      }));
      return;
    }

    setQuantityErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[productId];
      return newErrors;
    });

    setSelectedItems((items) =>
      items.map((item) =>
        item.productId === productId
          ? { ...item, quantity, subtotal: quantity * item.unitPrice }
          : item
      )
    );
  };

  const handleRemoveItem = (productId: string) => {
    setSelectedItems((items) =>
      items.filter((item) => item.productId !== productId)
    );
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onFormSubmit)}
          noValidate
          className='space-y-6'
        >
          <div className='grid grid-cols-1 lg:grid-cols-[1fr,400px] gap-6'>
            <div className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg font-medium'>Cliente</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name='customer'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex gap-2'>
                          <FormControl className='flex-1'>
                            <CustomerSelector
                              customers={customers}
                              value={field.value || ''}
                              onChange={(value) => {
                                if (value) {
                                  field.onChange(value);
                                  // Find and set the selected customer
                                  const customer = customers.find(
                                    (c) => c._id === value
                                  );
                                  setSelectedCustomer(customer || null);
                                  form.trigger('customer');
                                }
                              }}
                              disabled={isLoading}
                              showCreateDialog={showCreateCustomer}
                              onShowCreateDialogChange={setShowCreateCustomer}
                              onCreateCustomer={handleCreateCustomer}
                              isCreatingCustomer={isCreatingCustomer}
                            />
                          </FormControl>
                          <Button
                            type='button'
                            variant='outline'
                            size='icon'
                            onClick={() => setShowCreateCustomer(true)}
                            disabled={isLoading}
                          >
                            <Plus className='h-4 w-4' />
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <CustomerCreateDialog
                    open={showCreateCustomer}
                    onOpenChange={setShowCreateCustomer}
                    onSubmit={handleCustomerCreate}
                    isLoading={isCreatingCustomer}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='flex flex-row items-center justify-between'>
                  <CardTitle className='text-lg font-medium'>
                    Productos
                  </CardTitle>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>Total Items: {totalItems}</Badge>
                  </div>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <SaleProductSelector
                    products={products.filter((p) => p.stock > 0)}
                    onProductSelect={handleAddProduct}
                  />

                  <ScrollArea className='h-[400px] border rounded-md'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Producto</TableHead>
                          <TableHead className='w-[100px] text-right'>
                            Cantidad
                          </TableHead>
                          <TableHead className='w-[150px] text-right'>
                            Precio Unit.
                          </TableHead>
                          <TableHead className='w-[100px] text-right'>
                            Desc. %
                          </TableHead>
                          <TableHead className='w-[150px] text-right'>
                            Subtotal
                          </TableHead>
                          <TableHead className='w-[50px]'></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedItems.length === 0 ? (
                          <TableRow>
                            <TableCell
                              colSpan={5}
                              className='h-24 text-center text-muted-foreground'
                            >
                              No hay productos seleccionados
                            </TableCell>
                          </TableRow>
                        ) : (
                          selectedItems.map((item) => (
                            <TableRow key={item.productId}>
                              <TableCell className='font-medium'>
                                {item.name}
                              </TableCell>
                              <TableCell className='text-right'>
                                <div className='space-y-1'>
                                  <Input
                                    type='number'
                                    min={1}
                                    value={item.quantity}
                                    onChange={(e) =>
                                      handleQuantityChange(
                                        item.productId,
                                        parseInt(e.target.value) || 0
                                      )
                                    }
                                    className={cn(
                                      'w-20 text-right',
                                      quantityErrors[item.productId] &&
                                        'border-destructive'
                                    )}
                                  />
                                  {quantityErrors[item.productId] && (
                                    <div className='flex items-center gap-1 text-xs text-destructive'>
                                      <AlertCircle className='h-3 w-3' />
                                      <span>Excede stock ({item.stock})</span>
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className='text-right'>
                                <Input
                                  type='number'
                                  min={0}
                                  step='0.01'
                                  value={item.unitPrice || ''}
                                  onChange={(e) =>
                                    handlePriceChange(
                                      item.productId,
                                      e.target.value
                                    )
                                  }
                                  onKeyDown={handleNumericInput}
                                  className='w-28 text-right'
                                />
                              </TableCell>
                              <TableCell className='text-right'>
                                <Input
                                  type='number'
                                  min={0}
                                  max={100}
                                  value={item.discount?.toString() || '0'}
                                  onChange={(e) =>
                                    handleDiscountChange(
                                      item.productId,
                                      parseFloat(e.target.value) || 0
                                    )
                                  }
                                  className='w-20 text-right'
                                />
                              </TableCell>
                              <TableCell className='text-right font-medium'>
                                {formatCurrency(item.subtotal)}
                              </TableCell>
                              <TableCell>
                                <Button
                                  type='button'
                                  variant='ghost'
                                  size='icon'
                                  onClick={() =>
                                    handleRemoveItem(item.productId)
                                  }
                                >
                                  <Trash2 className='h-4 w-4' />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </ScrollArea>

                  {/* Notas Adicionales */}
                  <Separator className='my-4' />
                  <FormField
                    control={form.control}
                    name='notes'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notas Adicionales</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder='Notas adicionales...'
                            className='resize-none'
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            <div className='space-y-6'>
              {/* Card de Pago y Resumen */}
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg font-medium'>
                    Detalle de Pago
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  {/* Método de Pago */}
                  <FormField
                    control={form.control}
                    name='paymentMethod'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Método de Pago</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Seleccione método de pago' />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(PAYMENT_METHODS).map(
                              ([key, value]) => (
                                <SelectItem key={value} value={value}>
                                  {PAYMENT_METHOD_LABELS[key]}
                                </SelectItem>
                              )
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Descuento General */}
                  <div className='space-y-4 rounded-lg border p-4'>
                    <div className='flex items-center justify-between'>
                      <FormLabel>Descuento General</FormLabel>
                      <Select
                        value={generalDiscountType}
                        onValueChange={(value: 'fixed' | 'percentage') => {
                          setGeneralDiscountType(value);
                          form.setValue('discountType', value);
                        }}
                      >
                        <SelectTrigger className='w-[120px]'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='fixed'>Monto Fijo</SelectItem>
                          <SelectItem value='percentage'>Porcentaje</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <FormField
                      control={form.control}
                      name='discount'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type='number'
                              placeholder={
                                generalDiscountType === 'percentage'
                                  ? '0'
                                  : '0.00'
                              }
                              {...field}
                              value={field.value ?? ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === '') {
                                  field.onChange(null);
                                  return;
                                }
                                field.onChange(Number(value));
                              }}
                              onKeyDown={handleNumericInput}
                              min={0}
                              step={
                                generalDiscountType === 'percentage'
                                  ? '1'
                                  : '0.01'
                              }
                              className={cn(
                                'w-full',
                                form.formState.errors.discount &&
                                  'border-destructive'
                              )}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Resumen de Montos */}
                  <div className='space-y-2 rounded-lg bg-muted p-4'>
                    <div className='flex justify-between text-sm'>
                      <span>Subtotal:</span>
                      <span>{formatCurrency(subtotalBeforeDiscount)}</span>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <span>Descuento productos:</span>
                      <span className='text-red-500'>
                        -{formatCurrency(itemsDiscountTotal)}
                      </span>
                    </div>
                    <div className='flex justify-between text-sm'>
                      <span>Descuento general:</span>
                      <span className='text-red-500'>
                        -{formatCurrency(additionalDiscountAmount)}
                      </span>
                    </div>
                    <Separator className='my-2' />
                    <div className='flex justify-between font-medium'>
                      <span>Total a pagar:</span>
                      <span className='text-lg'>{formatCurrency(total)}</span>
                    </div>
                  </div>

                  {/* Monto Recibido - Solo mostrar si es pago en efectivo */}
                  {form.watch('paymentMethod') === PAYMENT_METHODS.CASH && (
                    <FormField
                      control={form.control}
                      name='amountReceived'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Monto Recibido</FormLabel>
                          <FormControl>
                            <Input
                              type='number'
                              placeholder='0.00'
                              {...field}
                              value={field.value ?? ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === '') {
                                  field.onChange(null);
                                  return;
                                }
                                field.onChange(Number(value));
                              }}
                              onKeyDown={handleNumericInput}
                              min={0}
                              step='0.01'
                              className={cn(
                                'w-full',
                                form.formState.errors.amountReceived &&
                                  'border-destructive'
                              )}
                            />
                          </FormControl>
                          {field.value && field.value > total && (
                            <FormDescription>
                              Vuelto: {formatCurrency(field.value - total)}
                            </FormDescription>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Removemos la sección de Facturación */}
                </CardContent>
              </Card>

              <div className='flex gap-4'>
                <Button
                  type='button'
                  variant='outline'
                  className='w-full'
                  onClick={onCancel}
                >
                  Cancelar
                </Button>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className='w-full'>
                        <Button
                          type='button'
                          className='w-full'
                          disabled={!isFormValid()}
                          onClick={handleFinalizeSale}
                        >
                          <Receipt className='mr-2 h-4 w-4' />
                          Finalizar Venta
                        </Button>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      {!selectedItems.length &&
                        'Debe agregar al menos un producto. '}
                      {Object.keys(quantityErrors).length > 0 &&
                        'Hay errores en las cantidades. '}
                      {form.watch('paymentMethod') === PAYMENT_METHODS.CASH &&
                        (!form.watch('amountReceived') ||
                          form.watch('amountReceived') < total) &&
                        'El monto recibido debe ser mayor o igual al total. '}
                      {Object.keys(form.formState.errors).length > 0 &&
                        'Hay campos con errores.'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </form>
      </Form>

      <SaleConfirmDialog
        open={showConfirmDialog}
        onOpenChange={(open) => {
          if (!open) setFormData(null);
          setShowConfirmDialog(open);
        }}
        onConfirm={handleConfirmSale}
        onCancel={() => setShowConfirmDialog(false)}
        isLoading={isLoading}
        saleData={{
          items: selectedItems.map((item) => ({
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice, // Cambiado de price a unitPrice
            subtotal: item.subtotal,
          })),
          paymentMethod: formData?.paymentMethod || '',
          total: total,
          amountReceived: formData?.amountReceived,
          customer: selectedCustomer
            ? {
                businessName: selectedCustomer.businessName,
                documentNumber: selectedCustomer.documentNumber,
              }
            : undefined,
        }}
      />
    </>
  );
}

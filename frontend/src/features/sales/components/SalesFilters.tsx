import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SALE_STATUS, SALE_STATUS_LABELS } from '../constants/sale.constants';
import type { SaleFilters } from '../types/sale.types';

interface SalesFiltersProps {
  filters: {
    search: string;
    status?: keyof typeof SALE_STATUS;
    page: number;
    limit: number;
  };
  onFiltersChange: (filters: Partial<SaleFilters>) => void;
}

export function SalesFilters({ filters, onFiltersChange }: SalesFiltersProps) {
  return (
    <div className='flex flex-col gap-4 sm:flex-row sm:items-center'>
      <div className='relative flex-1'>
        <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
        <Input
          placeholder='Buscar por número o cliente...'
          value={filters.search}
          onChange={(e) => onFiltersChange({ search: e.target.value, page: 1 })}
          className='pl-8'
        />
      </div>
      <Select
        value={filters.status || 'ALL'}
        onValueChange={(value) =>
          onFiltersChange({
            status:
              value === 'ALL' ? undefined : (value as keyof typeof SALE_STATUS),
            page: 1,
          })
        }
      >
        <SelectTrigger className='w-full sm:w-[180px]'>
          <SelectValue placeholder='Estado' />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='ALL'>Todos</SelectItem>
          {Object.entries(SALE_STATUS).map(([key, value]) => (
            <SelectItem key={key} value={value}>
              {SALE_STATUS_LABELS[value]}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

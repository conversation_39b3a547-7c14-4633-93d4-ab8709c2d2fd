import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  PAYMENT_METHOD_LABELS,
  PAYMENT_METHODS,
} from '../constants/sale.constants';
import { formatCurrency } from '@/lib/utils/format';
import {
  Receipt,
  CreditCard,
  Wallet,
  CircleDollarSign,
  FileText,
  User,
  Package2,
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface SaleConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading: boolean;
  saleData: {
    items: {
      name: string;
      quantity: number;
      unitPrice: number;
      subtotal: number;
    }[];
    paymentMethod: string;
    total: number;
    amountReceived?: number;
    customer?: {
      businessName: string;
      documentNumber: string;
    };
  };
}

export function SaleConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  isLoading,
  saleData,
}: SaleConfirmDialogProps) {
  const getPaymentIcon = (method: string) => {
    switch (method) {
      case PAYMENT_METHODS.CASH:
        return <Wallet className='h-4 w-4' />;
      case PAYMENT_METHODS.CARD:
        return <CreditCard className='h-4 w-4' />;
      default:
        return <CircleDollarSign className='h-4 w-4' />;
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='max-w-xl max-h-[85vh] flex flex-col'>
        <AlertDialogHeader>
          <AlertDialogTitle className='flex items-center gap-2 text-xl'>
            <Receipt className='h-5 w-5 text-primary' />
            Confirmar Venta
          </AlertDialogTitle>
          <AlertDialogDescription>
            Revise los detalles de la venta antes de confirmar
          </AlertDialogDescription>
        </AlertDialogHeader>

        <ScrollArea className='flex-1'>
          <div className='space-y-6 py-4'>
            {/* Cliente */}
            {saleData.customer && (
              <div className='rounded-lg border bg-card p-4'>
                <div className='flex items-center gap-2 mb-3'>
                  <User className='h-4 w-4 text-muted-foreground' />
                  <h4 className='font-medium'>Información del Cliente</h4>
                </div>
                <div className='space-y-2'>
                  {saleData.customer ? (
                    <>
                      <p className='text-sm font-medium'>
                        {saleData.customer.businessName}
                      </p>
                      <p className='text-sm text-muted-foreground'>
                        NIT: {saleData.customer.documentNumber}
                      </p>
                    </>
                  ) : (
                    <p className='text-sm text-muted-foreground'>
                      Sin cliente registrado
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Productos */}
            <div className='rounded-lg border bg-card p-4'>
              <div className='flex items-center gap-2 mb-3'>
                <Package2 className='h-4 w-4 text-muted-foreground' />
                <h4 className='font-medium'>Detalle de Productos</h4>
              </div>
              <div className='space-y-3'>
                {saleData.items.map((item, index) => (
                  <div
                    key={index}
                    className='flex items-center justify-between py-2 border-b last:border-0'
                  >
                    <div className='space-y-1'>
                      <p className='font-medium'>{item.name}</p>
                      <p className='text-sm text-muted-foreground'>
                        {item.quantity} x {formatCurrency(item.unitPrice)}
                      </p>
                    </div>
                    <span className='font-medium'>
                      {formatCurrency(item.subtotal)}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Información de Pago */}
            <div className='rounded-lg border bg-card p-4'>
              <div className='flex items-center gap-2 mb-3'>
                {getPaymentIcon(saleData.paymentMethod)}
                <h4 className='font-medium'>Información de Pago</h4>
              </div>

              <div className='space-y-3'>
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>Método de pago</span>
                  <span>
                    {
                      PAYMENT_METHOD_LABELS[
                        Object.entries(PAYMENT_METHODS).find(
                          ([, value]) => value === saleData.paymentMethod
                        )?.[0] ?? ''
                      ]
                    }
                  </span>
                </div>

                {saleData.paymentMethod === PAYMENT_METHODS.CASH &&
                  saleData.amountReceived && (
                    <>
                      <div className='flex justify-between text-sm'>
                        <span className='text-muted-foreground'>
                          Monto recibido
                        </span>
                        <span>{formatCurrency(saleData.amountReceived)}</span>
                      </div>
                      <div className='flex justify-between text-sm'>
                        <span className='text-muted-foreground'>Vuelto</span>
                        <span>
                          {formatCurrency(
                            saleData.amountReceived - saleData.total
                          )}
                        </span>
                      </div>
                    </>
                  )}

                <Separator />

                <div className='flex justify-between font-medium'>
                  <span>Total a pagar</span>
                  <span className='text-lg text-primary'>
                    {formatCurrency(saleData.total)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        <AlertDialogFooter className='mt-6'>
          <AlertDialogCancel disabled={isLoading} onClick={onCancel}>
            Cancelar
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className='bg-primary'
          >
            {isLoading ? (
              <div className='flex items-center gap-2'>
                <span className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent' />
                Procesando...
              </div>
            ) : (
              'Confirmar Venta'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

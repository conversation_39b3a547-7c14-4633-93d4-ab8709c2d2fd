import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SalesFilters } from './SalesFilters';
import type { SalePermissions } from '../types/sale.types';

interface SalesHeaderProps {
  search: string;
  status?: string;
  onSearchChange: (search: string) => void;
  onStatusChange: (status: string | undefined) => void;
  onCreateSale: () => void;
  permissions: SalePermissions;
}

export function SalesHeader({
  search,
  status,
  onSearchChange,
  onStatusChange,
  onCreateSale,
  permissions,
}: SalesHeaderProps) {
  return (
    <div className='flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
      <SalesFilters
        search={search}
        status={status}
        onSearchChange={onSearchChange}
        onStatusChange={onStatusChange}
      />
      {permissions.canCreate && (
        <Button onClick={onCreateSale} className='w-full sm:w-auto'>
          <Plus className='mr-2 h-4 w-4' />
          Nueva Venta
        </Button>
      )}
    </div>
  );
}
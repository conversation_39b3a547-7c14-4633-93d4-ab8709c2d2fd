import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { CheckCircle2, Printer, FileDown, Plus } from 'lucide-react';
import { useSaleReceipt } from '../hooks/useSaleReceipt';
import type { Sale } from '../types/sale.types';
import { formatCurrency } from '@/lib/utils/format';

interface SaleSuccessDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sale: Sale;
  onNewSale: () => void;
}

export function SaleSuccessDialog({
  open,
  onOpenChange,
  sale,
  onNewSale,
}: SaleSuccessDialogProps) {
  const { generatePDF, printPDF } = useSaleReceipt();

  if (!sale) return null;

  const handlePrint = async () => {
    try {
      await printPDF(sale);
    } catch (error) {
      console.error('Error al imprimir:', error);
    }
  };

  const handleSavePDF = async () => {
    try {
      await generatePDF(sale);
    } catch (error) {
      console.error('Error al generar PDF:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex flex-col items-center text-center pb-4'>
            <div className='h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4'>
              <CheckCircle2 className='h-8 w-8 text-green-600' />
            </div>
            <DialogTitle className='text-2xl font-semibold text-green-600'>
              ¡Venta Exitosa!
            </DialogTitle>
            <DialogDescription className='mt-2'>
              La venta #{sale.number} se ha registrado correctamente
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className='space-y-4 py-4'>
          {/* Resumen de la venta */}
          <div className='bg-muted rounded-lg p-4'>
            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span className='text-muted-foreground'>Total</span>
                <span className='font-medium'>
                  {formatCurrency(sale.total)}
                </span>
              </div>
              {sale.customer && (
                <div className='flex justify-between text-sm'>
                  <span className='text-muted-foreground'>Cliente</span>
                  <span className='font-medium'>
                    {sale.customer.businessName}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Acciones */}
          <div className='grid grid-cols-3 gap-3'>
            <Button
              variant='outline'
              className='flex flex-col items-center gap-2 h-auto py-4'
              onClick={handlePrint}
            >
              <Printer className='h-5 w-5 text-primary' />
              <span className='text-xs'>Imprimir</span>
            </Button>
            <Button
              variant='outline'
              className='flex flex-col items-center gap-2 h-auto py-4'
              onClick={handleSavePDF}
            >
              <FileDown className='h-5 w-5 text-primary' />
              <span className='text-xs'>Guardar PDF</span>
            </Button>
            <Button
              variant='outline'
              className='flex flex-col items-center gap-2 h-auto py-4'
              onClick={onNewSale}
            >
              <Plus className='h-5 w-5 text-primary' />
              <span className='text-xs'>Nueva Venta</span>
            </Button>
          </div>
        </div>

        <div className='flex justify-center pt-4'>
          <p className='text-xs text-muted-foreground text-center'>
            Puedes acceder a esta venta más tarde desde el historial de ventas
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}

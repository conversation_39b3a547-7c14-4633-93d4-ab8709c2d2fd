import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { AlertCircle } from 'lucide-react';
import type { Sale } from '../types/sale.types';
import { formatDate } from '@/lib/utils/format';

interface SaleDeleteDialogProps {
  sale: Sale | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function SaleDeleteDialog({
  sale,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: SaleDeleteDialogProps) {
  if (!sale) return null;

  const handleOpenChange = (newOpen: boolean) => {
    if (isLoading) return;
    onOpenChange(newOpen);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className='flex items-center gap-2 text-destructive'>
            <AlertCircle className='h-5 w-5' />
            Eliminar Venta #{sale.number}
          </AlertDialogTitle>
          <AlertDialogDescription>
            ¿Está seguro que desea eliminar permanentemente esta venta anulada
            del {formatDate(sale.date)}? Esta acción no se puede deshacer.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className='bg-destructive hover:bg-destructive/90 text-white'
          >
            {isLoading ? (
              <div className='flex items-center gap-2'>
                <span className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent' />
                Procesando...
              </div>
            ) : (
              'Eliminar Venta'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

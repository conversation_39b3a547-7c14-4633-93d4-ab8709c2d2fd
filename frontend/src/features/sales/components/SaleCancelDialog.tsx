import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { Package2, AlertCircle } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useState } from 'react';
import type { Sale } from '../types/sale.types';
import { formatCurrency, formatDate } from '@/lib/utils/format';
import { SALE_STATUS_LABELS } from '../constants/sale.constants';

interface SaleCancelDialogProps {
  sale: Sale | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (reason: string) => void;
  isLoading?: boolean;
}

export function SaleCancelDialog({
  sale,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: SaleCancelDialogProps) {
  const [reason, setReason] = useState('');

  if (!sale) return null;

  const handleOpenChange = (newOpen: boolean) => {
    if (isLoading) return;

    if (!newOpen) {
      setReason('');
    }
    onOpenChange(newOpen);
  };

  const handleConfirm = () => {
    onConfirm(reason);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className='max-w-xl'>
        <AlertDialogHeader>
          <AlertDialogTitle className='flex items-center gap-2 text-destructive'>
            <AlertCircle className='h-5 w-5' />
            Anular Venta #{sale.number}
          </AlertDialogTitle>
          <div className='space-y-2'>
            <AlertDialogDescription>
              Esta acción anulará la venta y realizará los siguientes cambios en
              el sistema:
            </AlertDialogDescription>
            <ul className='list-disc list-inside text-sm text-muted-foreground'>
              <li>Los productos serán devueltos al inventario</li>
              <li>Se registrará la anulación en el historial de la venta</li>
              <li>Se generará un registro de caja por la anulación</li>
              <li>El estado de la venta cambiará a "Anulada"</li>
            </ul>
          </div>
        </AlertDialogHeader>

        <div className='space-y-4 py-4'>
          {/* Información del cliente */}
          {sale.customer && (
            <div className='rounded-lg border bg-card p-4'>
              <div className='space-y-1'>
                <h4 className='font-medium'>{sale.customer.businessName}</h4>
                <p className='text-sm text-muted-foreground'>
                  {sale.customer.documentNumber}
                </p>
              </div>
            </div>
          )}

          {/* Productos a devolver */}
          <div className='rounded-lg border bg-card p-4'>
            <div className='flex items-center gap-2 mb-3'>
              <Package2 className='h-4 w-4 text-muted-foreground' />
              <h4 className='font-medium'>
                Productos que se devolverán al inventario
              </h4>
            </div>
            <div className='space-y-2'>
              {sale.items.map((item) => (
                <div key={item._id} className='flex justify-between text-sm'>
                  <div className='flex flex-col'>
                    <span>{item.product.name}</span>
                    <span className='text-xs text-muted-foreground'>
                      SKU: {item.product.sku}
                    </span>
                  </div>
                  <div className='text-right'>
                    <span className='font-medium'>
                      {item.quantity} unidades
                    </span>
                    <div className='text-xs text-muted-foreground'>
                      {formatCurrency(item.unitPrice)} c/u
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Información de la venta */}
          <div className='rounded-lg border bg-card p-4 space-y-3'>
            <div className='space-y-1'>
              <h4 className='font-medium'>Detalles de la venta</h4>
              <p className='text-sm text-muted-foreground'>
                Fecha: {formatDate(sale.date)}
              </p>
            </div>

            <Separator />

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Total de la venta:</span>
                <span className='font-medium'>
                  {formatCurrency(sale.total)}
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span>Estado actual:</span>
                <span className='font-medium'>
                  {SALE_STATUS_LABELS[sale.status]}
                </span>
              </div>
            </div>
          </div>

          {/* Motivo de anulación */}
          <div className='space-y-2'>
            <label htmlFor='reason' className='text-sm font-medium'>
              Motivo de anulación *
            </label>
            <Textarea
              id='reason'
              placeholder='Ingrese el motivo de la anulación'
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className='resize-none'
              rows={3}
            />
          </div>

          {/* Total a anular */}
          <div className='rounded-lg border bg-destructive/10 p-4'>
            <div className='flex justify-between text-destructive'>
              <span className='font-medium'>Total a anular:</span>
              <span className='text-lg font-semibold'>
                {formatCurrency(sale.total)}
              </span>
            </div>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading || !reason.trim()}
            className='bg-destructive hover:bg-destructive/90 text-white'
          >
            {isLoading ? (
              <div className='flex items-center gap-2'>
                <span className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent' />
                Procesando...
              </div>
            ) : (
              'Confirmar Anulación'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

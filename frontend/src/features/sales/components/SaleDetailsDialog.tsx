import {
  Receipt,
  User,
  Calendar,
  CreditCard,
  <PERSON>lipboard<PERSON>ist,
  Calculator,
  Printer,
  FileDown,
  XCircle,
  Trash2,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { formatDate, formatCurrency } from '@/lib/utils/format';
import {
  SALE_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
  SALE_STATUS,
} from '../constants/sale.constants';
import { useSaleReceipt } from '../hooks/useSaleReceipt';
import type { Sale } from '../types/sale.types';

interface SaleDetailsDialogProps {
  sale: Sale | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel?: (sale: Sale) => void;
  onDelete?: (sale: Sale) => void;
  permissions?: {
    canCancel?: boolean;
    canDelete?: boolean;
  };
  hideActions?: boolean; // Nuevo prop para ocultar las acciones
}

export function SaleDetailsDialog({
  sale,
  open,
  onOpenChange,
  onCancel,
  onDelete,
  permissions = { canCancel: false, canDelete: false },
  hideActions = false, // Por defecto, mostrar las acciones
}: SaleDetailsDialogProps) {
  const { generatePDF, printPDF } = useSaleReceipt();

  if (!sale) return null;

  const handlePrint = async () => {
    try {
      await printPDF(sale);
    } catch (error) {
      console.error('Error al imprimir:', error);
    }
  };

  const handleSavePDF = async () => {
    try {
      await generatePDF(sale);
    } catch (error) {
      console.error('Error al generar PDF:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex flex-col min-h-fit max-h-[85vh] w-full sm:w-[85vw] md:w-[75vw] lg:w-[65vw] max-w-[900px] p-4 sm:p-6'>
        <DialogHeader className='pb-3 sm:pb-4 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl font-semibold'>
            <Receipt className='w-4 h-4 sm:w-5 sm:h-5' />
            Detalles de Venta
          </DialogTitle>
          <div className='flex items-center justify-between'>
            <p className='text-xs sm:text-sm text-muted-foreground text-left'>
              Venta #{sale.number}
            </p>
            <Badge
              variant={
                sale.status === SALE_STATUS.CANCELED ? 'destructive' : 'default'
              }
              className='w-fit'
            >
              {SALE_STATUS_LABELS[sale.status]}
            </Badge>
          </div>
        </DialogHeader>

        <ScrollArea className='flex-1 pr-4'>
          {/* Información General */}
          <div className='space-y-6'>
            {/* Fecha y Cliente */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <h3 className='font-medium flex items-center gap-2'>
                  <Calendar className='w-4 h-4' />
                  Fecha
                </h3>
                <p className='text-sm'>{formatDate(sale.date)}</p>
              </div>
              <div className='space-y-2'>
                <h3 className='font-medium flex items-center gap-2'>
                  <User className='w-4 h-4' />
                  Cliente
                </h3>
                <div className='text-sm'>
                  <p className='font-medium'>
                    {sale.customer?.businessName || 'Sin cliente'}
                  </p>
                  {sale.customer && (
                    <p className='text-muted-foreground'>
                      {sale.customer.documentNumber}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Productos */}
            <div className='space-y-2'>
              <h3 className='font-medium flex items-center gap-2'>
                <ClipboardList className='w-4 h-4' />
                Productos
              </h3>
              <div className='border rounded-lg'>
                <div className='grid grid-cols-4 gap-4 p-3 border-b bg-muted/50 text-sm font-medium'>
                  <div className='col-span-2'>Producto</div>
                  <div className='text-right'>Cantidad</div>
                  <div className='text-right'>Subtotal</div>
                </div>
                <div className='divide-y'>
                  {sale.items.map((item) => (
                    <div
                      key={item._id}
                      className='grid grid-cols-4 gap-4 p-3 text-sm'
                    >
                      <div className='col-span-2'>
                        <div>{item.product.name}</div>
                        <div className='text-xs text-muted-foreground'>
                          SKU: {item.product.sku}
                        </div>
                      </div>
                      <div className='text-right'>
                        {item.quantity} x {formatCurrency(item.unitPrice)}
                        {item.discount > 0 && (
                          <div className='text-xs text-muted-foreground'>
                            Desc: {item.discount}%
                          </div>
                        )}
                      </div>
                      <div className='text-right'>
                        {formatCurrency(item.subtotal)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Pago */}
            <div className='space-y-2'>
              <h3 className='font-medium flex items-center gap-2'>
                <Calculator className='w-4 h-4' />
                Detalles de Pago
              </h3>
              <div className='bg-muted/30 rounded-lg p-4 space-y-3'>
                <div className='flex items-center gap-2'>
                  <CreditCard className='w-4 h-4 text-muted-foreground' />
                  <span className='text-sm'>
                    {PAYMENT_METHOD_LABELS[sale.paymentMethod]}
                  </span>
                </div>
                <Separator />
                <div className='flex justify-between font-medium'>
                  <span>Total</span>
                  <span className='text-lg text-primary'>
                    {formatCurrency(sale.total)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Acciones */}
        <div className='mt-6 border-t pt-4'>
          <div className='flex items-center justify-end gap-2'>
            <Button
              variant='outline'
              className='flex items-center gap-2'
              onClick={handlePrint}
            >
              <Printer className='h-4 w-4' />
              <span>Imprimir</span>
            </Button>
            <Button
              variant='outline'
              className='flex items-center gap-2'
              onClick={handleSavePDF}
            >
              <FileDown className='h-4 w-4' />
              <span>Guardar PDF</span>
            </Button>
            {!hideActions && permissions.canCancel && (
              <Button
                variant='outline'
                className='flex items-center gap-2 text-destructive hover:text-destructive hover:bg-destructive/10'
                onClick={() => onCancel?.(sale)}
                disabled={sale.status === SALE_STATUS.CANCELED}
              >
                <XCircle className='h-4 w-4' />
                <span>Cancelar</span>
              </Button>
            )}
            {!hideActions && permissions.canDelete && (
              <Button
                variant='outline'
                className='flex items-center gap-2 text-destructive hover:text-destructive hover:bg-destructive/10'
                onClick={() => onDelete?.(sale)}
                disabled={sale.status !== SALE_STATUS.CANCELED}
              >
                <Trash2 className='h-4 w-4' />
                <span>Eliminar</span>
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

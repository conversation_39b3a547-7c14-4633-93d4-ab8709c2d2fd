import { useState } from 'react';
import { Plus, Search, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils/styles';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils/format';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface Product {
  _id: string;
  name: string;
  sku: string;
  price: number;
  stock: number;
}

interface SaleProductSelectorProps {
  products: Product[];
  onProductSelect: (product: Product) => void;
  isLoading?: boolean;
}

export function SaleProductSelector({
  products = [], // Valor por defecto
  onProductSelect,
  isLoading = false,
}: SaleProductSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Asegurarnos de que products sea siempre un array
  const safeProducts = Array.isArray(products) ? products : [];

  const filteredProducts = safeProducts.filter(
    (product) =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelect = (product: Product) => {
    onProductSelect(product);
    setIsOpen(false);
    setSearchQuery('');
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className='w-full justify-between'
          disabled={isLoading}
          type='button' // Importante: especificar tipo para evitar submit accidental
        >
          <div className='flex items-center gap-2'>
            <Plus className='h-4 w-4' />
            <span>Agregar Producto</span>
          </div>
          <Search className='h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[var(--radix-popover-trigger-width)] p-0 max-w-screen-md'
        align='start'
        side='bottom'
        sideOffset={4}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder='Buscar producto por nombre o SKU...'
            value={searchQuery}
            onValueChange={setSearchQuery}
            className='px-4 py-3'
          />
          <CommandList>
            <CommandEmpty className='py-6 text-center text-sm'>
              No se encontraron productos
            </CommandEmpty>
            <CommandGroup>
              <ScrollArea className='h-[350px]'>
                {filteredProducts.map((product) => (
                  <CommandItem
                    key={product._id}
                    value={product._id}
                    onSelect={() => handleSelect(product)}
                    className='px-4 py-3 flex items-center justify-between hover:bg-accent'
                  >
                    <div className='flex items-center gap-3 flex-1'>
                      <Package className='h-5 w-5 text-muted-foreground' />
                      <div className='flex flex-col flex-1'>
                        <span className='font-medium text-base'>
                          {product.name}
                        </span>
                        <span className='text-sm text-muted-foreground'>
                          SKU: {product.sku}
                        </span>
                      </div>
                    </div>
                    <div className='flex items-center gap-4 ml-4'>
                      <Badge
                        variant={
                          product.stock > 0 ? 'secondary' : 'destructive'
                        }
                        className='px-3 py-1'
                      >
                        Stock: {product.stock}
                      </Badge>
                      <span className='font-medium text-base min-w-[100px] text-right'>
                        {formatCurrency(product.price)}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

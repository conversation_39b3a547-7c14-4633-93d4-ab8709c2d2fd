export interface Laboratory {
  _id: string;
  name: string;
  description?: string | null;
  country?: string | null;
  website?: string | null;
  isActive: boolean;
  isProtected: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateLaboratoryDto {
  name: string;
  description?: string | null;
  country?: string | null;
  website?: string | null;
}

export interface UpdateLaboratoryDto {
  name: string;
  description?: string | null;
  country?: string | null;
  website?: string | null;
}

export interface LaboratoriesResponse {
  laboratories: Laboratory[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface LaboratoryFilters {
  search?: string;
  isActive?: boolean;
  page: number;
  limit?: number;
}

export interface LaboratoriesFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  onReset: () => void;
}

export interface LaboratoriesActionsProps {
  canCreate: boolean;
  onCreate: () => void;
}

export interface LaboratoriesHeaderProps {
  title: string;
  description?: string;
}

export interface LaboratoriesTableProps {
  laboratories: Laboratory[];
  isLoading: boolean;
  pagination?: {
    totalRecords: number;
    totalPages: number;
    currentPage: number;
    onPageChange: (page: number) => void;
  };
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canCreate: boolean;
  };
  onEdit: (laboratory: Laboratory) => void;
  onDelete: (laboratory: Laboratory) => void;
  onToggleStatus: (laboratory: Laboratory) => void;
}

export interface LaboratoriesTableColumnProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canCreate: boolean;
  };
  onEdit: (laboratory: Laboratory) => void;
  onDelete: (laboratory: Laboratory) => void;
  onToggleStatus: (laboratory: Laboratory) => void;
}
export interface LaboratoryCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: {
    name: string;
    description?: string;
    country?: string;
    website?: string;
  }) => Promise<void>;
  isLoading?: boolean;
}

export interface LaboratoryEditDialogProps {
  laboratory: Laboratory | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (
    laboratoryId: string,
    laboratoryData: {
      name: string;
      description?: string;
      country?: string;
      website?: string;
    }
  ) => Promise<void>;
  isLoading: boolean;
}

export interface DeleteLaboratoryDialogProps {
  laboratory: Laboratory | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
}

export interface LaboratoryStatusDialogProps {
  laboratory: Laboratory | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading?: boolean;
}

import { create } from 'zustand';

interface LaboratoriesState {
  filters: {
    search: string;
    page: number;
  };
  setFilters: (filters: Partial<LaboratoriesState['filters']>) => void;
}

export const useLaboratoriesStore = create<LaboratoriesState>((set) => ({
  filters: {
    search: '',
    page: 1,
  },
  setFilters: (newFilters) =>
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    })),
}));
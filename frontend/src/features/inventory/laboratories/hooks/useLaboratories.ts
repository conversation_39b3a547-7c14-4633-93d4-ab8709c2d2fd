import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import {
  CreateLaboratoryDto,
  UpdateLaboratoryDto,
  laboratoryService,
  useLaboratoriesStore,
} from '@/features/inventory/laboratories';

export function useLaboratories() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { filters, setFilters } = useLaboratoriesStore();

  // Query principal
  const { data, isLoading } = useQuery({
    queryKey: ['laboratories', filters],
    queryFn: async () => {
      try {
        return await laboratoryService.getLaboratories({
          search: filters.search,
          page: filters.page,
          limit: 10,
        });
      } catch (error: any) {
        toast({
          title: 'Error',
          description: 'Error al cargar laboratorios',
          variant: 'destructive',
        });
        throw error;
      }
    },
  });

  const laboratories = data?.laboratories || [];
  const totalPages = data?.totalPages || 1;
  const totalLaboratories = data?.totalRecords || 0;

  // Mutations
  const createMutation = useMutation({
    mutationFn: (data: CreateLaboratoryDto) =>
      laboratoryService.createLaboratory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laboratories'] });
      toast({
        title: 'Laboratorio creado',
        description: 'El laboratorio ha sido creado exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al crear el laboratorio';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: { id: string; laboratory: UpdateLaboratoryDto }) =>
      laboratoryService.updateLaboratory(data.id, data.laboratory),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laboratories'] });
      toast({
        title: 'Laboratorio actualizado',
        description: 'El laboratorio ha sido actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al actualizar el laboratorio';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (laboratoryId: string) =>
      laboratoryService.deleteLaboratory(laboratoryId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laboratories'] });
      toast({
        title: 'Laboratorio eliminado',
        description: 'El laboratorio ha sido eliminado exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al eliminar el laboratorio';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const toggleStatusMutation = useMutation({
    mutationFn: (data: { laboratoryId: string; isActive: boolean }) =>
      laboratoryService.toggleLaboratoryStatus({
        laboratoryId: data.laboratoryId,
        isActive: data.isActive,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laboratories'] });
      toast({
        title: 'Estado actualizado',
        description:
          'El estado del laboratorio ha sido actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al actualizar el estado';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  return {
    laboratories,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    toggleStatusMutation,
    filters,
    setFilters,
    pagination: {
      totalPages,
      totalRecords: totalLaboratories,
    },
  };
}

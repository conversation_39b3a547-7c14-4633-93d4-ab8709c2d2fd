import { useQuery } from '@tanstack/react-query';
import { laboratoryService } from '../services/laboratory.service';

export function useLaboratorySelector() {
  const { data, isLoading } = useQuery({
    queryKey: ['laboratories', 'selector'],
    queryFn: async () => {
      const response = await laboratoryService.getAllLaboratories({
        isActive: true,
      });
      return response.laboratories;
    },
  });

  return {
    laboratories: data || [],
    isLoading,
  };
}
import { useAuth, PERMISSIONS } from '@/features/auth';

export function useLaboratoryPermissions() {
  const { checkPermission } = useAuth();

  return {
    checkPermission,
    permissions: {
      canList: checkPermission(PERMISSIONS.LABORATORIES.LIST),
      canCreate: checkPermission(PERMISSIONS.LABORATORIES.CREATE),
      canEdit: checkPermission(PERMISSIONS.LABORATORIES.EDIT),
      canDelete: checkPermission(PERMISSIONS.LABORATORIES.DELETE),
    },
  };
}
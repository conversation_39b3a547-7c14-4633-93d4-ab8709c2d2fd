import { useState } from 'react';
import { PERMISSIONS } from '@/features/auth';
import { useQueryClient } from '@tanstack/react-query';
import {
  useLaboratories,
  type Laboratory,
} from '@/features/inventory/laboratories';

export function useLaboratoriesPage() {
  const {
    laboratories,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    toggleStatusMutation,
    filters,
    setFilters,
    pagination,
  } = useLaboratories();
  const queryClient = useQueryClient();

  // States
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedLaboratory, setSelectedLaboratory] =
    useState<Laboratory | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editDialogLaboratory, setEditDialogLaboratory] =
    useState<Laboratory | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [selectedLaboratoryForStatus, setSelectedLaboratoryForStatus] =
    useState<Laboratory | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  // Handlers
  const handleCreate = () => setIsDialogOpen(true);

  const handleDelete = (laboratory: Laboratory) => {
    setSelectedLaboratory(laboratory);
    setIsDeleteDialogOpen(true);
  };

  const handleEdit = (laboratory: Laboratory) => {
    setEditDialogLaboratory(laboratory);
    setIsEditDialogOpen(true);
  };

  const handleToggleStatus = (laboratory: Laboratory) => {
    setSelectedLaboratoryForStatus(laboratory);
    setIsStatusDialogOpen(true);
  };

  // Submit handlers
  const handleSubmitCreate = async (data: {
    name: string;
    description?: string;
    country?: string;
    website?: string;
  }) => {
    await createMutation.mutateAsync(data);
  };

  const handleConfirmDelete = async () => {
    if (!selectedLaboratory) return;
    await deleteMutation.mutateAsync(selectedLaboratory._id);
  };

  const handleConfirmToggleStatus = async () => {
    if (!selectedLaboratoryForStatus) return;
    await toggleStatusMutation.mutateAsync({
      laboratoryId: selectedLaboratoryForStatus._id,
      isActive: !selectedLaboratoryForStatus.isActive,
    });
  };

  return {
    laboratories,
    isLoading,

    filters: {
      search: filters.search,
      setSearch: (search: string) =>
        setFilters({ ...filters, search, page: 1 }),
      reset: () => {
        setFilters({
          search: '',
          page: 1,
        });
        queryClient.invalidateQueries({ queryKey: ['laboratories'] });
      },
    },

    pagination: {
      currentPage: filters.page,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      onPageChange: (page: number) => {
        setFilters({ ...filters, page });
      },
    },

    actions: {
      handlers: {
        onCreate: handleCreate,
        onDelete: handleDelete,
        onEdit: handleEdit,
        onToggleStatus: handleToggleStatus,
      },
    },

    dialog: {
      isOpen: isDialogOpen,
      onOpenChange: setIsDialogOpen,
      onSubmit: handleSubmitCreate,
      isLoading: createMutation.isPending,
    },

    deleteDialog: {
      laboratory: selectedLaboratory,
      isOpen: isDeleteDialogOpen,
      onOpenChange: setIsDeleteDialogOpen,
      onConfirm: handleConfirmDelete,
      isLoading: deleteMutation.isPending,
    },

    editDialog: {
      laboratory: editDialogLaboratory,
      isOpen: isEditDialogOpen,
      onOpenChange: setIsEditDialogOpen,
      onUpdate: async (
        laboratoryId: string,
        laboratoryData: {
          name: string;
          description?: string;
          country?: string;
          website?: string;
        }
      ) => {
        setIsEditLoading(true);
        try {
          await updateMutation.mutateAsync({
            id: laboratoryId,
            laboratory: laboratoryData,
          });
        } finally {
          setIsEditLoading(false);
        }
      },
      isLoading: isEditLoading,
    },

    statusDialog: {
      laboratory: selectedLaboratoryForStatus,
      isOpen: isStatusDialogOpen,
      onOpenChange: setIsStatusDialogOpen,
      onConfirm: handleConfirmToggleStatus,
      isLoading: toggleStatusMutation.isPending,
    },
  };
}

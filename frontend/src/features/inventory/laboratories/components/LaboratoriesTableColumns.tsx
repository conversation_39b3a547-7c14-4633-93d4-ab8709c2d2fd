import { Edit, Trash, Power } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  LaboratoriesTableColumnProps,
  Laboratory,
} from '@/features/inventory/laboratories';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export const getLaboratoryTableColumns = ({
  permissions,
  onEdit,
  onDelete,
  onToggleStatus,
}: LaboratoriesTableColumnProps) => [
  {
    id: 'name',
    header: 'Nombre',
    cell: (laboratory: Laboratory) => laboratory.name,
  },
  {
    id: 'country',
    header: 'País',
    cell: (laboratory: Laboratory) => laboratory.country || '-',
  },
  {
    id: 'website',
    header: 'Sitio Web',
    cell: (laboratory: Laboratory) =>
      laboratory.website ? (
        <a
          href={laboratory.website}
          target='_blank'
          rel='noopener noreferrer'
          className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
        >
          {laboratory.website}
        </a>
      ) : (
        '-'
      ),
  },
  {
    id: 'status',
    header: 'Estado',
    cell: (laboratory: Laboratory) => (
      <Badge variant={laboratory.isActive ? 'success' : 'destructive'}>
        {laboratory.isActive ? 'Activo' : 'Inactivo'}
      </Badge>
    ),
  },
  {
    id: 'createdAt',
    header: 'Creación',
    cell: (laboratory: Laboratory) =>
      new Date(laboratory.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: (laboratory: Laboratory) => (
      <div className='flex items-center gap-2'>
        {permissions.canEdit && (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onEdit(laboratory)}
                  >
                    <Edit className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Editar</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onToggleStatus(laboratory)}
                    className={
                      laboratory.isActive
                        ? 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-500/10'
                        : 'text-green-600 hover:text-green-700 hover:bg-green-500/10'
                    }
                  >
                    <Power className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{laboratory.isActive ? 'Desactivar' : 'Activar'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        )}

        {permissions.canDelete && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onDelete(laboratory)}
                  className='text-red-600 hover:text-red-700 dark:text-rose-500 dark:hover:text-rose-400 hover:bg-red-500/10 dark:hover:bg-rose-500/20'
                >
                  <Trash className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Eliminar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    ),
  },
];

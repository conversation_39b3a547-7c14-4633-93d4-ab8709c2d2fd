import { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLaboratorySelector } from '../hooks/useLaboratorySelector';

interface LaboratorySelectProps {
  value?: string | null;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function LaboratorySelect({
  value,
  onChange,
  disabled = false,
  placeholder = 'Seleccionar laboratorio...',
}: LaboratorySelectProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const { laboratories, isLoading } = useLaboratorySelector();

  const selectedLaboratory = laboratories.find((lab) => lab._id === value);

  const filteredLaboratories = laboratories.filter((lab) =>
    lab.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className='w-full justify-between'
          disabled={disabled}
        >
          {selectedLaboratory ? (
            <span className='truncate'>{selectedLaboratory.name}</span>
          ) : (
            <span className='text-muted-foreground'>{placeholder}</span>
          )}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[var(--radix-popover-trigger-width)] p-0'>
        <Command shouldFilter={false}>
          <CommandInput
            placeholder='Buscar laboratorio...'
            value={search}
            onValueChange={setSearch}
          />
          <CommandList>
            <CommandEmpty>No se encontraron laboratorios.</CommandEmpty>
            <CommandGroup>
              <ScrollArea className='h-[200px]'>
                <CommandItem
                  value=''
                  onSelect={() => {
                    onChange(null);
                    setOpen(false);
                    setSearch('');
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      !value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  <span className='text-muted-foreground'>Todos</span>
                </CommandItem>
                {filteredLaboratories.map((laboratory) => (
                  <CommandItem
                    key={laboratory._id}
                    value={laboratory._id}
                    onSelect={() => {
                      onChange(laboratory._id);
                      setOpen(false);
                      setSearch('');
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === laboratory._id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    {laboratory.name}
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

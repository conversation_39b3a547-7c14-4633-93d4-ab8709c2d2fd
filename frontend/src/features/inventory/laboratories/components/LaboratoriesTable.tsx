import { ChevronLeft, ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { getLaboratoryTableColumns } from '@/features/inventory/laboratories';
import type { LaboratoriesTableProps } from '@/features/inventory/laboratories';

const tableVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      when: 'beforeChildren',
      staggerChildren: 0.1,
    },
  },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.3 },
  },
};

export function LaboratoriesTable({
  laboratories,
  isLoading,
  pagination,
  permissions,
  onEdit,
  onDelete,
  onToggleStatus,
}: LaboratoriesTableProps) {
  const columns = getLaboratoryTableColumns({
    permissions,
    onEdit,
    onDelete,
    onToggleStatus,
  });

  if (isLoading) {
    return (
      <div className='flex justify-center items-center py-8'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return (
    <motion.div initial='hidden' animate='visible' variants={tableVariants}>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id}>{column.header}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {laboratories.map((laboratory, index) => (
              <motion.tr
                key={laboratory._id || index}
                variants={rowVariants}
                className='border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted'
              >
                {columns.map((column) => (
                  <TableCell key={`${laboratory._id}-${column.id}`}>
                    {column.cell(laboratory)}
                  </TableCell>
                ))}
              </motion.tr>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className='flex items-center justify-between p-4'
        >
          <div className='text-sm text-muted-foreground'>
            Total: {pagination.totalRecords} registros
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage - 1)
              }
              disabled={pagination.currentPage <= 1}
            >
              <ChevronLeft className='h-4 w-4' />
            </Button>
            <span className='text-sm'>
              Página {pagination.currentPage} de {pagination.totalPages}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage + 1)
              }
              disabled={pagination.currentPage >= pagination.totalPages}
            >
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

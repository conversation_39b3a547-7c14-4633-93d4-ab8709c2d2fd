import { cn } from '@/lib/utils/styles';
import { LaboratoriesHeaderProps } from '@/features/inventory/laboratories';

export function LaboratoriesHeader({
  title = 'Laboratorios',
  description = 'Gestiona los laboratorios del sistema',
  className,
}: LaboratoriesHeaderProps & { showBackButton?: boolean; className?: string }) {
  return (
    <div className={cn('space-y-2 pb-4', className)}>
      <div className='flex items-center gap-4'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>{title}</h1>
          {description && (
            <p className='text-sm text-muted-foreground mt-1'>{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}

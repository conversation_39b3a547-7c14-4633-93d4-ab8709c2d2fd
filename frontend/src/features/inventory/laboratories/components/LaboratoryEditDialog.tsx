import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Building2, TextIcon, FileText, Globe2, MapPin } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils/styles';
import {
  editLaboratorySchema,
  type EditLaboratorySchema,
  type LaboratoryEditDialogProps,
} from '@/features/inventory/laboratories';

export function LaboratoryEditDialog({
  laboratory,
  isOpen,
  onOpenChange,
  onUpdate,
  isLoading,
}: LaboratoryEditDialogProps) {
  const form = useForm<EditLaboratorySchema>({
    resolver: zodResolver(editLaboratorySchema),
    defaultValues: {
      name: '',
      description: '',
      country: '',
      website: '',
    },
  });

  // Actualizar el formulario cuando el laboratorio cambia
  useEffect(() => {
    if (laboratory && isOpen) {
      form.reset({
        name: laboratory.name,
        description: laboratory.description || '',
        country: laboratory.country || '',
        website: laboratory.website || '',
      });
    }
  }, [laboratory, isOpen, form]);

  // Limpiar el formulario cuando se cierra el diálogo
  useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [isOpen, form]);

  const handleSubmit = async (data: EditLaboratorySchema) => {
    if (!laboratory) return;

    try {
      await onUpdate(laboratory._id, {
        name: data.name,
        description: data.description || null,
        country: data.country || null,
        website: data.website || null,
      });
      onOpenChange(false);
    } catch (error) {
      // El error es manejado por la mutación
    }
  };

  // No renderizar el contenido si no hay laboratorio
  if (!laboratory) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Building2 className='w-5 h-5 text-primary' />
            Editar laboratorio
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 sm:space-y-6 py-2'
          >
            <div className='grid gap-6'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.name && 'text-destructive'
                      )}
                    >
                      <TextIcon className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Nombre
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Nombre del laboratorio'
                        disabled={isLoading}
                        className={cn(
                          form.formState.errors.name && 'border-destructive'
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <FileText className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Descripción
                        <span className='text-muted-foreground'>
                          (opcional)
                        </span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Descripción del laboratorio'
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='country'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <MapPin className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        País
                        <span className='text-muted-foreground'>
                          (opcional)
                        </span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='País de origen'
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='website'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <Globe2 className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Sitio web
                        <span className='text-muted-foreground'>
                          (opcional)
                        </span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='url'
                        placeholder='https://ejemplo.com'
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex justify-end gap-4 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Guardando...' : 'Guardar cambios'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

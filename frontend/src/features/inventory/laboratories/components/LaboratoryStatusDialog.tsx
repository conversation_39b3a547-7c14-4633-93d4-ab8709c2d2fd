import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils/styles';
import { LaboratoryStatusDialogProps } from '@/features/inventory/laboratories';

export function LaboratoryStatusDialog({
  laboratory,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: LaboratoryStatusDialogProps) {
  if (!laboratory) return null;

  const actionText = laboratory.isActive ? 'desactivar' : 'activar';
  const actionTextCapitalized = laboratory.isActive
    ? 'Desactivar laboratorio'
    : 'Activar laboratorio';
  const actionLoadingText = laboratory.isActive
    ? 'Desactivando...'
    : 'Activando...';

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {}
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='w-[95vw] max-w-[500px]'>
        <AlertDialogHeader>
          <AlertDialogTitle>¿{actionTextCapitalized}?</AlertDialogTitle>
          <AlertDialogDescription>
            ¿Estás seguro que deseas {actionText} el laboratorio "
            {laboratory.name}"?{' '}
            {laboratory.isActive
              ? 'El laboratorio no estará disponible hasta que sea reactivado.'
              : 'El laboratorio volverá a estar disponible en el sistema.'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <Button
            variant={laboratory.isActive ? 'destructive' : 'default'}
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              laboratory.isActive &&
                'dark:bg-rose-600 dark:hover:bg-rose-700 dark:text-white',
              isLoading && 'dark:opacity-80'
            )}
          >
            {isLoading ? actionLoadingText : actionTextCapitalized}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

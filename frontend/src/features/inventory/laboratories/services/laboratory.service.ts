import { api } from '@/lib/api/api';
import type {
  Laboratory,
  LaboratoriesResponse,
  LaboratoryFilters,
  CreateLaboratoryDto,
  UpdateLaboratoryDto,
} from '@/features/inventory/laboratories';

export const laboratoryService = {
  getLaboratories: async (
    filters?: LaboratoryFilters
  ): Promise<LaboratoriesResponse> => {
    const { data } = await api.get('/laboratories', { params: filters });
    return data;
  },

  getActiveLaboratories: async (): Promise<{ laboratories: Laboratory[] }> => {
    const { data } = await api.get('/laboratories/active');
    return data;
  },

  createLaboratory: async (
    laboratory: CreateLaboratoryDto
  ): Promise<Laboratory> => {
    const { data } = await api.post('/laboratories', laboratory);
    return data;
  },

  updateLaboratory: async (
    id: string,
    laboratory: UpdateLaboratoryDto
  ): Promise<Laboratory> => {
    const { data } = await api.patch(`/laboratories/${id}`, laboratory);
    return data;
  },

  deleteLaboratory: async (id: string): Promise<void> => {
    await api.delete(`/laboratories/${id}`);
  },

  toggleLaboratoryStatus: async (data: {
    laboratoryId: string;
    isActive: boolean;
  }): Promise<Laboratory> => {
    const { data: response } = await api.patch(
      `/laboratories/${data.laboratoryId}/toggle-status`,
      { isActive: data.isActive }
    );
    return response;
  },

  getAllLaboratories: async (params?: { isActive?: boolean }) => {
    const { data } = await api.get('/laboratories/all', { params });
    return data;
  },
};

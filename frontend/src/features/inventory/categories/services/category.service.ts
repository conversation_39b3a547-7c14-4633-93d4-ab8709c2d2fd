import { api } from '@/lib/api/api';
import type {
  Category,
  CategoriesResponse,
  CreateCategoryDto,
  UpdateCategoryDto,
} from '@/features/inventory/categories';

export const categoryService = {
  getCategories: async (params?: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<CategoriesResponse> => {
    const response = await api.get('/categories', { params });
    return response.data;
  },

  getActiveCategories: async (): Promise<Category[]> => {
    const response = await api.get('/categories/active');
    return response.data.categories;
  },

  createCategory: async (
    categoryData: CreateCategoryDto
  ): Promise<Category> => {
    const response = await api.post('/categories', categoryData);
    return response.data;
  },

  updateCategory: async (
    id: string,
    categoryData: UpdateCategoryDto
  ): Promise<Category> => {
    const response = await api.patch(`/categories/${id}`, categoryData);
    return response.data;
  },

  deleteCategory: async (id: string): Promise<void> => {
    const response = await api.delete(`/categories/${id}`);
    return response.data;
  },

  toggleCategoryStatus: async (
    id: string,
    isActive: boolean
  ): Promise<Category> => {
    const response = await api.patch(`/categories/${id}/toggle-status`, {
      isActive,
    });
    return response.data;
  },

  getAllCategories: async (params?: { isActive?: boolean }) => {
    const { data } = await api.get('/categories/all', { params });
    return data;
  },
};

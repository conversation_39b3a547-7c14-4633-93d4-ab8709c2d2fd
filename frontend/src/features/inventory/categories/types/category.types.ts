export interface Category {
  _id: string;
  name: string;
  description?: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryDto {
  name: string;
  description?: string | null;
}

export interface UpdateCategoryDto {
  name: string;
  description?: string | null;
}

export interface CategoriesResponse {
  categories: Category[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface CategoryFilters {
  search?: string;
  isActive?: boolean;
}

export interface CategoriesFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  onReset: () => void;
}

export interface CategoriesActionsProps {
  canCreate: boolean;
  onCreate: () => void;
}

export interface CategoriesHeaderProps {
  title: string;
  description?: string;
}

export interface CategoriesTableProps {
  categories: Category[];
  isLoading: boolean;
  pagination?: {
    totalRecords: number;
    totalPages: number;
    currentPage: number;
    onPageChange: (page: number) => void;
  };
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
  onToggleStatus: (category: Category) => void;
}

export interface CategoriesTableColumnProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
  onToggleStatus: (category: Category) => void;
}

export interface CategoryStatusDialogProps {
  category: Category | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

export interface CategoryEditDialogProps {
  category: Category | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (categoryId: string, data: UpdateCategoryDto) => Promise<void>;
  isLoading?: boolean;
}

export interface CategoryCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateCategoryDto) => Promise<void>;
  isLoading: boolean;
}

export interface DeleteCategoryDialogProps {
  category: Category | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

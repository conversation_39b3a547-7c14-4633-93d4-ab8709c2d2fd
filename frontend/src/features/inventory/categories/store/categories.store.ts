import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Category } from '@/features/inventory/categories';

interface CategoriesState {
  // Estado
  categories: Category[];
  totalRecords: number;
  selectedCategory: Category | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    search: string;
    page: number;
    limit: number;
  };

  // Acciones
  setCategories: (categories: Category[], total: number) => void;
  setSelectedCategory: (category: Category | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: Partial<CategoriesState['filters']>) => void;
  resetFilters: () => void;
}

const initialFilters = {
  search: '',
  page: 1,
  limit: 10,
};

export const useCategoriesStore = create<CategoriesState>()(
  devtools((set) => ({
    // Estado inicial
    categories: [],
    totalRecords: 0,
    selectedCategory: null,
    isLoading: false,
    error: null,
    filters: initialFilters,

    // Acciones
    setCategories: (categories, total) =>
      set({ categories, totalRecords: total }),

    setSelectedCategory: (category) => set({ selectedCategory: category }),

    setIsLoading: (isLoading) => set({ isLoading }),

    setError: (error) => set({ error }),

    setFilters: (newFilters) =>
      set((state) => ({
        filters: {
          ...state.filters,
          ...newFilters,
        },
      })),

    resetFilters: () => set({ filters: initialFilters }),
  }))
);

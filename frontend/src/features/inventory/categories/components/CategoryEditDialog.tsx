import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FolderEdit, TextIcon, FileText } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  editCategorySchema,
  type EditCategorySchema,
  CategoryEditDialogProps,
} from '@/features/inventory/categories';

export function CategoryEditDialog({
  category,
  isOpen,
  onOpenChange,
  onUpdate,
  isLoading = false,
}: CategoryEditDialogProps) {
  const form = useForm<EditCategorySchema>({
    resolver: zodResolver(editCategorySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  useEffect(() => {
    if (category) {
      form.reset({
        name: category.name,
        description: category.description ?? '',
      });
    }
  }, [category, form]);

  useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [isOpen, form]);

  const handleSubmit = async (data: EditCategorySchema) => {
    if (!category) return;

    try {
      await onUpdate(category._id, {
        name: data.name,
        description: data.description || null,
      });
      onOpenChange(false);
    } catch (error) {}
  };

  if (!category) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg font-semibold'>
            <FolderEdit className='w-5 h-5 text-primary' />
            Editar Categoría: {category.name}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 sm:space-y-6 py-2'
          >
            <div className='grid gap-6'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      className={cn(
                        'flex items-center gap-2',
                        form.formState.errors.name && 'text-destructive'
                      )}
                    >
                      <TextIcon className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Nombre
                        <span className='text-destructive'>*</span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Nombre de la categoría'
                        disabled={isLoading}
                        className={cn(
                          form.formState.errors.name && 'border-destructive'
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <FileText className='w-4 h-4 text-muted-foreground' />
                      <div className='flex items-center gap-1'>
                        Descripción
                        <span className='text-muted-foreground'>
                          (opcional)
                        </span>
                      </div>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Descripción de la categoría'
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex justify-end gap-4 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Guardando...' : 'Guardar cambios'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils/styles';
import { CategoriesHeaderProps } from '@/features/inventory/categories';

export function CategoriesHeader({
  title = 'Categorías',
  description = 'Gestiona las categorías de productos del sistema',
  className,
}: CategoriesHeaderProps & { showBackButton?: boolean; className?: string }) {
  const navigate = useNavigate();

  return (
    <div className={cn('space-y-2 pb-4', className)}>
      <div className='flex items-center gap-4'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>{title}</h1>
          {description && (
            <p className='text-sm text-muted-foreground mt-1'>{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}

import { Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import { CategoriesActionsProps } from '@/features/inventory/categories';

export function CategoriesActions({
  canCreate,
  onCreate,
}: CategoriesActionsProps) {
  return (
    <TooltipProvider>
      <div className='flex items-center sm:justify-end justify-center w-full'>
        {canCreate && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={onCreate}
                  size='icon'
                  className='sm:hidden h-10 w-10'
                >
                  <Plus className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Nueva Categoría</TooltipContent>
            </Tooltip>

            <Button
              onClick={onCreate}
              className='hidden sm:flex items-center gap-2'
            >
              <Plus className='h-4 w-4' />
              <span>Nueva Categoría</span>
            </Button>
          </>
        )}
      </div>
    </TooltipProvider>
  );
}

import { Edit, Trash, Power } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  CategoriesTableColumnProps,
  Category,
} from '@/features/inventory/categories';

export const getCategoryTableColumns = ({
  permissions,
  onEdit,
  onDelete,
  onToggleStatus,
}: CategoriesTableColumnProps) => [
  {
    id: 'name',
    header: 'Nombre',
    cell: (category: Category) => category.name,
  },
  {
    id: 'status',
    header: 'Estado',
    cell: (category: Category) => (
      <Badge variant={category.isActive ? 'success' : 'destructive'}>
        {category.isActive ? 'Activo' : 'Inactivo'}
      </Badge>
    ),
  },
  {
    id: 'createdAt',
    header: 'Creación',
    cell: (category: Category) =>
      new Date(category.createdAt).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: (category: Category) => (
      <div className='flex items-center gap-2'>
        {permissions.canEdit && (
          <>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onEdit(category)}
                  >
                    <Edit className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Editar</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    onClick={() => onToggleStatus(category)}
                    className={
                      category.isActive
                        ? 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-500/10'
                        : 'text-green-600 hover:text-green-700 hover:bg-green-500/10'
                    }
                  >
                    <Power className='h-4 w-4' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{category.isActive ? 'Desactivar' : 'Activar'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        )}

        {permissions.canDelete && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onDelete(category)}
                  className='text-red-600 hover:text-red-700 dark:text-rose-500 dark:hover:text-rose-400 hover:bg-red-500/10 dark:hover:bg-rose-500/20'
                >
                  <Trash className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Eliminar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    ),
  },
];

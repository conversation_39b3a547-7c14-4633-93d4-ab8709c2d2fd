import { useForm } from 'react-hook-form';
import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { FolderPlus, TextIcon, FileText } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  createCategorySchema,
  type CreateCategorySchema,
  CategoryCreateDialogProps,
} from '@/features/inventory/categories';

export function CategoryCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
}: CategoryCreateDialogProps) {
  const form = useForm<CreateCategorySchema>({
    resolver: zodResolver(createCategorySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const handleSubmit = async (data: CreateCategorySchema) => {
    try {
      await onSubmit({
        name: data.name,
        description: data.description || null,
      });
      onOpenChange(false);
    } catch (error: any) {
      // Manejar errores de validación del servidor
      if (error.response?.data?.details) {
        error.response.data.details.forEach((detail: any) => {
          const fieldName = detail.field.replace('body.', '');
          form.setError(fieldName as any, {
            type: 'server',
            message: detail.message,
          });
        });
      } else if (error.response?.data?.error) {
        // Si hay un error general, mostrarlo en el campo name
        form.setError('name', {
          type: 'server',
          message: error.response.data.error,
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px] max-h-[90vh] flex flex-col'>
        <DialogHeader className='pb-4 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <FolderPlus className='w-5 h-5 text-primary' />
            Crear nueva categoría
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='flex flex-col flex-1 overflow-hidden'
          >
            <div className='space-y-4 sm:space-y-6 py-2 flex-1 overflow-y-auto'>
              <div className='grid gap-6'>
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel
                        className={cn(
                          'flex items-center gap-2',
                          form.formState.errors.name && 'text-destructive'
                        )}
                      >
                        <TextIcon className='w-4 h-4 text-muted-foreground' />
                        <div className='flex items-center gap-1'>
                          Nombre
                          <span className='text-destructive'>*</span>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Nombre de la categoría'
                          disabled={isLoading}
                          className={cn(
                            form.formState.errors.name && 'border-destructive'
                          )}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='flex items-center gap-2'>
                        <FileText className='w-4 h-4 text-muted-foreground' />
                        <div className='flex items-center gap-1'>
                          Descripción
                          <span className='text-muted-foreground'>
                            (opcional)
                          </span>
                        </div>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Descripción de la categoría'
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='flex justify-end gap-4 pt-4 border-t mt-4 shrink-0'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Creando...' : 'Crear categoría'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

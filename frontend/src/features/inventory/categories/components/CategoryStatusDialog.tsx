import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils/styles';
import { CategoryStatusDialogProps } from '@/features/inventory/categories';

export function CategoryStatusDialog({
  category,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: CategoryStatusDialogProps) {
  if (!category) return null;

  const actionText = category.isActive ? 'desactivar' : 'activar';
  const actionTextCapitalized = category.isActive
    ? 'Desactivar categoría'
    : 'Activar categoría';
  const actionLoadingText = category.isActive
    ? 'Desactivando...'
    : 'Activando...';

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {}
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className='w-[95vw] max-w-[500px]'>
        <AlertDialogHeader>
          <AlertDialogTitle>¿{actionTextCapitalized}?</AlertDialogTitle>
          <AlertDialogDescription>
            ¿Estás seguro que deseas {actionText} la categoría "{category.name}
            "?{' '}
            {category.isActive
              ? 'La categoría no estará disponible hasta que sea reactivada.'
              : 'La categoría volverá a estar disponible en el sistema.'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <Button
            variant={category.isActive ? 'destructive' : 'default'}
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              category.isActive &&
                'dark:bg-rose-600 dark:hover:bg-rose-700 dark:text-white',
              isLoading && 'dark:opacity-80'
            )}
          >
            {isLoading ? actionLoadingText : actionTextCapitalized}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

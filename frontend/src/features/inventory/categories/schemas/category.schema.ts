import * as z from 'zod';
import { CATEGORY_VALIDATION } from '../constants/category-validation.constants';

export const createCategorySchema = z.object({
  name: z
    .string()
    .min(CATEGORY_VALIDATION.NAME.MIN_LENGTH, CATEGORY_VALIDATION.NAME.MESSAGE)
    .max(CATEGORY_VALIDATION.NAME.MAX_LENGTH, CATEGORY_VALIDATION.NAME.MESSAGE),
  description: z
    .string()
    .max(
      CATEGORY_VALIDATION.DESCRIPTION.MAX_LENGTH,
      CATEGORY_VALIDATION.DESCRIPTION.MESSAGE
    )
    .nullable()
    .optional(),
});

export const editCategorySchema = createCategorySchema;

export type CreateCategorySchema = z.infer<typeof createCategorySchema>;
export type EditCategorySchema = z.infer<typeof editCategorySchema>;

export const toggleStatusSchema = z.object({
  isActive: z.boolean(),
});

export type ToggleStatusSchema = z.infer<typeof toggleStatusSchema>;

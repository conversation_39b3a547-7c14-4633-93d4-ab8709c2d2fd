import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useCategories, type Category } from '@/features/inventory/categories';

export function useCategoriesPage() {
  const {
    categories,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    toggleStatusMutation,
    filters,
    setFilters,
    pagination,
  } = useCategories();
  const queryClient = useQueryClient();

  // States
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editDialogCategory, setEditDialogCategory] = useState<Category | null>(
    null
  );
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [selectedCategoryForStatus, setSelectedCategoryForStatus] =
    useState<Category | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  // Handlers
  const handleCreate = () => setIsDialogOpen(true);

  const handleDelete = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteDialogOpen(true);
  };

  const handleEdit = (category: Category) => {
    setEditDialogCategory(category);
    setIsEditDialogOpen(true);
  };

  const handleToggleStatus = (category: Category) => {
    setSelectedCategoryForStatus(category);
    setIsStatusDialogOpen(true);
  };

  // Submit handlers
  const handleSubmitCreate = async (data: {
    name: string;
    description?: string;
  }) => {
    await createMutation.mutateAsync(data);
  };

  const handleConfirmDelete = async () => {
    if (!selectedCategory) return;
    await deleteMutation.mutateAsync(selectedCategory._id);
  };

  const editDialog = {
    category: editDialogCategory,
    isOpen: isEditDialogOpen,
    onOpenChange: setIsEditDialogOpen,
    onUpdate: async (
      categoryId: string,
      categoryData: { name: string; description?: string }
    ) => {
      setIsEditLoading(true);
      try {
        await updateMutation.mutateAsync({
          id: categoryId,
          category: categoryData,
        });
      } finally {
        setIsEditLoading(false);
      }
    },
    isLoading: isEditLoading,
  };

  const handleConfirmToggleStatus = async () => {
    if (!selectedCategoryForStatus) return;
    await toggleStatusMutation.mutateAsync({
      categoryId: selectedCategoryForStatus._id,
      isActive: !selectedCategoryForStatus.isActive,
    });
  };

  return {
    categories,
    isLoading,

    filters: {
      search: filters.search,
      setSearch: (search: string) =>
        setFilters({ ...filters, search, page: 1 }),
      reset: () => {
        setFilters({
          search: '',
          page: 1,
        });
        // Invalidate the query to force a reload
        queryClient.invalidateQueries({ queryKey: ['categories'] });
      },
    },

    pagination: {
      currentPage: filters.page,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      onPageChange: (page: number) => {
        setFilters({ ...filters, page });
      },
    },

    actions: {
      handlers: {
        onCreate: handleCreate,
        onDelete: handleDelete,
        onEdit: handleEdit,
        onToggleStatus: handleToggleStatus,
      },
    },

    dialog: {
      isOpen: isDialogOpen,
      onOpenChange: setIsDialogOpen,
      onSubmit: handleSubmitCreate,
      isLoading: createMutation.isPending,
    },

    deleteDialog: {
      category: selectedCategory,
      isOpen: isDeleteDialogOpen,
      onOpenChange: setIsDeleteDialogOpen,
      onConfirm: handleConfirmDelete,
      isLoading: deleteMutation.isPending,
    },

    editDialog,

    statusDialog: {
      category: selectedCategoryForStatus,
      isOpen: isStatusDialogOpen,
      onOpenChange: setIsStatusDialogOpen,
      onConfirm: handleConfirmToggleStatus,
      isLoading: toggleStatusMutation.isPending,
    },
  };
}

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import {
  categoryService,
  useCategoriesStore,
  type CreateCategoryDto,
  type UpdateCategoryDto,
} from '@/features/inventory/categories';
import { usePermissions } from '@/features/users';

export function useCategories() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { checkPermission } = usePermissions();
  const { filters, setFilters } = useCategoriesStore();

  // Query principal
  const { data, isLoading } = useQuery({
    queryKey: ['categories', filters],
    queryFn: async () => {
      try {
        return await categoryService.getCategories({
          search: filters.search,
          page: filters.page,
          limit: 10,
        });
      } catch (error: any) {
        toast({
          title: 'Error',
          description: 'Error al cargar categorías',
          variant: 'destructive',
        });
        throw error;
      }
    },
  });

  const categories = data?.categories || [];
  const totalPages = data?.totalPages || 1;
  const totalCategories = data?.totalRecords || 0;

  // Mutations
  const createMutation = useMutation({
    mutationFn: (data: CreateCategoryDto) =>
      categoryService.createCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast({
        title: 'Categoría creada',
        description: 'La categoría ha sido creada exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al crear la categoría';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: { id: string; category: UpdateCategoryDto }) =>
      categoryService.updateCategory(data.id, data.category),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast({
        title: 'Categoría actualizada',
        description: 'La categoría ha sido actualizada exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al actualizar la categoría';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (categoryId: string) =>
      categoryService.deleteCategory(categoryId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast({
        title: 'Categoría eliminada',
        description: 'La categoría ha sido eliminada exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al eliminar la categoría';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const toggleStatusMutation = useMutation({
    mutationFn: (data: { categoryId: string; isActive: boolean }) =>
      categoryService.toggleCategoryStatus(data.categoryId, data.isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast({
        title: 'Estado actualizado',
        description:
          'El estado de la categoría ha sido actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.error || 'Error al actualizar el estado';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  return {
    categories,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    toggleStatusMutation,
    checkPermission,
    filters,
    setFilters,
    pagination: {
      totalPages,
      totalRecords: totalCategories,
    },
  };
}

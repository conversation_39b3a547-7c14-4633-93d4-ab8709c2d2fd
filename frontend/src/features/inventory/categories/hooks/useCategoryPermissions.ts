import { useAuth, PERMISSIONS } from '@/features/auth';

export function useCategoryPermissions() {
  const { checkPermission } = useAuth();

  return {
    checkPermission,
    permissions: {
      canList: checkPermission(PERMISSIONS.CATEGORIES.LIST),
      canCreate: checkPermission(PERMISSIONS.CATEGORIES.CREATE),
      canEdit: checkPermission(PERMISSIONS.CATEGORIES.EDIT),
      canDelete: checkPermission(PERMISSIONS.CATEGORIES.DELETE),
    },
  };
}

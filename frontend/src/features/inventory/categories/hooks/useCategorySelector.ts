import { useQuery } from '@tanstack/react-query';
import { categoryService } from '../services/category.service';

export function useCategorySelector() {
  const { data, isLoading } = useQuery({
    queryKey: ['categories', 'selector'],
    queryFn: async () => {
      const response = await categoryService.getAllCategories({
        isActive: true,
      });
      return response.categories;
    },
  });

  return {
    categories: data || [],
    isLoading,
  };
}
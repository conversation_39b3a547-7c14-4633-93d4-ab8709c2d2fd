import type { Category } from '@/features/inventory/categories';
import type { Laboratory } from '@/features/inventory/laboratories';

// Domain Types
export interface Product {
  _id: string;
  sku: string;
  name: string;
  description: string;
  image?: string;
  laboratory: Laboratory;
  category: Category;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  location: string;
  expirationDate: string;
  batchNumber: string;
  isActive: boolean;
  barcode?: string;
  requiresPrescription: boolean;
  presentation: string;
  concentration: string;
  measurementUnit:
    | 'TABLETA'
    | 'CAPSULA'
    | 'AMPOLLA'
    | 'FRASCO'
    | 'CREMA'
    | 'GOTAS';
  administrationRoute: 'ORAL' | 'INYECTABLE' | 'TOPICA' | 'OFTALMICA' | 'OTRO';
  storageCondition: 'TEMPERATURA_AMBIENTE' | 'REFRIGERACION' | 'CONGELACION';
  sanitaryRegistration: string;
  stockAlert: boolean;
  expirationAlert: boolean;
  createdBy: {
    _id: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

// DTO Types
export interface CreateProductDto {
  sku: string;
  name: string;
  description: string;
  laboratory: string;
  category: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  location: string;
  expirationDate: string;
  batchNumber: string;
  barcode?: string;
  requiresPrescription: boolean;
  image?: File | string;
  presentation: string;
  concentration: string;
  measurementUnit: string;
  administrationRoute: string;
  maxStock: number;
  storageCondition: string;
  sanitaryRegistration: string;
  stockAlert?: boolean;
  expirationAlert?: boolean;
}

export interface UpdateProductDto
  extends Partial<Omit<CreateProductDto, 'stock'>> {}

// API Types
export interface ProductFilters {
  search: string;
  category: string;
  laboratory?: string;
  showInactive?: boolean;
  storageCondition?: string;
  administrationRoute?: string;
  page: number;
  limit?: number;
}

export interface ProductsResponse {
  products: Product[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface ProductDashboardResponse {
  stats: {
    lowStock: number;
    expiringSoon: number;
    totalProducts: number;
    activeProducts: number;
  };
  recentMovements: Array<{
    _id: string;
    type: string;
    quantity: number;
    product: Pick<Product, '_id' | 'name' | 'sku'>;
    createdAt: string;
  }>;
  topProducts: Product[];
}

export interface ExpiringProductsResponse {
  products: Product[];
  pagination: {
    totalRecords: number;
    totalPages: number;
    currentPage: number;
  };
}

export interface LowStockProductsResponse {
  products: Product[];
  pagination: {
    totalRecords: number;
    totalPages: number;
    currentPage: number;
  };
}

// UI Component Props Types
export interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  limit: number;
}

export interface ProductsHeaderProps {
  title: string;
  description?: string;
  stats?: ProductDashboardResponse['stats'];
}

export interface ProductsFiltersProps {
  search: string;
  selectedCategory: string;
  selectedLaboratory: string;
  showInactive: boolean;
  onSearchChange: (search: string) => void;
  onCategoryChange: (category: string) => void;
  onLaboratoryChange: (laboratory: string) => void;
  onActiveStatusChange: (showInactive: boolean) => void;
  onReset: () => void;
}

export interface ProductsActionsProps {
  canCreate: boolean;
  onCreate: () => void;
  onExport?: () => void;
}

export interface ProductsTableProps {
  products: Product[];
  isLoading: boolean;
  pagination: PaginationState & {
    onPageChange: (page: number) => void;
    onLimitChange: (limit: number) => void;
  };
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
  onViewDetails: (product: Product) => void;
  onPrintTicket: (product: Product) => void;
}

export interface GetProductTableColumnsProps {
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
  onViewDetails: (product: Product) => void;
  onPrintTicket: (product: Product) => void;
}

export interface ProductTableColumn {
  id: string;
  header: string;
  cell: (product: Product) => React.ReactNode;
  sortable?: boolean;
  sortKey?: string;
}

export interface ProductTableActionProps {
  product: Product;
  onEdit: (product: Product) => void;
  onDelete: (product: Product) => void;
  onViewDetails: (product: Product) => void;
  onPrintTicket: (product: Product) => void;
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
  };
}

export interface ProductStatsCardProps {
  title: string;
  value: number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

// Dialog Props Types
export interface ProductDetailsDialogProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface ProductEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (
    productId: string,
    productData: FormData | UpdateProductDto
  ) => Promise<void>;
  product: Product;
  isLoading?: boolean;
  categories: Category[];
  laboratories: Laboratory[];
}

export interface DeleteProductDialogProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

export interface ProductCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: FormData) => Promise<void>;
  isLoading?: boolean;
  categories: Category[];
  laboratories: Laboratory[];
}

export interface StockUpdateDto {
  type: 'IN' | 'OUT';
  quantity: number;
  reason: string;
  batchNumber: string;
  expirationDate?: string;
  cost?: number;
  observations?: string;
}

export interface StockUpdateDialogProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (productId: string, data: StockUpdateDto) => Promise<void>;
  isLoading: boolean;
}

export interface ProductTicketProps {
  product: Product;
  codeType: 'QR' | 'BARCODE';
}

export interface ProductTicketDialogProps {
  product: Product | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  ticketType: 'QR' | 'BARCODE';
  onTicketTypeChange: (type: 'QR' | 'BARCODE') => void;
}

export interface ProductTicketDialogProps {
  product: Product | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  ticketType: 'QR' | 'BARCODE';
  onTicketTypeChange: (type: 'QR' | 'BARCODE') => void;
}

export interface TicketDataConfig {
  showName: boolean;
  showSku: boolean;
  showExpiration: boolean;
  showPrice: boolean;
  orientation: 'vertical' | 'horizontal';
  showBorders: boolean;
}

export interface TicketPreferences extends TicketDataConfig {
  ticketType: 'QR' | 'BARCODE';
}

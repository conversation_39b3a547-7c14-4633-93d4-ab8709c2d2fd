import { api } from '@/lib/api/api';
import type {
  Product,
  ProductsResponse,
  UpdateProductDto,
  ProductDashboardResponse,
} from '@/features/inventory/products';

export const productService = {
  getProducts: async (params: {
    search?: string;
    category?: string;
    laboratory?: string;
    page?: number;
    limit?: number;
  }): Promise<ProductsResponse> => {
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value !== undefined && value !== ''
      )
    );

    const { data } = await api.get('/products', { params: cleanParams });
    return data;
  },

  createProduct: async (
    productData: FormData
  ): Promise<{ product: Product; message: string }> => {
    const { data } = await api.post('/products', productData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return data;
  },

  updateProduct: async (
    id: string,
    productData: UpdateProductDto | FormData
  ): Promise<{ product: Product; message: string }> => {
    const isFormData = productData instanceof FormData;
    const { data } = await api.put(`/products/${id}`, productData, {
      headers: isFormData
        ? { 'Content-Type': 'multipart/form-data' }
        : undefined,
    });
    return data;
  },

  deleteProduct: async (
    id: string
  ): Promise<{ product: Product; message: string }> => {
    const { data } = await api.delete(`/products/${id}`);
    return data;
  },

  getCategories: async () => {
    const { data } = await api.get('/categories');
    return data.categories;
  },

  getDashboard: async (): Promise<ProductDashboardResponse> => {
    const { data } = await api.get('/products/dashboard');
    return data;
  },

  getProductById: async (id: string): Promise<Product> => {
    const { data } = await api.get(`/products/${id}`);
    return data;
  },

  getLowStockProducts: async (params: {
    category?: string;
    page?: number;
    limit?: number;
  }): Promise<ProductsResponse> => {
    const { data } = await api.get('/products/low-stock', { params });
    return data;
  },

  getExpiringProducts: async (params: {
    category?: string;
    days?: number;
    page?: number;
    limit?: number;
  }): Promise<ProductsResponse> => {
    const { data } = await api.get('/products/expiring', { params });
    return data;
  },

  getProductsForSale: async () => {
    const { data } = await api.get('/products/sale/available');
    return data;
  },
};

import { z } from 'zod';
import { PRODUCT_VALIDATION } from '@/features/inventory/products';
import { isValidDate, isFutureDate, toUTCISOString } from '@/lib/utils/format';

const numberFromString = z.union([
  z.string().transform((val, ctx) => {
    const parsed = Number(val);
    if (isNaN(parsed)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: PRODUCT_VALIDATION.NUMBER.INVALID,
      });
      return z.NEVER;
    }
    return parsed;
  }),
  z.number(),
]);

const booleanFromString = z.preprocess((val) => {
  if (typeof val === 'string') return val.toLowerCase() === 'true';
  if (typeof val === 'boolean') return val;
  return false;
}, z.boolean());

export const createProductSchema = z
  .object({
    sku: z
      .union([
        z
          .string()
          .min(
            PRODUCT_VALIDATION.SKU.MIN_LENGTH,
            PRODUCT_VALIDATION.SKU.MESSAGE
          )
          .max(50, PRODUCT_VALIDATION.SKU.MESSAGE),
        z.string().length(0),
      ])
      .optional(),
    name: z
      .string()
      .min(PRODUCT_VALIDATION.NAME.MIN_LENGTH, PRODUCT_VALIDATION.NAME.MESSAGE),
    description: z
      .string()
      .min(1, PRODUCT_VALIDATION.DESCRIPTION.REQUIRED)
      .max(
        PRODUCT_VALIDATION.DESCRIPTION.MAX_LENGTH,
        PRODUCT_VALIDATION.DESCRIPTION.MESSAGE
      ),
    laboratory: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LABORATORY),
    category: z.string().min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CATEGORY),
    price: numberFromString.pipe(
      z
        .number()
        .min(PRODUCT_VALIDATION.PRICE.MIN, PRODUCT_VALIDATION.PRICE.MESSAGE)
    ),
    cost: numberFromString.pipe(
      z
        .number()
        .min(
          PRODUCT_VALIDATION.PRICE.MIN,
          PRODUCT_VALIDATION.PRICE.COST_MESSAGE
        )
    ),
    minStock: numberFromString.pipe(
      z
        .number()
        .min(PRODUCT_VALIDATION.STOCK.MIN, PRODUCT_VALIDATION.STOCK.MIN_MESSAGE)
    ),
    location: z.string().min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LOCATION),
    expirationDate: z
      .string()
      .refine(isValidDate, { message: 'Fecha inválida' })
      .refine(isFutureDate, {
        message: PRODUCT_VALIDATION.DATES.EXPIRATION_MESSAGE,
      })
      .transform(toUTCISOString),
    batchNumber: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.BATCH_NUMBER),
    barcode: z.string().optional(),
    requiresPrescription: booleanFromString,
    image: z.union([z.instanceof(File), z.null(), z.undefined()]).optional(),
    presentation: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.PRESENTATION),
    concentration: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CONCENTRATION),
    measurementUnit: z.enum(PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.VALUES, {
      errorMap: () => ({
        message: PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.MESSAGE,
      }),
    }),
    administrationRoute: z.enum(
      PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.VALUES,
      {
        errorMap: () => ({
          message: PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.MESSAGE,
        }),
      }
    ),
    maxStock: numberFromString.pipe(
      z.number().min(0, PRODUCT_VALIDATION.STOCK.MAX_MESSAGE)
    ),
    storageCondition: z.enum(
      PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.VALUES,
      {
        errorMap: () => ({
          message: PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.MESSAGE,
        }),
      }
    ),
    sanitaryRegistration: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.SANITARY_REGISTRATION),
    stockAlert: booleanFromString.optional(),
    expirationAlert: booleanFromString.optional(),
    deleteImage: z.boolean().optional(),
  })
  .refine(
    (data) => {
      const minStock = Number(data.minStock);
      const maxStock = Number(data.maxStock);
      return maxStock >= minStock;
    },
    {
      message: PRODUCT_VALIDATION.STOCK.COMPARISON_MESSAGE,
      path: ['maxStock'],
    }
  );

export const updateProductSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z
    .object({
      sku: z
        .union([
          z
            .string()
            .min(
              PRODUCT_VALIDATION.SKU.MIN_LENGTH,
              PRODUCT_VALIDATION.SKU.MESSAGE
            )
            .max(50, PRODUCT_VALIDATION.SKU.MESSAGE),
          z.string().length(0), // Permite string vacío
        ])
        .optional(),
      name: z
        .string()
        .min(
          PRODUCT_VALIDATION.NAME.MIN_LENGTH,
          PRODUCT_VALIDATION.NAME.MESSAGE
        )
        .optional(),
      description: z
        .string()
        .min(1, PRODUCT_VALIDATION.DESCRIPTION.REQUIRED)
        .optional(),
      laboratory: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LABORATORY)
        .optional(),
      category: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CATEGORY)
        .optional(),
      price: numberFromString
        .pipe(
          z
            .number()
            .min(PRODUCT_VALIDATION.PRICE.MIN, PRODUCT_VALIDATION.PRICE.MESSAGE)
        )
        .optional(),
      cost: numberFromString
        .pipe(
          z
            .number()
            .min(
              PRODUCT_VALIDATION.PRICE.MIN,
              PRODUCT_VALIDATION.PRICE.COST_MESSAGE
            )
        )
        .optional(),
      minStock: numberFromString
        .pipe(
          z
            .number()
            .min(
              PRODUCT_VALIDATION.STOCK.MIN,
              PRODUCT_VALIDATION.STOCK.MIN_MESSAGE
            )
        )
        .optional(),
      location: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LOCATION)
        .optional(),
      barcode: z.string().optional(),
      requiresPrescription: booleanFromString.optional(),
      image: z
        .instanceof(File)
        .optional()
        .refine(
          (file) => !file || file.size <= PRODUCT_VALIDATION.IMAGE.MAX_SIZE,
          PRODUCT_VALIDATION.IMAGE.SIZE_MESSAGE
        )
        .refine((file): file is File => {
          if (!file) return true;
          return PRODUCT_VALIDATION.IMAGE.ALLOWED_TYPES.includes(
            file.type as any
          );
        }, PRODUCT_VALIDATION.IMAGE.TYPE_MESSAGE),
      presentation: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.PRESENTATION)
        .optional(),
      concentration: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CONCENTRATION)
        .optional(),
      measurementUnit: z
        .enum(PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.VALUES, {
          errorMap: () => ({
            message: PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.MESSAGE,
          }),
        })
        .optional(),
      administrationRoute: z
        .enum(PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.VALUES, {
          errorMap: () => ({
            message: PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.MESSAGE,
          }),
        })
        .optional(),
      maxStock: numberFromString
        .pipe(z.number().min(0, PRODUCT_VALIDATION.STOCK.MAX_MESSAGE))
        .optional(),
      storageCondition: z
        .enum(PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.VALUES, {
          errorMap: () => ({
            message: PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.MESSAGE,
          }),
        })
        .optional(),
      sanitaryRegistration: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.SANITARY_REGISTRATION)
        .optional(),
      stockAlert: booleanFromString.optional(),
      expirationAlert: booleanFromString.optional(),
      deleteImage: booleanFromString.optional(),
    })
    .refine(
      (data) => {
        if (data.minStock !== undefined && data.maxStock !== undefined) {
          const minStock = Number(data.minStock);
          const maxStock = Number(data.maxStock);
          return maxStock >= minStock;
        }
        return true;
      },
      {
        message: PRODUCT_VALIDATION.STOCK.COMPARISON_MESSAGE,
        path: ['maxStock'],
      }
    ),
});

export const updateStockSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    type: z.enum(PRODUCT_VALIDATION.STOCK_MOVEMENT.TYPE.VALUES, {
      errorMap: () => ({
        message: PRODUCT_VALIDATION.STOCK_MOVEMENT.TYPE.MESSAGE,
      }),
    }),
    quantity: numberFromString.pipe(
      z
        .number()
        .min(
          PRODUCT_VALIDATION.STOCK_MOVEMENT.QUANTITY.MIN,
          PRODUCT_VALIDATION.STOCK_MOVEMENT.QUANTITY.MESSAGE
        )
    ),
    reason: z.string().min(1, PRODUCT_VALIDATION.STOCK_MOVEMENT.REASON),
    batchNumber: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.BATCH_NUMBER),
    expirationDate: z
      .string()
      .transform((date) => {
        const utcDate = new Date(date);
        utcDate.setHours(0, 0, 0, 0);
        return utcDate.toISOString();
      })
      .refine(
        (date) => {
          const utcDate = new Date(date);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return utcDate > today;
        },
        {
          message: PRODUCT_VALIDATION.DATES.EXPIRATION_MESSAGE,
        }
      ),
    cost: numberFromString
      .pipe(
        z
          .number()
          .min(
            PRODUCT_VALIDATION.PRICE.MIN,
            PRODUCT_VALIDATION.PRICE.COST_MESSAGE
          )
      )
      .optional(),
  }),
});

export type CreateProductSchema = z.infer<typeof createProductSchema>;
export type UpdateProductSchema = z.infer<typeof updateProductSchema>['body'];
export type UpdateStockSchema = z.infer<typeof updateStockSchema>['body'];

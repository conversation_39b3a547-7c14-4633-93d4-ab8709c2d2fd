import { APP_CONFIG } from '@/config/app.config';

export const PRODUCT_VALIDATION = {
  SKU: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    MESSAGE: 'El SKU debe tener entre 3 y 50 caracteres',
  },
  NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
    MESSAGE: 'El nombre debe tener entre 3 y 100 caracteres',
  },
  DESCRIPTION: {
    MAX_LENGTH: 500,
    MESSAGE: 'La descripción no debe exceder los 500 caracteres',
    REQUIRED: 'La descripción es requerida',
  },
  PRICE: {
    MIN: 0,
    MESSAGE: 'El precio debe ser mayor a 0',
    COST_MESSAGE: 'El costo debe ser mayor o igual a 0',
  },
  STOCK: {
    MIN: 0,
    MESSAGE: 'El stock no puede ser negativo',
    MIN_MESSAGE: 'El stock mínimo debe ser mayor o igual a 0',
    MAX_MESSAGE: 'El stock máximo debe ser mayor o igual a 0',
    COMPARISON_MESSAGE:
      'El stock máximo debe ser mayor o igual que el stock mínimo',
  },
  IMAGE: {
    MAX_SIZE: APP_CONFIG.files.maxProductImageSize,
    ALLOWED_TYPES: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
    ] as const,
    SIZE_MESSAGE: 'El tamaño máximo de la imagen es 5MB',
    TYPE_MESSAGE: 'Formato de imagen no soportado. Use JPEG, PNG o WEBP',
  },
  REQUIRED_FIELDS: {
    LABORATORY: 'El laboratorio es requerido',
    CATEGORY: 'La categoría es requerida',
    LOCATION: 'La ubicación es requerida',
    BATCH_NUMBER: 'El número de lote es requerido',
    PRESENTATION: 'La presentación es requerida',
    CONCENTRATION: 'La concentración es requerida',
    SANITARY_REGISTRATION: 'El registro sanitario es requerido',
  },
  ENUMS: {
    MEASUREMENT_UNIT: {
      VALUES: [
        'TABLETA',
        'CAPSULA',
        'AMPOLLA',
        'FRASCO',
        'CREMA',
        'GOTAS',
      ] as const,
      MESSAGE: 'Unidad de medida inválida',
    },
    ADMINISTRATION_ROUTE: {
      VALUES: ['ORAL', 'INYECTABLE', 'TOPICA', 'OFTALMICA', 'OTRO'] as const,
      MESSAGE: 'Vía de administración inválida',
    },
    STORAGE_CONDITION: {
      VALUES: ['TEMPERATURA_AMBIENTE', 'REFRIGERACION', 'CONGELACION'] as const,
      MESSAGE: 'Condición de almacenamiento inválida',
    },
  },
  DATES: {
    EXPIRATION_MESSAGE:
      'La fecha de vencimiento debe ser posterior a la fecha actual',
  },
  NUMBER: {
    INVALID: 'Debe ser un número válido',
  },
  STOCK_MOVEMENT: {
    TYPE: {
      VALUES: ['IN', 'OUT', 'RETURN'] as const,
      MESSAGE: 'Tipo de movimiento inválido',
    },
    QUANTITY: {
      MIN: 1,
      MESSAGE: 'La cantidad debe ser mayor a 0',
    },
    REASON: 'La razón es requerida',
  },
} as const;

import { useState } from 'react';
import {
  Package,
  CircleDollarSign,
  Calendar,
  Boxes,
  ClipboardList,
  User,
  TestTube,
  Barcode,
  Thermometer,
  TestTube2,
  ImageIcon,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  formatAdministrationRoute,
  formatCurrency,
  formatDate,
  formatMeasurementUnit,
} from '@/lib/utils/format';
import { getImageUrl } from '@/lib/utils/image';
import type { ProductDetailsDialogProps } from '@/features/inventory/products';

const InfoCard = ({
  icon: Icon,
  title,
  children,
}: {
  icon: any;
  title: string;
  children: React.ReactNode;
}) => (
  <div className='bg-card rounded-lg p-5 space-y-2 shadow-sm hover:shadow-md transition-all duration-200 border'>
    <div className='flex items-center gap-2 text-muted-foreground mb-3'>
      <Icon className='w-4 h-4' />
      <p className='text-sm font-medium'>{title}</p>
    </div>
    {children}
  </div>
);

export function ProductDetailsDialog({
  product,
  open,
  onOpenChange,
}: ProductDetailsDialogProps) {
  const [hasImageError, setHasImageError] = useState(false);

  if (!product) return null;

  const handleOpenImage = () => {
    if (product.image) {
      window.open(getImageUrl(product.image), '_blank');
    }
  };

  const createdDate = new Date(product.createdAt);
  const expirationDate = new Date(product.expirationDate);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex flex-col h-[95vh] sm:h-[85vh] w-full sm:w-[95vw] md:w-[90vw] lg:w-[85vw] xl:w-[80vw] max-w-[1400px] p-4 sm:p-6'>
        <DialogHeader className='pb-3 sm:pb-4 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl font-semibold'>
            <Package className='w-4 h-4 sm:w-5 sm:h-5' />
            Detalles del Producto
          </DialogTitle>
          <p className='text-xs sm:text-sm text-muted-foreground text-left'>
            {product.name}
          </p>
        </DialogHeader>

        <ScrollArea className='flex-1 overflow-y-auto'>
          <div className='space-y-4 sm:space-y-8 py-4 sm:p-6'>
            {/* Basic information */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6'>
              <InfoCard icon={ImageIcon} title='Imagen del Producto'>
                <div className='flex justify-center'>
                  <div className='relative group w-full h-[120px]'>
                    <div className='w-full h-full rounded-lg overflow-hidden border bg-muted flex items-center justify-center'>
                      {product.image && !hasImageError ? (
                        <img
                          src={getImageUrl(product.image)}
                          alt={product.name}
                          className='w-full h-full object-contain p-2'
                          onError={(e) => {
                            setHasImageError(true);
                          }}
                          loading='lazy'
                        />
                      ) : (
                        <div className='flex flex-col items-center gap-1'>
                          <Package className='w-8 h-8' />
                          <span className='text-xs text-muted-foreground'>
                            Sin imagen
                          </span>
                        </div>
                      )}
                    </div>
                    {product.image && !hasImageError && (
                      <>
                        {/* Overlay al hacer hover */}
                        <div
                          onClick={handleOpenImage}
                          className='absolute inset-0 bg-black/60 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center cursor-pointer'
                        >
                          <div className='flex items-center gap-1'>
                            <ImageIcon className='w-4 h-4' />
                            <span className='text-xs font-medium'>
                              Ver imagen
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </InfoCard>

              <InfoCard icon={Package} title='Información Básica'>
                <p className='text-lg font-medium'>{product.name}</p>
                <p className='text-sm text-muted-foreground'>
                  SKU: {product.sku}
                </p>
                {product.barcode && (
                  <p className='text-sm text-muted-foreground flex items-center gap-2'>
                    <Barcode className='w-4 h-4' />
                    {product.barcode}
                  </p>
                )}
              </InfoCard>

              <InfoCard icon={TestTube} title='Laboratorio y Categoría'>
                <p className='text-base'>{product.laboratory?.name || 'N/A'}</p>
                <Badge variant='secondary' className='text-sm'>
                  {product.category?.name || 'N/A'}
                </Badge>
              </InfoCard>

              <InfoCard icon={CircleDollarSign} title='Información Financiera'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-muted-foreground'>
                    Precio Venta:
                  </span>
                  <p className='text-base font-medium'>
                    {formatCurrency(product.price)}
                  </p>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-muted-foreground'>Costo:</span>
                  <p className='text-base'>{formatCurrency(product.cost)}</p>
                </div>
              </InfoCard>

              {/* Technical Information */}
              <InfoCard icon={TestTube2} title='Información Técnica'>
                <div className='space-y-3'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Presentación
                    </span>
                    <Badge variant='outline'>
                      {product.presentation || 'No especificado'}
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Concentración
                    </span>
                    <Badge variant='outline'>
                      {product.concentration || 'No especificado'}
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Unidad de Medida
                    </span>
                    <Badge variant='outline'>
                      {formatMeasurementUnit(product.measurementUnit) ||
                        'No especificado'}
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Vía de Administración
                    </span>
                    <Badge variant='outline'>
                      {formatAdministrationRoute(product.administrationRoute) ||
                        'No especificado'}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard
                icon={Thermometer}
                title='Condiciones de Almacenamiento'
              >
                <div className='space-y-3'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Condición
                    </span>
                    <Badge variant='outline'>{product.storageCondition}</Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Registro Sanitario
                    </span>
                    <span>{product.sanitaryRegistration}</span>
                  </div>
                </div>
              </InfoCard>
            </div>

            {/* Stock and Alerts */}
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6'>
              <InfoCard icon={Boxes} title='Información de Stock'>
                <div className='space-y-3'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Stock Actual
                    </span>
                    <Badge
                      variant={
                        product.stock > product.minStock
                          ? 'default'
                          : 'destructive'
                      }
                    >
                      {product.stock} unidades
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Stock Mínimo
                    </span>
                    <span>{product.minStock} unidades</span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Stock Máximo
                    </span>
                    <span>{product.maxStock} unidades</span>
                  </div>
                </div>
              </InfoCard>

              <InfoCard icon={Calendar} title='Fechas Importantes'>
                <div className='space-y-3'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Fecha de Vencimiento
                    </span>
                    <Badge
                      variant={
                        new Date(product.expirationDate) > new Date()
                          ? 'outline'
                          : 'destructive'
                      }
                    >
                      {formatDate(expirationDate)}
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Número de Lote
                    </span>
                    <span>{product.batchNumber}</span>
                  </div>
                </div>
              </InfoCard>
            </div>

            {/* Description */}
            <InfoCard icon={ClipboardList} title='Descripción'>
              <p className='text-sm text-muted-foreground'>
                {product.description || 'Sin descripción'}
              </p>
            </InfoCard>

            {/* Creation Info */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <InfoCard icon={Calendar} title='Fecha de Creación'>
                <p className='text-lg font-medium'>{formatDate(createdDate)}</p>
              </InfoCard>

              <InfoCard icon={User} title='Creado por'>
                <p className='text-lg font-medium'>
                  {product.createdBy?.username || 'N/A'}
                </p>
              </InfoCard>
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className='mt-auto pt-3 sm:pt-4 border-t shrink-0'>
          <Button
            onClick={() => onOpenChange(false)}
            className='w-full sm:w-auto'
          >
            Cerrar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { useRef, useState } from 'react';
import { Printer, FileDown, X } from 'lucide-react';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { format } from 'date-fns';
import { formatDate } from '@/lib/utils/format';
import QRCode from 'react-qr-code';
import Barcode from 'react-barcode';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { formatCurrency } from '@/lib/utils/format';
import {
  ProductTicketDialogProps,
  TicketDataConfig,
  TicketPreferences,
} from '@/features/inventory/products';

const THERMAL_PRINTER_CONFIG = {
  sizes: {
    STANDARD: { name: '80mm', widthMm: 80, widthPixels: 302 },
    SMALL: { name: '58mm', widthMm: 58, widthPixels: 219 },
  },
  fonts: {
    title: "'Inter', sans-serif",
    content: "'Arial', sans-serif",
    price: "'Inter', sans-serif",
    monospace: "'Roboto Mono', monospace",
    cutLine: "'Roboto Mono', monospace",
  },
  fontSize: {
    title: '16px',
    subtitle: '14px',
    normal: '12px',
    price: '16px',
    sku: '12px',
    cutLine: '8px',
  },
  spacing: { padding: '16px', marginBottom: '8px' },
  borders: { dashed: '1px dashed #000' },
};

const SELECTED_PRINTER = THERMAL_PRINTER_CONFIG.sizes.STANDARD;
const RECEIPT_CONFIG = {
  width: SELECTED_PRINTER.widthPixels,
  fontSize: THERMAL_PRINTER_CONFIG.fontSize,
  padding: THERMAL_PRINTER_CONFIG.spacing.padding,
};

const TICKET_PREFERENCES_KEY = 'ticketPreferences';

const loadTicketPreferences = (): TicketPreferences => {
  const defaultPreferences: TicketPreferences = {
    showName: true,
    showSku: true,
    showExpiration: true,
    showPrice: true,
    orientation: 'vertical',
    showBorders: true,
    ticketType: 'QR',
  };

  try {
    const savedPreferences = localStorage.getItem(TICKET_PREFERENCES_KEY);
    return savedPreferences
      ? { ...defaultPreferences, ...JSON.parse(savedPreferences) }
      : defaultPreferences;
  } catch (error) {
    return defaultPreferences;
  }
};

const saveTicketPreferences = (preferences: TicketPreferences) => {
  try {
    localStorage.setItem(TICKET_PREFERENCES_KEY, JSON.stringify(preferences));
  } catch (error) {
    console.error('Error saving preferences:', error);
  }
};

export function ProductTicketDialog({
  product,
  isOpen,
  onOpenChange,
  ticketType,
  onTicketTypeChange,
}: ProductTicketDialogProps) {
  const ticketRef = useRef<HTMLDivElement>(null);
  const [isPrinting, setIsPrinting] = useState(false);
  const [ticketConfig, setTicketConfig] = useState<TicketDataConfig>(() =>
    loadTicketPreferences()
  );

  if (!product) return null;

  // Handlers
  const updateTicketConfig = (updates: Partial<TicketDataConfig>) => {
    const newConfig = { ...ticketConfig, ...updates };
    setTicketConfig(newConfig);
    saveTicketPreferences({ ...newConfig, ticketType });
  };

  const handleTicketTypeChange = (type: 'QR' | 'BARCODE') => {
    onTicketTypeChange(type);
    saveTicketPreferences({ ...ticketConfig, ticketType: type });
  };

  const handlePrint = async () => {
    if (!ticketRef.current) return;
    setIsPrinting(true);

    try {
      const isHorizontal = ticketConfig.orientation === 'horizontal';
      const canvas = await html2canvas(ticketRef.current, {
        scale: isHorizontal ? 3 : 2,
        backgroundColor: '#ffffff',
        width: isHorizontal ? RECEIPT_CONFIG.width * 1.5 : RECEIPT_CONFIG.width,
        useCORS: true,
        logging: false,
      });

      const printWindow = window.open('', '_blank');
      if (!printWindow) return;

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Imprimir Ticket</title>
            <style>
              @page { size: ${
                isHorizontal ? 'landscape' : 'portrait'
              } !important; margin: 0 !important; }
              body { margin: 0; padding: 0; width: ${
                SELECTED_PRINTER.widthMm
              }mm; }
              img { width: 100%; height: auto; display: block; }
              @media print {
                html, body {
                  width: ${SELECTED_PRINTER.widthMm}mm;
                  height: ${
                    isHorizontal ? SELECTED_PRINTER.widthMm * 0.7 : 'auto'
                  }mm;
                }
              }
            </style>
          </head>
          <body>
            <img src="${canvas.toDataURL('image/png')}" />
            <script>
              window.onload = () => {
                window.matchMedia('print').addListener((mql) => !mql.matches && window.close());
                window.print();
              };
            </script>
          </body>
        </html>
      `);

      printWindow.document.close();
    } catch (error) {
      console.error('Error printing:', error);
    } finally {
      setIsPrinting(false);
    }
  };

  const handleExportPDF = async () => {
    if (!ticketRef.current) return;

    try {
      const isHorizontal = ticketConfig.orientation === 'horizontal';
      const canvas = await html2canvas(ticketRef.current, {
        scale: isHorizontal ? 5 : 4,
        backgroundColor: '#ffffff',
        width: isHorizontal ? RECEIPT_CONFIG.width * 1.5 : RECEIPT_CONFIG.width,
        useCORS: true,
        logging: false,
      });

      const aspectRatio = canvas.height / canvas.width;
      const pdfWidth = SELECTED_PRINTER.widthMm;
      const pdfHeight = pdfWidth * aspectRatio;

      const pdf = new jsPDF({
        orientation: isHorizontal ? 'landscape' : 'portrait',
        unit: 'mm',
        format: [pdfWidth, pdfHeight],
      });

      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        0,
        0,
        pdfWidth,
        pdfHeight,
        undefined,
        'FAST'
      );
      const fileName = `ticket-${product.sku}-${format(
        new Date(),
        'dd-MM-yyyy-HH-mm'
      )}`;
      pdf.save(`${fileName}.pdf`);
    } catch (error) {
      console.error('Error exporting PDF:', error);
    }
  };

  const renderTicketContent = () => (
    <div
      style={{
        fontSize: THERMAL_PRINTER_CONFIG.fontSize.normal,
        color: '#000000',
        lineHeight: '1.2',
        fontFamily: THERMAL_PRINTER_CONFIG.fonts.content,
        width: '100%',
        padding: '2px 0',
      }}
    >
      {/* Content ticket */}
      {ticketConfig.showBorders && (
        <div
          style={{
            borderBottom: '1px dotted #000000',
            marginBottom: '12px',
          }}
        />
      )}

      {/* Principal content */}
      <div
        style={{
          display: 'flex',
          flexDirection:
            ticketConfig.orientation === 'horizontal' ? 'row' : 'column',
          alignItems: 'center',
          gap: '8px',
          justifyContent: 'center',
        }}
      >
        {/* Information product */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems:
              ticketConfig.orientation === 'horizontal'
                ? 'flex-start'
                : 'center',
            gap: '8px',
          }}
        >
          {ticketConfig.showName && (
            <div
              style={{
                fontSize: THERMAL_PRINTER_CONFIG.fontSize.title,
                fontFamily: THERMAL_PRINTER_CONFIG.fonts.title,
                fontWeight: '600',
              }}
            >
              {product.name}
            </div>
          )}
          {ticketConfig.showSku && (
            <div
              style={{
                fontFamily: THERMAL_PRINTER_CONFIG.fonts.monospace,
                fontSize: THERMAL_PRINTER_CONFIG.fontSize.sku,
              }}
            >
              SKU: {product.sku}
            </div>
          )}
          {ticketConfig.showExpiration && product.expirationDate && (
            <div
              style={{
                fontFamily: THERMAL_PRINTER_CONFIG.fonts.content,
              }}
            >
              Vence: {formatDate(product.expirationDate)}
            </div>
          )}
          {ticketConfig.showPrice && (
            <div
              style={{
                fontSize: THERMAL_PRINTER_CONFIG.fontSize.price,
                fontFamily: THERMAL_PRINTER_CONFIG.fonts.price,
                fontWeight: '600',
              }}
            >
              {formatCurrency(product.price)}
            </div>
          )}
        </div>

        {/* QR or Barcode */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          {ticketType === 'QR' ? (
            <QRCode value={product.sku} size={120} level='H' />
          ) : (
            <Barcode
              value={product.sku}
              width={1.2}
              height={50}
              fontSize={10}
              margin={0}
            />
          )}
        </div>
      </div>

      {/* Border button */}
      {ticketConfig.showBorders && (
        <div
          style={{
            borderTop: '1px dotted #000000',
            marginTop: '12px',
          }}
        />
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[500px] w-[95vw] max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>Imprimir Ticket</DialogTitle>
          <DialogDescription>
            Tamaño: {SELECTED_PRINTER.name} ({SELECTED_PRINTER.widthMm}mm)
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          <Tabs
            defaultValue={ticketType}
            onValueChange={(value) =>
              handleTicketTypeChange(value as 'QR' | 'BARCODE')
            }
            className='w-full'
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='QR'>Código QR</TabsTrigger>
              <TabsTrigger value='BARCODE'>Código de Barras</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className='space-y-4'>
            <div>
              <Label>Orientación</Label>
              <RadioGroup
                value={ticketConfig.orientation}
                onValueChange={(value: 'vertical' | 'horizontal') =>
                  updateTicketConfig({ orientation: value })
                }
                className='flex space-x-4 mt-2'
              >
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='vertical' id='vertical' />
                  <Label htmlFor='vertical'>Vertical</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='horizontal' id='horizontal' />
                  <Label htmlFor='horizontal'>Horizontal</Label>
                </div>
              </RadioGroup>
            </div>

            <div className='space-y-4  pt-4 border-t '>
              <div className='text-sm font-medium'>Configuración:</div>
              <div className='grid grid-cols-2 gap-4'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='name'
                    checked={ticketConfig.showName}
                    onCheckedChange={(checked) =>
                      updateTicketConfig({ showName: checked as boolean })
                    }
                  />
                  <Label htmlFor='name'>Nombre</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='sku'
                    checked={ticketConfig.showSku}
                    onCheckedChange={(checked) =>
                      updateTicketConfig({ showSku: checked as boolean })
                    }
                  />
                  <Label htmlFor='sku'>SKU</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='expiration'
                    checked={ticketConfig.showExpiration}
                    onCheckedChange={(checked) =>
                      updateTicketConfig({ showExpiration: checked as boolean })
                    }
                  />
                  <Label htmlFor='expiration'>Vencimiento</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='price'
                    checked={ticketConfig.showPrice}
                    onCheckedChange={(checked) =>
                      updateTicketConfig({ showPrice: checked as boolean })
                    }
                  />
                  <Label htmlFor='price'>Precio</Label>
                </div>
              </div>

              <div className='flex items-center space-x-2 border-t pt-4 mt-4'>
                <Checkbox
                  id='showBorders'
                  checked={ticketConfig.showBorders}
                  onCheckedChange={(checked) =>
                    updateTicketConfig({ showBorders: checked as boolean })
                  }
                />
                <Label htmlFor='showBorders'>Mostrar bordes de corte</Label>
              </div>
            </div>
          </div>

          {/* Preview ticket */}
          <div className='flex justify-center'>
            <div
              ref={ticketRef}
              className='receipt-content mx-auto'
              style={{
                width:
                  ticketConfig.orientation === 'horizontal'
                    ? `${RECEIPT_CONFIG.width * 1.5}px`
                    : `${RECEIPT_CONFIG.width}px`,
                padding: RECEIPT_CONFIG.padding,
                backgroundColor: '#ffffff',
              }}
            >
              {renderTicketContent()}
            </div>
          </div>

          <div className='flex justify-end gap-2 pt-4 border-t'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              <X className='h-4 w-4 mr-2' />
              Cancelar
            </Button>
            <Button variant='outline' onClick={handleExportPDF}>
              <FileDown className='h-4 w-4 mr-2' />
              Exportar PDF
            </Button>
            <Button onClick={handlePrint} disabled={isPrinting}>
              <Printer className='h-4 w-4 mr-2' />
              {isPrinting ? 'Imprimiendo...' : 'Imprimir'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

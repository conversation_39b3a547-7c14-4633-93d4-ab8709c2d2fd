import { useState, memo } from 'react';
import {
  Eye,
  Edit,
  Trash,
  AlertCircle,
  Package,
  CircleDollarSign,
  Printer,
  ChevronDown,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils/format';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import type {
  Product,
  ProductTableColumn,
  GetProductTableColumnsProps,
} from '@/features/inventory/products';
import { getImageUrl } from '@/lib/utils/image';

const ProductImageCell = memo(({ product }: { product: Product }) => {
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  return (
    <div className='min-w-[250px] flex items-center gap-3'>
      <div className='w-10 h-10 rounded-md bg-muted flex items-center justify-center'>
        {product.image && !imageErrors[product._id] ? (
          <img
            src={getImageUrl(product.image)}
            alt={product.name}
            className='w-full h-full object-cover rounded-md'
            onError={() =>
              setImageErrors((prev) => ({ ...prev, [product._id]: true }))
            }
            loading='lazy'
          />
        ) : (
          <Package className='w-5 h-5 text-muted-foreground' />
        )}
      </div>
      <div className='flex flex-col'>
        <span className='font-medium'>{product.name}</span>
        <span className='text-xs text-muted-foreground'>
          SKU: {product.sku}
        </span>
      </div>
    </div>
  );
});

ProductImageCell.displayName = 'ProductImageCell';

const PriceCell = memo(({ product }: { product: Product }) => (
  <div className='min-w-[120px]'>
    <div className='flex flex-col'>
      <div className='flex items-center gap-1'>
        <CircleDollarSign className='w-4 h-4 text-green-500' />
        <span className='font-medium'>{formatCurrency(product.price)}</span>
      </div>
      <span className='text-xs text-muted-foreground'>
        Costo: {formatCurrency(product.cost)}
      </span>
    </div>
  </div>
));

PriceCell.displayName = 'PriceCell';

const StockCell = memo(({ product }: { product: Product }) => (
  <TooltipProvider>
    <div className='min-w-[100px]'>
      <div className='flex flex-col'>
        <div className='flex items-center gap-2'>
          <Badge
            variant={
              product.stock <= product.minStock
                ? 'destructive'
                : product.stock >= product.maxStock
                ? 'secondary'
                : 'default'
            }
          >
            {product.stock}
          </Badge>
          {product.stock <= product.minStock && (
            <Tooltip>
              <TooltipTrigger>
                <AlertCircle className='w-4 h-4 text-destructive' />
              </TooltipTrigger>
              <TooltipContent>
                <p>Stock bajo mínimo</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <span className='text-xs text-muted-foreground'>
          Mín: {product.minStock} | Máx: {product.maxStock}
        </span>
      </div>
    </div>
  </TooltipProvider>
));

StockCell.displayName = 'StockCell';

export const getProductTableColumns = ({
  permissions,
  onEdit,
  onDelete,
  onViewDetails,
  onPrintTicket, // Nuevo prop
}: GetProductTableColumnsProps): ProductTableColumn[] => [
  {
    id: 'name',
    header: 'Producto',
    cell: (product: Product) => <ProductImageCell product={product} />,
    sortable: true,
    sortKey: 'name',
  },
  {
    id: 'category',
    header: 'Categoría',
    cell: (product: Product) => (
      <div className='min-w-[150px]'>
        <Badge variant='outline' className='font-normal'>
          {product.category?.name || 'Sin categoría'}
        </Badge>
      </div>
    ),
    sortable: true,
    sortKey: 'category.name',
  },
  {
    id: 'laboratory',
    header: 'Laboratorio',
    cell: (product: Product) => (
      <div className='min-w-[150px]'>
        <span className='text-sm text-muted-foreground'>
          {product.laboratory?.name || 'Sin laboratorio'}
        </span>
      </div>
    ),
    sortable: true,
    sortKey: 'laboratory.name',
  },
  {
    id: 'price',
    header: 'Precio',
    cell: (product: Product) => <PriceCell product={product} />,
    sortable: true,
    sortKey: 'price',
  },
  {
    id: 'stock',
    header: 'Stock',
    cell: (product: Product) => <StockCell product={product} />,
    sortable: true,
    sortKey: 'stock',
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: (product: Product) => (
      <div className='flex items-center justify-end'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant='ghost'
              size='sm'
              className='flex items-center gap-1 hover:bg-primary/10 hover:text-primary'
            >
              Opciones
              <ChevronDown className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-48'>
            <DropdownMenuItem onClick={() => onViewDetails(product)}>
              <Eye className='mr-2 h-4 w-4' />
              Ver detalles
            </DropdownMenuItem>

            {permissions.canEdit && (
              <DropdownMenuItem onClick={() => onEdit(product)}>
                <Edit className='mr-2 h-4 w-4' />
                Modificar producto
              </DropdownMenuItem>
            )}

            <DropdownMenuItem onClick={() => onPrintTicket(product)}>
              <Printer className='mr-2 h-4 w-4' />
              Imprimir ticket
            </DropdownMenuItem>

            {permissions.canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(product)}
                  className='text-destructive focus:text-destructive focus:bg-destructive/10'
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Eliminar producto
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    ),
  },
];

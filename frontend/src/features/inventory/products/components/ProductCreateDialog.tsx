import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageIcon, Package, Save, Upload, X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { APP_CONFIG } from '@/config/app.config';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  ProductCreateDialogProps,
  createProductSchema,
  type CreateProductSchema,
} from '@/features/inventory/products';
import placeholderImage from '@/assets/images/placeholder-image.png';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

export function ProductCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  categories,
  laboratories,
}: ProductCreateDialogProps) {
  const [previewUrl, setPreviewUrl] = useState<string>(placeholderImage);
  const [isDragging, setIsDragging] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const form = useForm<CreateProductSchema>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      sku: '',
      name: '',
      description: '',
      laboratory: '',
      category: '',
      price: 0,
      cost: 0,
      minStock: 0,
      maxStock: 0,
      location: '',
      expirationDate: '',
      batchNumber: '',
      barcode: '',
      requiresPrescription: false,
      image: undefined,
      presentation: '',
      concentration: '',
      measurementUnit: 'TABLETA',
      administrationRoute: 'ORAL',
      storageCondition: 'TEMPERATURA_AMBIENTE',
      sanitaryRegistration: '',
      stockAlert: false,
      expirationAlert: false,
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
      setPreviewUrl(placeholderImage);
    }
  }, [open, form]);

  const handleImageChange = useCallback(
    (
      event:
        | React.ChangeEvent<HTMLInputElement>
        | React.DragEvent<HTMLDivElement>,
      field: any
    ) => {
      let file: File | null = null;

      if ('dataTransfer' in event) {
        file = event.dataTransfer.files?.[0];
      } else {
        file = event.target.files?.[0];
      }

      if (file) {
        if (file.size > APP_CONFIG.files.maxProductImageSize) {
          form.setError('image', {
            type: 'manual',
            message: `La imagen no debe superar los ${APP_CONFIG.files.maxProductImageSize}MB`,
          });
          return;
        }

        if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
          form.setError('image', {
            type: 'manual',
            message: 'Formato de imagen no válido. Use JPG, PNG o WebP',
          });
          return;
        }

        const reader = new FileReader();
        reader.onload = () => {
          field.onChange(file);
          setPreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    },
    [form]
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    },
    []
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    },
    []
  );

  const handleRemoveImage = useCallback((field: any) => {
    field.onChange(null);
    setPreviewUrl(placeholderImage);
  }, []);

  // Función para verificar errores por tab
  const getTabErrors = () => {
    const errors = form.formState.errors;
    return {
      basic: ['name', 'sku', 'description', 'laboratory', 'category'].some(
        (field) => errors[field]
      ),
      technical: [
        'presentation',
        'concentration',
        'measurementUnit',
        'administrationRoute',
        'sanitaryRegistration',
      ].some((field) => errors[field]),
      stock: [
        'price',
        'cost',
        'stock',
        'minStock',
        'maxStock',
        'location',
      ].some((field) => errors[field]),
      additional: [
        'barcode',
        'requiresPrescription',
        'stockAlert',
        'expirationAlert',
      ].some((field) => errors[field]),
    };
  };

  const handleSubmit = async (data: CreateProductSchema) => {
    try {
      const formData = new FormData();

      // Agregar todos los campos al FormData
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (key === 'image' && value instanceof File) {
            formData.append(key, value);
          } else {
            formData.append(key, String(value));
          }
        }
      });

      await onSubmit(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Submit error:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[1000px] max-h-[90vh] overflow-hidden flex flex-col p-0 gap-0 md:gap-4 md:p-6'>
        <DialogHeader className='p-4 md:p-0 pb-4 border-b sticky top-0 bg-background z-10'>
          <DialogTitle className='flex items-center gap-2 text-lg font-semibold'>
            <Package className='w-5 h-5 text-primary' />
            Crear Nuevo Producto
          </DialogTitle>
        </DialogHeader>

        <div className='flex-1 overflow-y-auto px-4 md:px-0'>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-4 py-4'
            >
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className='w-full'
              >
                {/* Tabs para móvil - vertical */}
                <TabsList className='hidden md:grid md:grid-cols-4 w-full'>
                  {Object.entries({
                    basic: 'Información Básica',
                    technical: 'Detalles Técnicos',
                    stock: 'Inventario',
                    additional: 'Adicional',
                  }).map(([value, label]) => (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className={cn(
                        getTabErrors()[value] &&
                          'text-destructive border-destructive'
                      )}
                    >
                      {label}
                      {getTabErrors()[value] && (
                        <span className='ml-1 text-destructive'>●</span>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Tabs para móvil - horizontal scrollable */}
                <TabsList className='md:hidden w-full overflow-x-auto flex flex-nowrap whitespace-nowrap scrollbar-none'>
                  {Object.entries({
                    basic: 'Básico',
                    technical: 'Técnico',
                    stock: 'Inventario',
                    additional: 'Adicional',
                  }).map(([value, label]) => (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className={cn(
                        'flex-shrink-0',
                        getTabErrors()[value] &&
                          'text-destructive border-destructive'
                      )}
                    >
                      {label}
                      {getTabErrors()[value] && (
                        <span className='ml-1 text-destructive'>●</span>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Contenido de las tabs */}
                <div className='mt-4 md:mt-6'>
                  <TabsContent value='basic' className='space-y-4 mt-2 md:mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4'>
                      <FormField
                        control={form.control}
                        name='name'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            {' '}
                            {/* Gap consistente */}
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Nombre del Producto'
                                tooltip='Ingrese el nombre comercial del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder='Ingrese el nombre del producto'
                                {...field}
                                className={cn(
                                  form.formState.errors.name && 'border-red-500'
                                )}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='sku'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='SKU'
                                tooltip='Código único de identificación del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: MED-PAR-500'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='barcode'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Código de Barras' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: 7501234567890'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='laboratory'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Laboratorio' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                disabled={isLoading}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione un laboratorio' />
                                </SelectTrigger>
                                <SelectContent>
                                  {laboratories.map((laboratory) => (
                                    <SelectItem
                                      key={laboratory._id}
                                      value={laboratory._id}
                                    >
                                      {laboratory.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className='col-span-full'>
                        <FormField
                          control={form.control}
                          name='description'
                          render={({ field }) => (
                            <FormItem className='flex flex-col gap-2'>
                              <FormLabel>
                                <FormLabelWithTooltip label='Descripción' />
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder='Descripción detallada del producto...'
                                  className='min-h-[100px]'
                                  disabled={isLoading}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className='col-span-full'>
                        <FormField
                          control={form.control}
                          name='image'
                          render={({ field }) => (
                            <FormItem className='flex flex-col gap-2'>
                              <FormLabel
                                className={cn('flex items-center gap-2 mb-4')}
                              >
                                <ImageIcon className='w-4 h-4 text-muted-foreground' />
                                Imagen del Producto
                              </FormLabel>
                              <FormControl>
                                <div
                                  className={cn(
                                    'relative border-2 border-dashed rounded-xl p-6 transition-all duration-200',
                                    isDragging
                                      ? 'border-primary bg-primary/5'
                                      : 'border-muted-foreground/25',
                                    'hover:border-primary/50 hover:bg-primary/5'
                                  )}
                                  onDragOver={handleDragOver}
                                  onDragLeave={handleDragLeave}
                                  onDrop={(e) => {
                                    e.preventDefault();
                                    setIsDragging(false);
                                    handleImageChange(e, field);
                                  }}
                                >
                                  {/* Image Preview */}
                                  <div className='relative group mb-4'>
                                    <div className='w-40 h-40 mx-auto rounded-lg overflow-hidden border bg-background'>
                                      <img
                                        src={previewUrl}
                                        alt='Vista previa'
                                        className='w-full h-full object-contain p-2'
                                        onError={() =>
                                          setPreviewUrl(placeholderImage)
                                        }
                                      />
                                    </div>
                                    {previewUrl !== placeholderImage && (
                                      <button
                                        type='button'
                                        onClick={() => handleRemoveImage(field)}
                                        className='absolute -top-2 -right-2 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full p-1.5 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity'
                                      >
                                        <X className='w-4 h-4' />
                                      </button>
                                    )}
                                  </div>

                                  {/* Upload Controls */}
                                  <div className='text-center'>
                                    <Upload className='w-6 h-6 text-muted-foreground mx-auto mb-2' />
                                    <p className='text-sm text-muted-foreground mb-2'>
                                      Arrastra y suelta tu imagen aquí o
                                    </p>
                                    <Input
                                      type='file'
                                      accept='image/jpeg,image/png,image/webp'
                                      className='hidden'
                                      id='product-image-input'
                                      onChange={(e) =>
                                        handleImageChange(e, field)
                                      }
                                    />
                                    <Button
                                      type='button'
                                      variant='secondary'
                                      size='sm'
                                      onClick={() =>
                                        document
                                          .getElementById('product-image-input')
                                          ?.click()
                                      }
                                    >
                                      Seleccionar archivo
                                    </Button>
                                    <p className='text-xs text-muted-foreground mt-2'>
                                      PNG, JPG o WebP (máx.{' '}
                                      {APP_CONFIG.files.maxProductImageSize /
                                        (1024 * 1024)}
                                      MB)
                                    </p>
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name='category'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Categoría' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                disabled={isLoading}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione una categoría' />
                                </SelectTrigger>
                                <SelectContent>
                                  {categories.map((category) => (
                                    <SelectItem
                                      key={category._id}
                                      value={category._id}
                                    >
                                      {category.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Pestaña: Detalles Técnicos */}
                  <TabsContent value='technical' className='space-y-4 mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <FormField
                        control={form.control}
                        name='presentation'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Presentación'
                                tooltip='Forma farmacéutica del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: Tabletas 500mg'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='concentration'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Concentración'
                                tooltip='Concentración del principio activo'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: 500mg'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='measurementUnit'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Unidad de Medida</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione unidad' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='TABLETA'>Tableta</SelectItem>
                                <SelectItem value='CAPSULA'>Cápsula</SelectItem>
                                <SelectItem value='AMPOLLA'>Ampolla</SelectItem>
                                <SelectItem value='FRASCO'>Frasco</SelectItem>
                                <SelectItem value='CREMA'>Crema</SelectItem>
                                <SelectItem value='GOTAS'>Gotas</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='administrationRoute'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Vía de Administración</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione vía' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='ORAL'>Oral</SelectItem>
                                <SelectItem value='INYECTABLE'>
                                  Inyectable
                                </SelectItem>
                                <SelectItem value='TOPICA'>Tópica</SelectItem>
                                <SelectItem value='OFTALMICA'>
                                  Oftálmica
                                </SelectItem>
                                <SelectItem value='OTRO'>Otro</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='storageCondition'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Condición de Almacenamiento</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione condición' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='TEMPERATURA_AMBIENTE'>
                                  Temperatura Ambiente
                                </SelectItem>
                                <SelectItem value='REFRIGERACION'>
                                  Refrigeración
                                </SelectItem>
                                <SelectItem value='CONGELACION'>
                                  Congelación
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='sanitaryRegistration'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Registro Sanitario'
                                tooltip='Número de registro sanitario del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: RS-12345'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Pestaña: Inventario */}
                  <TabsContent value='stock' className='space-y-4 mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <FormField
                        control={form.control}
                        name='price'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Precio de Venta (Bs)' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.01'
                                min='0'
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value))
                                }
                                value={field.value || ''}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='cost'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Costo (Bs)' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.01'
                                min='0'
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value))
                                }
                                value={field.value || ''}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='minStock'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Stock Mínimo' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='1'
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value))
                                }
                                value={field.value || ''}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='maxStock'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Stock Máximo' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='1'
                                {...field}
                                onChange={(e) =>
                                  field.onChange(Number(e.target.value))
                                }
                                value={field.value || ''}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='location'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Ubicación' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: Estante A-123'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='storageCondition'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Condición de Almacenamiento' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder='Seleccione condición' />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value='TEMPERATURA_AMBIENTE'>
                                    Temperatura Ambiente
                                  </SelectItem>
                                  <SelectItem value='REFRIGERACION'>
                                    Refrigeración
                                  </SelectItem>
                                  <SelectItem value='CONGELACION'>
                                    Congelación
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='batchNumber'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Número de Lote' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: LOT123456'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='expirationDate'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Fecha de Vencimiento' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='date'
                                {...field}
                                min={new Date().toISOString().split('T')[0]}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Pestaña: Adicional */}
                  <TabsContent value='additional' className='space-y-4 mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <FormField
                        control={form.control}
                        name='requiresPrescription'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>Requiere Receta Médica</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='stockAlert'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>Activar Alertas de Stock</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='expirationAlert'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>
                                Activar Alertas de Vencimiento
                              </FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                </div>
              </Tabs>

              {/* Sección de errores y botones */}
              <div className='flex flex-col gap-4 mt-6 sticky bottom-0 bg-background p-4 md:p-0 border-t md:border-t-0'>
                {Object.values(form.formState.errors).length > 0 && (
                  <div className='rounded-lg bg-destructive/10 p-3 text-destructive text-sm'>
                    <p className='font-medium'>
                      Por favor corrija los siguientes errores:
                    </p>
                    <ul className='list-disc list-inside mt-1'>
                      {Object.entries(form.formState.errors).map(
                        ([key, error]) => (
                          <li key={key}>
                            {error && 'message' in error
                              ? String(error.message)
                              : `Error en el campo ${key}`}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                <div className='flex justify-end gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => onOpenChange(false)}
                  >
                    Cancelar
                  </Button>
                  <Button type='submit' disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className='w-4 h-4 animate-spin mr-2' />
                        Guardando...
                      </>
                    ) : (
                      <>
                        <Save className='w-4 h-4 mr-2' />
                        Guardar
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

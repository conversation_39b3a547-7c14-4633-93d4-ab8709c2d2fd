import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageIcon, Package, Save, Upload, X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { APP_CONFIG } from '@/config/app.config';
import placeholderImage from '@/assets/images/placeholder-image.png';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ProductEditDialogProps,
  createProductSchema,
  type CreateProductSchema,
} from '@/features/inventory/products';
import { getImageUrl } from '@/lib/utils/image';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

const formatDateForInput = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0];
};

export function ProductEditDialog({
  open,
  onOpenChange,
  onUpdate,
  product,
  isLoading = false,
  categories,
  laboratories,
}: ProductEditDialogProps) {
  const [previewUrl, setPreviewUrl] = useState<string>(() =>
    product?.image
      ? getImageUrl(
          product.image.startsWith('/') ? product.image : `/${product.image}`
        )
      : placeholderImage
  );
  const [isDragging, setIsDragging] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const form = useForm<CreateProductSchema>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      sku: product?.sku || '',
      name: product?.name || '',
      description: product?.description || '',
      laboratory: product?.laboratory?._id || '',
      category: product?.category?._id || '',
      price: product?.price || 0,
      cost: product?.cost || 0,
      minStock: product?.minStock || 0,
      maxStock: product?.maxStock || 0,
      location: product?.location || '',
      expirationDate: product ? formatDateForInput(product.expirationDate) : '',
      batchNumber: product?.batchNumber || '',
      barcode: product?.barcode || '',
      requiresPrescription: product?.requiresPrescription || false,
      image: undefined,
      presentation: product?.presentation || '',
      concentration: product?.concentration || '',
      measurementUnit: product?.measurementUnit || 'TABLETA',
      administrationRoute: product?.administrationRoute || 'ORAL',
      storageCondition: product?.storageCondition || 'TEMPERATURA_AMBIENTE',
      sanitaryRegistration: product?.sanitaryRegistration || '',
      stockAlert: product?.stockAlert || false,
      expirationAlert: product?.expirationAlert || false,
    },
  });

  // Función para restablecer el formulario
  const resetForm = useCallback(() => {
    if (product) {
      form.reset({
        sku: product.sku || '',
        name: product.name || '',
        description: product.description || '',
        laboratory: product.laboratory?._id || '',
        category: product.category?._id || '',
        price: product.price || 0,
        cost: product.cost || 0,
        minStock: product.minStock || 0,
        maxStock: product.maxStock || 0,
        location: product.location || '',
        expirationDate: formatDateForInput(product.expirationDate),
        batchNumber: product.batchNumber || '',
        barcode: product.barcode || '',
        requiresPrescription: product.requiresPrescription || false,
        image: undefined,
        presentation: product.presentation || '',
        concentration: product.concentration || '',
        measurementUnit: product.measurementUnit || 'TABLETA',
        administrationRoute: product.administrationRoute || 'ORAL',
        storageCondition: product.storageCondition || 'TEMPERATURA_AMBIENTE',
        sanitaryRegistration: product.sanitaryRegistration || '',
        stockAlert: product.stockAlert || false,
        expirationAlert: product.expirationAlert || false,
      });
      setPreviewUrl(
        product.image ? getImageUrl(product.image) : placeholderImage
      );
    }
    setActiveTab('basic');
    setIsDragging(false);
  }, [product, form]);

  // Efecto para manejar el reseteo cuando cambia el producto
  useEffect(() => {
    if (product) {
      resetForm();
    }
  }, [product, resetForm]);

  // Efecto para manejar el cierre del modal
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open, resetForm]);

  const handleImageChange = useCallback(
    (
      event:
        | React.ChangeEvent<HTMLInputElement>
        | React.DragEvent<HTMLDivElement>,
      field: any
    ) => {
      let file: File | null = null;

      if ('dataTransfer' in event) {
        file = event.dataTransfer.files?.[0];
      } else {
        file = event.target.files?.[0];
      }

      if (file) {
        if (file.size > APP_CONFIG.files.maxProductImageSize) {
          form.setError('image', {
            type: 'manual',
            message: `La imagen no debe superar los ${APP_CONFIG.files.maxProductImageSize}MB`,
          });
          return;
        }

        if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
          form.setError('image', {
            type: 'manual',
            message: 'Formato de imagen no válido. Use JPG, PNG o WebP',
          });
          return;
        }

        const reader = new FileReader();
        reader.onload = () => {
          field.onChange(file);
          setPreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    },
    [form]
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    },
    []
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    },
    []
  );

  const handleRemoveImage = useCallback(
    (field: any) => {
      field.onChange(null);
      setPreviewUrl(placeholderImage);
      form.setValue('deleteImage', true);
    },
    [form]
  );

  // Función para verificar errores por tab
  const getTabErrors = () => {
    const errors = form.formState.errors;
    return {
      basic: ['name', 'sku', 'description', 'laboratory', 'category'].some(
        (field) => errors[field]
      ),
      technical: [
        'presentation',
        'concentration',
        'measurementUnit',
        'administrationRoute',
        'sanitaryRegistration',
      ].some((field) => errors[field]),
      stock: ['price', 'cost', 'minStock', 'maxStock', 'location'].some(
        (field) => errors[field]
      ),
      additional: [
        'barcode',
        'requiresPrescription',
        'stockAlert',
        'expirationAlert',
      ].some((field) => errors[field]),
    };
  };

  const handleSubmit = async (data: CreateProductSchema) => {
    try {
      const formData = new FormData();
      const shouldDeleteImage = form.getValues('deleteImage');

      // Agregar todos los campos al FormData excepto deleteImage
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null && key !== 'deleteImage') {
          if (key === 'image' && value instanceof File) {
            formData.append(key, value);
          } else {
            formData.append(key, String(value));
          }
        }
      });

      // Agregar deleteImage una sola vez si es necesario
      if (shouldDeleteImage) {
        formData.append('deleteImage', 'true');
      }

      await onUpdate(product._id, formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Submit error:', error);
    }
  };

  // Modificar el handler de onOpenChange para incluir el reseteo
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetForm();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='w-[95vw] max-w-[1000px] max-h-[90vh] overflow-hidden flex flex-col p-0 gap-0 md:gap-4 md:p-6'>
        <DialogHeader className='p-4 md:p-0 pb-4 border-b sticky top-0 bg-background z-10'>
          <DialogTitle className='flex items-center gap-2 text-lg font-semibold'>
            <Package className='w-5 h-5 text-primary' />
            Editar Producto
          </DialogTitle>
        </DialogHeader>
        <div className='flex-1 overflow-y-auto px-4 md:px-0'>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-4 py-4'
            >
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className='w-full'
              >
                {/* Tabs para desktop */}
                <TabsList className='hidden md:grid md:grid-cols-4 w-full'>
                  {Object.entries({
                    basic: 'Información Básica',
                    technical: 'Detalles Técnicos',
                    stock: 'Inventario',
                    additional: 'Adicional',
                  }).map(([value, label]) => (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className={cn(
                        getTabErrors()[value] &&
                          'text-destructive border-destructive'
                      )}
                    >
                      {label}
                      {getTabErrors()[value] && (
                        <span className='ml-1 text-destructive'>●</span>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Tabs para móvil - scrollable horizontal */}
                <TabsList className='md:hidden w-full overflow-x-auto flex flex-nowrap whitespace-nowrap scrollbar-none'>
                  {Object.entries({
                    basic: 'Básico',
                    technical: 'Técnico',
                    stock: 'Inventario',
                    additional: 'Adicional',
                  }).map(([value, label]) => (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className={cn(
                        'flex-shrink-0',
                        getTabErrors()[value] &&
                          'text-destructive border-destructive'
                      )}
                    >
                      {label}
                      {getTabErrors()[value] && (
                        <span className='ml-1 text-destructive'>●</span>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Contenido de las tabs */}
                <div className='mt-4 md:mt-6'>
                  {/* Tab: Información Básica */}
                  <TabsContent value='basic' className='space-y-4 mt-2 md:mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4'>
                      <FormField
                        control={form.control}
                        name='name'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            {' '}
                            {/* Gap consistente */}
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Nombre del Producto'
                                tooltip='Ingrese el nombre comercial del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder='Ingrese el nombre del producto'
                                {...field}
                                className={cn(
                                  form.formState.errors.name && 'border-red-500'
                                )}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='sku'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='SKU'
                                tooltip='Código único de identificación del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: MED-PAR-500'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='barcode'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Código de Barras' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: 7501234567890'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='laboratory'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Laboratorio'
                                tooltip='Seleccione el laboratorio del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={field.value || ''}
                                onValueChange={field.onChange}
                                disabled={isLoading}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione un laboratorio' />
                                </SelectTrigger>
                                <SelectContent>
                                  {laboratories.map((laboratory) => (
                                    <SelectItem
                                      key={laboratory._id}
                                      value={laboratory._id}
                                    >
                                      {laboratory.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='category'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip
                                label='Categoría'
                                tooltip='Seleccione la categoría del producto'
                              />
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={field.value || ''}
                                onValueChange={field.onChange}
                                disabled={isLoading}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Seleccione una categoría' />
                                </SelectTrigger>
                                <SelectContent>
                                  {categories.map((category) => (
                                    <SelectItem
                                      key={category._id}
                                      value={category._id}
                                    >
                                      {category.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className='col-span-full'>
                        <FormField
                          control={form.control}
                          name='description'
                          render={({ field }) => (
                            <FormItem className='flex flex-col gap-2'>
                              <FormLabel>
                                <FormLabelWithTooltip label='Descripción' />
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder='Descripción detallada del producto...'
                                  className='min-h-[100px]'
                                  disabled={isLoading}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className='col-span-full'>
                        <FormField
                          control={form.control}
                          name='image'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className='flex items-center gap-2 mb-4'>
                                <ImageIcon className='w-4 h-4 text-muted-foreground' />
                                Imagen del Producto
                              </FormLabel>
                              <FormControl>
                                <div className='space-y-4'>
                                  <div
                                    className={cn(
                                      'relative border-2 border-dashed rounded-xl p-8 transition-all duration-200 ease-in-out',
                                      isDragging
                                        ? 'border-primary bg-primary/5'
                                        : 'border-muted-foreground/25',
                                      'hover:border-primary/50 hover:bg-primary/5'
                                    )}
                                    onDragOver={handleDragOver}
                                    onDragLeave={handleDragLeave}
                                    onDrop={(e) => {
                                      e.preventDefault();
                                      setIsDragging(false);
                                      handleImageChange(e, field);
                                    }}
                                  >
                                    <div className='flex flex-col items-center justify-center gap-6'>
                                      {/* Preview de la imagen */}
                                      <div className='relative group'>
                                        <div className='w-48 h-48 rounded-xl overflow-hidden border bg-background flex items-center justify-center'>
                                          <img
                                            src={previewUrl}
                                            alt='Vista previa'
                                            className='w-full h-full object-contain p-2'
                                            onError={() =>
                                              setPreviewUrl(placeholderImage)
                                            }
                                          />
                                        </div>
                                        {previewUrl !== placeholderImage && (
                                          <button
                                            type='button'
                                            onClick={() =>
                                              handleRemoveImage(field)
                                            }
                                            className='absolute -top-2 -right-2 bg-destructive hover:bg-destructive/90 text-destructive-foreground rounded-full p-1.5 shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200'
                                          >
                                            <X className='w-4 h-4' />
                                          </button>
                                        )}
                                      </div>

                                      <div className='text-center space-y-2'>
                                        <div className='flex flex-col items-center gap-2'>
                                          <Upload className='w-8 h-8 text-muted-foreground mb-2' />
                                          <p className='text-sm text-muted-foreground'>
                                            Arrastra y suelta tu imagen aquí o
                                          </p>
                                          <Input
                                            type='file'
                                            accept='image/jpeg,image/png,image/webp'
                                            className='hidden'
                                            id='product-image-input'
                                            onChange={(e) =>
                                              handleImageChange(e, field)
                                            }
                                          />
                                          <Button
                                            type='button'
                                            variant='secondary'
                                            size='sm'
                                            className='mt-2'
                                            onClick={() =>
                                              document
                                                .getElementById(
                                                  'product-image-input'
                                                )
                                                ?.click()
                                            }
                                          >
                                            Seleccionar archivo
                                          </Button>
                                          <p className='text-xs text-muted-foreground mt-2'>
                                            PNG, JPG o WebP (máx.{' '}
                                            {
                                              APP_CONFIG.files
                                                .maxProductImageSize
                                            }
                                            MB)
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Detalles Técnicos */}
                  <TabsContent
                    value='technical'
                    className='space-y-4 mt-2 md:mt-4'
                  >
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4'>
                      <FormField
                        control={form.control}
                        name='concentration'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Concentración' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: 500mg'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='measurementUnit'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Unidad de Medida' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder='Seleccione unidad' />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value='TABLETA'>
                                    Tableta
                                  </SelectItem>
                                  <SelectItem value='CAPSULA'>
                                    Cápsula
                                  </SelectItem>
                                  <SelectItem value='AMPOLLA'>
                                    Ampolla
                                  </SelectItem>
                                  <SelectItem value='FRASCO'>Frasco</SelectItem>
                                  <SelectItem value='CREMA'>Crema</SelectItem>
                                  <SelectItem value='GOTAS'>Gotas</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='administrationRoute'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Vía de Administración' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder='Seleccione vía' />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value='ORAL'>Oral</SelectItem>
                                  <SelectItem value='INYECTABLE'>
                                    Inyectable
                                  </SelectItem>
                                  <SelectItem value='TOPICA'>Tópica</SelectItem>
                                  <SelectItem value='OFTALMICA'>
                                    Oftálmica
                                  </SelectItem>
                                  <SelectItem value='OTRO'>Otro</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='sanitaryRegistration'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Registro Sanitario' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: RS-12345-2023'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='presentation'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Presentación' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: Caja x 30 tabletas'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Tab: Inventario */}
                  <TabsContent value='stock' className='space-y-4 mt-2 md:mt-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4'>
                      <FormField
                        control={form.control}
                        name='price'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Precio de Venta (Bs)' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='any'
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? 0
                                      : Number(e.target.value);
                                  field.onChange(value);
                                }}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='cost'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Costo (Bs)' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='any'
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? 0
                                      : Number(e.target.value);
                                  field.onChange(value);
                                }}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='minStock'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Stock Mínimo' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='1'
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? 0
                                      : Number(e.target.value);
                                  field.onChange(value);
                                }}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='maxStock'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Stock Máximo' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                min='0'
                                step='1'
                                {...field}
                                value={field.value ?? ''}
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? 0
                                      : Number(e.target.value);
                                  field.onChange(value);
                                }}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='location'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Ubicación' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: Estante A-123'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='storageCondition'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Condición de Almacenamiento' />
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={isLoading}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder='Seleccione condición' />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value='TEMPERATURA_AMBIENTE'>
                                    Temperatura Ambiente
                                  </SelectItem>
                                  <SelectItem value='REFRIGERACION'>
                                    Refrigeración
                                  </SelectItem>
                                  <SelectItem value='CONGELACION'>
                                    Congelación
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='batchNumber'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Número de Lote' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Ej: LOT123456'
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='expirationDate'
                        render={({ field }) => (
                          <FormItem className='flex flex-col gap-2'>
                            <FormLabel>
                              <FormLabelWithTooltip label='Fecha de Vencimiento' />
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='date'
                                {...field}
                                min={new Date().toISOString().split('T')[0]}
                                disabled={isLoading}
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Tab: Adicional */}
                  <TabsContent
                    value='additional'
                    className='space-y-4 mt-2 md:mt-4'
                  >
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4'>
                      <FormField
                        control={form.control}
                        name='requiresPrescription'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>Requiere Receta Médica</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='stockAlert'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>Activar Alertas de Stock</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='expirationAlert'
                        render={({ field }) => (
                          <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className='space-y-1 leading-none'>
                              <FormLabel>
                                Activar Alertas de Vencimiento
                              </FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>
                </div>
              </Tabs>

              {/* Sección de errores y botones */}
              <div className='flex flex-col gap-4 mt-6 sticky bottom-0 bg-background p-4 md:p-0 border-t md:border-t-0'>
                {Object.values(form.formState.errors).length > 0 && (
                  <div className='rounded-lg bg-destructive/10 p-3 text-destructive text-sm'>
                    <p className='font-medium'>
                      Por favor corrija los siguientes errores:
                    </p>
                    <ul className='list-disc list-inside mt-1'>
                      {Object.entries(form.formState.errors).map(
                        ([key, error]) => (
                          <li key={key}>
                            {error && 'message' in error
                              ? String(error.message)
                              : `Error en el campo ${key}`}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                <div className='flex justify-end gap-3'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => handleOpenChange(false)}
                  >
                    Cancelar
                  </Button>
                  <Button type='submit' disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className='w-4 h-4 animate-spin mr-2' />
                        Guardando...
                      </>
                    ) : (
                      <>
                        <Save className='w-4 h-4 mr-2' />
                        Guardar Cambios
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

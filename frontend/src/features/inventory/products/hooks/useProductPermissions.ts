import { useAuth, PERMISSIONS } from '@/features/auth';

export function useProductPermissions() {
  const { checkPermission } = useAuth();

  return {
    checkPermission,
    permissions: {
      canList: checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.LIST),
      canCreate: checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.CREATE),
      canEdit: checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.EDIT),
      canDelete: checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.DELETE),
    },
  };
}

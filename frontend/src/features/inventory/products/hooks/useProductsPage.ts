import { useState } from 'react';
import { useProducts } from './useProducts';
import type { Product, UpdateProductDto } from '@/features/inventory/products';

export function useProductsPage() {
  const {
    products,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    filters,
    setFilters,
    pagination,
    stats,
  } = useProducts();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedProductForDetails, setSelectedProductForDetails] =
    useState<Product | null>(null);
  const [editDialogProduct, setEditDialogProduct] = useState<Product | null>(
    null
  );
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [isTicketDialogOpen, setIsTicketDialogOpen] = useState(false);
  const [selectedProductForTicket, setSelectedProductForTicket] =
    useState<Product | null>(null);
  const [ticketType, setTicketType] = useState<'QR' | 'BARCODE'>('QR');

  // Handlers
  const handleCreate = () => setIsDialogOpen(true);

  const handleDelete = (product: Product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  const handleEdit = (product: Product) => {
    setEditDialogProduct(product);
    setIsEditDialogOpen(true);
  };

  const handleViewDetails = (product: Product) => {
    setSelectedProductForDetails(product);
    setIsDetailsDialogOpen(true);
  };

  const handlePrintTicket = (product: Product) => {
    setSelectedProductForTicket(product);
    setIsTicketDialogOpen(true);
  };

  // Submit handlers
  const handleSubmitCreate = async (data: FormData) => {
    await createMutation.mutateAsync(data);
    setIsDialogOpen(false);
  };

  const handleConfirmDelete = async () => {
    if (!selectedProduct) return;
    await deleteMutation.mutateAsync(selectedProduct._id);
    setIsDeleteDialogOpen(false);
  };

  const editDialog = {
    product: editDialogProduct,
    isOpen: isEditDialogOpen,
    onOpenChange: setIsEditDialogOpen,
    onUpdate: async (
      productId: string,
      productData: FormData | UpdateProductDto
    ) => {
      setIsEditLoading(true);
      try {
        await updateMutation.mutateAsync({
          id: productId,
          product: productData,
        });
        setIsEditDialogOpen(false);
        setEditDialogProduct(null);
      } finally {
        setIsEditLoading(false);
      }
    },
    isLoading: isEditLoading,
  };

  const handleFilterChange = (filterUpdates: Partial<typeof filters>) => {
    setFilters({ ...filters, ...filterUpdates, page: 1 });
  };

  return {
    products,
    isLoading,
    stats,

    filters: {
      search: filters.search,
      category: filters.category,
      laboratory: filters.laboratory,
      showInactive: filters.showInactive,
      onSearchChange: (search: string) => handleFilterChange({ search }),
      onCategoryChange: (category: string) => handleFilterChange({ category }),
      onLaboratoryChange: (laboratory: string) =>
        handleFilterChange({ laboratory }),
      onActiveStatusChange: (showInactive: boolean) =>
        handleFilterChange({ showInactive }),
      reset: () => {
        setFilters({
          search: '',
          category: 'ALL',
          laboratory: 'ALL',
          showInactive: false,
          page: 1,
          limit: 10,
        });
      },
    },

    pagination: {
      currentPage: filters.page,
      totalPages: pagination.totalPages,
      totalRecords: pagination.totalRecords,
      limit: filters.limit || 10,
      onPageChange: (page: number) => setFilters({ ...filters, page }),
      onLimitChange: (limit: number) => setFilters({ ...filters, limit }),
    },

    actions: {
      handlers: {
        onCreate: handleCreate,
        onDelete: handleDelete,
        onEdit: handleEdit,
        onViewDetails: handleViewDetails,
        onPrintTicket: handlePrintTicket,
      },
    },

    dialog: {
      isOpen: isDialogOpen,
      onOpenChange: setIsDialogOpen,
      onSubmit: handleSubmitCreate,
      isLoading: createMutation.isPending,
    },

    deleteDialog: {
      product: selectedProduct,
      isOpen: isDeleteDialogOpen,
      onOpenChange: setIsDeleteDialogOpen,
      onConfirm: handleConfirmDelete,
      isLoading: deleteMutation.isPending,
    },

    detailsDialog: {
      product: selectedProductForDetails,
      isOpen: isDetailsDialogOpen,
      onOpenChange: setIsDetailsDialogOpen,
    },

    editDialog,

    ticketDialog: {
      product: selectedProductForTicket,
      isOpen: isTicketDialogOpen,
      onOpenChange: setIsTicketDialogOpen,
      ticketType,
      onTicketTypeChange: setTicketType,
    },
  };
}

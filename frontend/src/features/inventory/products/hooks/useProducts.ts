import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { productService } from '../services/product.service';
import { useProductsStore } from '../store/products.store';
import { Product, UpdateProductDto } from '../types/product.types';

interface UseProductsOptions {
  enabled?: boolean;
  getAllProducts?: boolean;
}

export function useProducts({
  enabled = true,
  getAllProducts = false,
}: UseProductsOptions = {}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { filters, setFilters } = useProductsStore();

  const productsForSale = useQuery({
    queryKey: ['products', 'sale'],
    queryFn: () => productService.getProductsForSale(),
    enabled: getAllProducts && enabled,
  });

  const productsWithPagination = useQuery({
    queryKey: ['products', filters],
    queryFn: () => {
      const cleanFilters = {
        search: filters.search || undefined,
        category: filters.category === 'ALL' ? undefined : filters.category,
        laboratory:
          filters.laboratory === 'ALL' ? undefined : filters.laboratory,
        showInactive: filters.showInactive,
        page: filters.page,
        limit: filters.limit,
      };

      return productService.getProducts(cleanFilters);
    },
    enabled: !getAllProducts && enabled,
  });

  const query = getAllProducts ? productsForSale : productsWithPagination;
  const data = query.data;

  const createMutation = useMutation({
    mutationFn: (data: FormData) => productService.createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: 'Éxito',
        description: 'Producto creado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al crear el producto',
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation<
    { product: Product; message: string },
    Error,
    { id: string; product: FormData | UpdateProductDto }
  >({
    mutationFn: ({ id, product }) => productService.updateProduct(id, product),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: 'Éxito',
        description: 'Producto actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al actualizar el producto',
        variant: 'destructive',
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (productId: string) => productService.deleteProduct(productId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      toast({
        title: 'Éxito',
        description: 'Producto eliminado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al eliminar el producto',
        variant: 'destructive',
      });
    },
  });

  return {
    products: getAllProducts ? data?.products || [] : data?.products || [],
    isLoading: query.isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
    filters,
    setFilters,
    pagination: {
      totalPages: getAllProducts ? 1 : data?.totalPages || 1,
      totalRecords: data?.totalRecords || 0,
      currentPage: getAllProducts ? 1 : filters.page,
    },
    stats: {
      totalProducts: data?.totalRecords || 0,
      activeProducts: data?.products?.filter((p) => p.isActive).length || 0,
    },
  };
}

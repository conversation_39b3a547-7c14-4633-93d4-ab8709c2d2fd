import { create } from 'zustand';
import type {
  ProductFilters,
  Product,
  ProductDashboardResponse,
} from '@/features/inventory/products';
import { StockMovement } from '@/features/inventory/stock';

interface ProductsStore {
  // Estado existente
  filters: ProductFilters;
  setFilters: (filters: Partial<ProductFilters>) => void;
  resetFilters: () => void;

  // Nuevo estado para productos
  products: Product[];
  totalRecords: number;
  selectedProduct: Product | null;
  isLoading: boolean;

  // Estado para dashboard
  dashboard: ProductDashboardResponse | null;

  // Estado para movimientos de stock
  stockMovements: StockMovement[];
  stockMovementsTotal: number;

  // Acciones
  setProducts: (products: Product[], total: number) => void;
  setSelectedProduct: (product: Product | null) => void;
  setDashboard: (dashboard: ProductDashboardResponse) => void;
  setStockMovements: (movements: StockMovement[], total: number) => void;
  setLoading: (loading: boolean) => void;
}

const initialFilters: ProductFilters = {
  search: '',
  category: 'ALL',
  laboratory: 'ALL',
  showInactive: false,
  storageCondition: 'ALL',
  administrationRoute: 'ALL',
  page: 1,
  limit: 10,
};

export const useProductsStore = create<ProductsStore>((set) => ({
  // Estado inicial
  filters: initialFilters,
  products: [],
  totalRecords: 0,
  selectedProduct: null,
  isLoading: false,
  dashboard: null,
  stockMovements: [],
  stockMovementsTotal: 0,

  // Acciones existentes
  setFilters: (newFilters) =>
    set((state) => ({
      filters: {
        ...state.filters,
        ...newFilters,
      },
    })),
  resetFilters: () => set({ filters: initialFilters }),

  // Nuevas acciones
  setProducts: (products, total) => set({ products, totalRecords: total }),

  setSelectedProduct: (product) => set({ selectedProduct: product }),

  setDashboard: (dashboard) => set({ dashboard }),

  setStockMovements: (movements, total) =>
    set({
      stockMovements: movements,
      stockMovementsTotal: total,
    }),

  setLoading: (loading) => set({ isLoading: loading }),
}));

import { api } from '@/lib/api/api';
import type {
  StockMovementParams,
  StockMovementsResponse,
  StockMovementResponse,
  StockEntryDto,
  StockOutputDto,
} from '@/features/inventory/stock';

interface UpdateStockMovementDto {
  reference?: string;
  observations?: string;
}

export const stockService = {
  getStockMovements: async (
    params: StockMovementParams
  ): Promise<StockMovementsResponse> => {
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value != null && value !== ''
      )
    );
    const { data } = await api.get('/stock/movements', { params: cleanParams });
    return data;
  },

  createStockEntry: async (
    data: StockEntryDto
  ): Promise<StockMovementResponse> => {
    const { productId, ...stockData } = data;
    const { data: response } = await api.post(
      `/stock/products/${productId}/entries`,
      stockData
    );
    return response;
  },

  createStockOutput: async (
    data: StockOutputDto
  ): Promise<StockMovementResponse> => {
    const { productId, ...stockData } = data;
    const { data: response } = await api.post(
      `/stock/products/${productId}/outputs`,
      stockData
    );
    return response;
  },

  updateStockMovement: async (
    id: string,
    data: UpdateStockMovementDto
  ): Promise<StockMovementResponse> => {
    const { data: response } = await api.patch(`/stock/movements/${id}`, data);
    return response;
  },

  deleteStockMovement: async (
    movementId: string
  ): Promise<StockMovementResponse> => {
    const { data } = await api.delete(`/stock/movements/${movementId}`);
    return data;
  },
};

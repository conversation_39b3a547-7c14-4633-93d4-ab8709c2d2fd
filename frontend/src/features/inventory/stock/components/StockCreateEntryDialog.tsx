import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Package, Save, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ProductSelector } from './ProductSelector';
import {
  createStockEntrySchema,
  type CreateStockEntrySchemaType,
  type StockEntryDto,
  STOCK_MOVEMENT_REASONS_OPTIONS,
  CreateStockEntryDialogProps,
} from '@/features/inventory/stock';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

export function CreateStockEntryDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  products = [],
}: CreateStockEntryDialogProps) {
  const form = useForm<CreateStockEntrySchemaType>({
    resolver: zodResolver(createStockEntrySchema),
    defaultValues: {
      productId: '',
      quantity: 0,
      reason: 'PURCHASE',
      reference: '',
      batchNumber: '',
      expirationDate: '',
      cost: 0,
      observations: '',
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const handleSubmit = async (data: CreateStockEntrySchemaType) => {
    try {
      const stockEntry: StockEntryDto = {
        productId: data.productId,
        quantity: data.quantity,
        reason: data.reason,
        reference: data.reference,
        batchNumber: data.batchNumber,
        expirationDate: data.expirationDate,
        cost: data.cost,
        observations: data.observations,
      };

      await onSubmit(stockEntry);
      onOpenChange(false);
    } catch (error: any) {
      if (error.response?.data?.details) {
        error.response.data.details.forEach((detail: any) => {
          const fieldName = detail.field.replace('body.', '');
          form.setError(fieldName as any, {
            type: 'server',
            message: detail.message,
          });
        });
      } else if (error.response?.data?.error) {
        form.setError('productId', {
          type: 'server',
          message: error.response.data.error,
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[700px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Package className='w-5 h-5 text-primary' />
            Nueva Entrada de Stock
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 sm:space-y-6 py-2'
          >
            <FormField
              control={form.control}
              name='productId'
              render={({ field }) => (
                <ProductSelector
                  products={products}
                  selectedProductId={field.value}
                  onProductSelect={field.onChange}
                  isLoading={isLoading}
                />
              )}
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='quantity'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Cantidad'
                      tooltip='Cantidad de unidades a ingresar al inventario'
                    />
                    <FormControl>
                      <Input
                        type='number'
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                        disabled={isLoading}
                        placeholder='Ej: 100'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='cost'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Costo Unitario'
                      tooltip='Costo por unidad del producto'
                    />
                    <FormControl>
                      <Input
                        type='number'
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                        disabled={isLoading}
                        placeholder='Ej: 1000.00'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='reason'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Motivo'
                      tooltip='Razón por la cual se está realizando el ingreso del producto'
                    />
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Seleccionar motivo de entrada' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STOCK_MOVEMENT_REASONS_OPTIONS.ENTRY.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='reference'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Referencia'
                      tooltip='Número o código de referencia para identificar este movimiento (ej: número de factura, orden, etc.)'
                    />
                    <FormControl>
                      <Input
                        {...field}
                        disabled={isLoading}
                        placeholder='Ej: FAC-2023-001'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='batchNumber'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Número de Lote'
                      tooltip='Identificador único del lote del producto. Importante para la trazabilidad y control de caducidad'
                    />
                    <FormControl>
                      <Input
                        {...field}
                        disabled={isLoading}
                        placeholder='Ej: LOT2023001'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='expirationDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Fecha de Vencimiento'
                      tooltip='Fecha en la que el producto expira o caduca'
                    />
                    <FormControl>
                      <Input type='date' {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='observations'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Observaciones'
                    tooltip='Notas adicionales sobre esta entrada de stock'
                  />
                  <FormControl>
                    <Textarea
                      {...field}
                      className='min-h-[100px]'
                      disabled={isLoading}
                      placeholder='Ej: Producto recibido en buen estado, Entrada por devolución de cliente...'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-2 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='flex items-center gap-2'
              >
                {isLoading ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin' />
                    Guardando...
                  </>
                ) : (
                  <>
                    <Save className='h-4 w-4' />
                    Guardar
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

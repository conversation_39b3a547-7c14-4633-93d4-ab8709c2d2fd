import { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  StockEntriesTableProps,
  getStockEntriesTableColumns,
} from '@/features/inventory/stock';

const tableVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      when: 'beforeChildren',
      staggerChildren: 0.1,
    },
  },
};

const rowVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.3 },
  },
};

export function StockEntriesTable({
  entries,
  isLoading,
  permissions,
  onViewDetails,
  onEdit,
  onDelete,
}: StockEntriesTableProps) {
  const columns = useMemo(
    () =>
      getStockEntriesTableColumns({
        permissions,
        onViewDetails,
        onEdit,
        onDelete,
      }),
    [permissions, onViewDetails, onEdit, onDelete]
  );

  if (isLoading) {
    return (
      <div className='flex justify-center items-center py-8'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return (
    <motion.div initial='hidden' animate='visible' variants={tableVariants}>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.id}>{column.header}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {entries.map((entry, index) => (
              <motion.tr
                key={`${entry._id}-${index}`}
                variants={rowVariants}
                className='border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted'
              >
                {columns.map((column) => (
                  <TableCell key={`${entry._id}-${column.id}`}>
                    {column.cell({ row: { original: entry } })}
                  </TableCell>
                ))}
              </motion.tr>
            ))}
            {entries.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  No hay movimientos para mostrar
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </motion.div>
  );
}

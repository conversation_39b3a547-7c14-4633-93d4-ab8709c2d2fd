import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Package, Save, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ProductSelector } from './ProductSelector';
import {
  createStockOutputSchema,
  STOCK_MOVEMENT_REASONS_OPTIONS,
  type StockOutputDto,
  StockCreateOutputDialogProps,
} from '@/features/inventory/stock';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

export function StockCreateOutputDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  products = [],
}: StockCreateOutputDialogProps) {
  const form = useForm<StockOutputDto>({
    resolver: zodResolver(createStockOutputSchema),
    defaultValues: {
      productId: '',
      quantity: 0,
      reason: '',
      batchNumber: '',
      observations: '',
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const handleSubmit = async (data: StockOutputDto) => {
    try {
      await onSubmit(data);
      onOpenChange(false);
    } catch (error: any) {
      if (error.response?.data?.details) {
        error.response.data.details.forEach((detail: any) => {
          const fieldName = detail.field.replace('body.', '');
          form.setError(fieldName as any, {
            type: 'server',
            message: detail.message,
          });
        });
      } else if (error.response?.data?.error) {
        form.setError('productId', {
          type: 'server',
          message: error.response.data.error,
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[700px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Package className='w-5 h-5 text-primary' />
            Nueva Salida de Stock
          </DialogTitle>
          <DialogDescription>
            Complete los detalles para registrar una nueva salida de stock
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 sm:space-y-6 py-2'
          >
            <div className='space-y-4'>
              <FormField
                control={form.control}
                name='productId'
                render={({ field }) => (
                  <ProductSelector
                    products={products}
                    selectedProductId={field.value}
                    onProductSelect={field.onChange}
                    isLoading={isLoading}
                    required
                  />
                )}
              />

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='quantity'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabelWithTooltip
                        label='Cantidad'
                        tooltip='Cantidad de unidades a retirar del inventario'
                      />
                      <FormControl>
                        <Input
                          type='number'
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                          disabled={isLoading}
                          placeholder='Ej: 10'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='reason'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabelWithTooltip
                        label='Motivo'
                        tooltip='Razón por la cual se está realizando la salida del producto'
                      />
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Seleccionar motivo de salida' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STOCK_MOVEMENT_REASONS_OPTIONS.OUTPUT.map(
                            (option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='batchNumber'
                render={({ field }) => (
                  <FormItem>
                    <FormLabelWithTooltip
                      label='Número de lote'
                      tooltip='Identificador único del lote del producto. Importante para la trazabilidad y control de caducidad'
                    />
                    <FormControl>
                      <Input
                        {...field}
                        disabled={isLoading}
                        placeholder='Ej: LOT2023001'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='observations'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observaciones (Opcional)</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className='min-h-[100px]'
                        disabled={isLoading}
                        placeholder='Agregue notas adicionales sobre esta salida de stock (opcional)'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex justify-end gap-2 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='flex items-center gap-2'
              >
                {isLoading ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin' />
                    Guardando...
                  </>
                ) : (
                  <>
                    <Save className='h-4 w-4' />
                    Guardar
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

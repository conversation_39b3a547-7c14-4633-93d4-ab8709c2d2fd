import { Eye, Trash, Edit, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type {
  StockEntriesTableColumnProps,
  StockMovement,
} from '../types/stock.types';
import { formatDate } from '@/lib/utils/format';
import { STOCK_MOVEMENT_REASONS_LABELS } from '@/features/inventory/stock';

export const getStockOutputsTableColumns = ({
  permissions,
  onViewDetails,
  onEdit,
  onDelete,
}: StockEntriesTableColumnProps) => [
  {
    id: 'product',
    header: 'Producto',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <div className='flex items-start gap-3'>
        <div className='flex flex-col'>
          <div className='flex items-center gap-2'>
            <span className='font-medium'>
              {row.original.product?.name || 'Producto no disponible'}
            </span>
            {!row.original.product && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <AlertCircle className='h-4 w-4 text-amber-500' />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Este producto ya no existe en el inventario</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <span className='text-sm text-muted-foreground'>
            SKU: {row.original.product?.sku || 'N/A'}
          </span>
        </div>
      </div>
    ),
  },
  {
    id: 'quantity',
    header: 'Cantidad',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <span className='font-medium text-red-600 dark:text-rose-500'>
        -{row.original.quantity}
      </span>
    ),
  },
  {
    id: 'reason',
    header: 'Motivo',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <span>
        {STOCK_MOVEMENT_REASONS_LABELS[row.original.reason] ||
          row.original.reason}
      </span>
    ),
  },
  {
    id: 'batchNumber',
    header: 'Lote',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <div className='flex flex-col'>
        <span className='font-medium'>{row.original.batchNumber || '-'}</span>
        {row.original.expirationDate && (
          <span className='text-sm text-muted-foreground'>
            Vence: {formatDate(row.original.expirationDate)}
          </span>
        )}
      </div>
    ),
  },
  {
    id: 'previousStock',
    header: 'Stock Anterior',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <span className='font-medium'>{row.original.previousStock}</span>
    ),
  },
  {
    id: 'newStock',
    header: 'Stock Actual',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <span className='font-medium'>{row.original.newStock}</span>
    ),
  },
  {
    id: 'actions',
    header: 'Acciones',
    cell: ({ row }: { row: { original: StockMovement } }) => (
      <div className='flex items-center gap-2'>
        <Button
          variant='ghost'
          size='icon'
          onClick={() => onViewDetails(row.original)}
        >
          <Eye className='h-4 w-4' />
        </Button>

        {permissions.canEdit && (
          <Button
            variant='ghost'
            size='icon'
            onClick={() => onEdit(row.original)}
          >
            <Edit className='h-4 w-4' />
          </Button>
        )}

        {permissions.canDelete && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  onClick={() => onDelete(row.original)}
                  className='text-red-600 hover:text-red-700 dark:text-rose-500 dark:hover:text-rose-400 hover:bg-red-500/10 dark:hover:bg-rose-500/20'
                >
                  <Trash className='h-4 w-4' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Eliminar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    ),
  },
];

export default getStockOutputsTableColumns;

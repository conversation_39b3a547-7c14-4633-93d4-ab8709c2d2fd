import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { StockEntriesActionsProps } from '@/features/inventory/stock';

export function StockEntriesActions({
  onCreate,
  canCreate,
}: StockEntriesActionsProps) {
  return (
    <div className='flex justify-end'>
      {canCreate && (
        <Button
          onClick={() => onCreate(true)}
          className='flex items-center gap-2'
        >
          <Plus className='h-4 w-4' />
          <span>Nueva entrada</span>
        </Button>
      )}
    </div>
  );
}

import { useRef, useState } from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils/styles';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

interface Product {
  _id: string;
  name: string;
  sku: string;
  barcode?: string;
}

interface ProductSelectorProps {
  products: Product[];
  selectedProductId: string;
  onProductSelect: (productId: string) => void;
  isLoading?: boolean;
  required?: boolean;
}

export function ProductSelector({
  products,
  selectedProductId,
  onProductSelect,
  isLoading = false,
}: ProductSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [openCombobox, setOpenCombobox] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const filteredProducts = !searchQuery
    ? products
    : products.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.barcode?.toLowerCase().includes(searchQuery.toLowerCase())
      );

  return (
    <FormItem className='flex flex-col relative'>
      <FormLabel className='flex gap-1 after:content-["*"] after:text-red-500'>
        Producto
      </FormLabel>
      <FormControl>
        <div className='relative'>
          <Button
            variant='outline'
            role='combobox'
            type='button'
            className='w-full justify-between'
            onClick={() => setOpenCombobox(!openCombobox)}
            disabled={isLoading}
          >
            {products.find((p) => p._id === selectedProductId)?.name ||
              'Seleccionar producto'}
            <Search className='ml-2 h-4 w-4 shrink-0 opacity-50' />
          </Button>

          {openCombobox && (
            <div className='absolute top-[100%] mt-1 w-full bg-popover border rounded-md shadow-lg z-50'>
              <div className='p-2 border-b border-border'>
                <Input
                  ref={searchInputRef}
                  placeholder='Buscar producto...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='w-full'
                />
              </div>

              <ScrollArea className='h-[200px]'>
                {filteredProducts.length === 0 ? (
                  <div className='p-4 text-center text-sm text-muted-foreground'>
                    No se encontraron productos
                  </div>
                ) : (
                  filteredProducts.map((product) => (
                    <div
                      key={product._id}
                      onClick={() => {
                        onProductSelect(product._id);
                        setOpenCombobox(false);
                        setSearchQuery('');
                      }}
                      className={cn(
                        'px-4 py-2 cursor-pointer transition-colors text-sm',
                        'hover:bg-accent hover:text-accent-foreground',
                        selectedProductId === product._id
                          ? 'bg-primary text-primary-foreground'
                          : 'text-popover-foreground'
                      )}
                    >
                      <div className='text-sm'>{product.name}</div>
                      <div className='text-xs opacity-70'>
                        SKU: {product.sku}
                      </div>
                    </div>
                  ))
                )}
              </ScrollArea>
            </div>
          )}
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
}

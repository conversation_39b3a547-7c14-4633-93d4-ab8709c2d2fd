import { AlertCircle, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import type { StockMovement } from '@/features/inventory/stock';

interface DeleteStockOutputDialogProps {
  entry: StockMovement | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

export function DeleteStockOutputDialog({
  entry,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: DeleteStockOutputDialogProps) {
  if (!entry) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <AlertCircle className='w-5 h-5 text-destructive' />
            Eliminar Salida
          </DialogTitle>
          <DialogDescription className='text-left pt-2'>
            ¿Estás seguro que deseas eliminar esta salida de stock? Esta acción
            no se puede deshacer y afectará al inventario actual.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='flex justify-end gap-2 pt-4'>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant='destructive'
            onClick={onConfirm}
            disabled={isLoading}
            className='flex items-center gap-2'
          >
            {isLoading ? (
              <Loader2 className='w-4 h-4 animate-spin' />
            ) : (
              <AlertCircle className='w-4 h-4' />
            )}
            Eliminar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { useState } from 'react';
import { Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  STOCK_MOVEMENT_REASONS_OPTIONS,
  type StockEntriesFiltersProps,
} from '@/features/inventory/stock';

export function StockOutputsFilters({
  search,
  startDate,
  endDate,
  reason,
  batchNumber,
  onSearchChange,
  onStartDateChange,
  onEndDateChange,
  onReasonChange,
  onBatchNumberChange,
  onApplyFilters,
  onReset,
}: StockEntriesFiltersProps) {
  const [localSearch, setLocalSearch] = useState(search);
  const [localStartDate, setLocalStartDate] = useState<Date | undefined>(
    startDate
  );
  const [localEndDate, setLocalEndDate] = useState<Date | undefined>(endDate);
  const [localReason, setLocalReason] = useState<string | undefined>(reason);
  const [localBatchNumber, setLocalBatchNumber] = useState<string | undefined>(
    batchNumber
  );
  const hasActiveFilters = startDate || endDate || reason || batchNumber;

  const handleSearch = () => {
    onSearchChange(localSearch.trim());
    onApplyFilters();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleReset = () => {
    setLocalSearch('');
    setLocalStartDate(undefined);
    setLocalEndDate(undefined);
    setLocalReason(undefined);
    setLocalBatchNumber(undefined);

    onReset();
  };

  const handleApplyFilters = () => {
    onSearchChange(localSearch.trim());
    onStartDateChange(localStartDate);
    onEndDateChange(localEndDate);
    onReasonChange(localReason);
    onBatchNumberChange(localBatchNumber);
    onApplyFilters();

    const dropdownTrigger = document.querySelector(
      '[data-state="open"]'
    ) as HTMLElement;
    if (dropdownTrigger) {
      dropdownTrigger.click();
    }
  };

  return (
    <TooltipProvider>
      <div className='flex flex-col sm:flex-row gap-3 w-full sm:items-center'>
        <div className='w-full sm:w-[80%] flex gap-2'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <Input
              placeholder='Buscar por producto o referencia...'
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              onKeyPress={handleKeyPress}
              className='pl-10 w-full'
            />
          </div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={handleSearch}
                variant='default'
                size='icon'
                className='md:hidden h-10 w-10'
              >
                <Search className='h-4 w-4' />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Buscar</TooltipContent>
          </Tooltip>

          <Button
            onClick={handleSearch}
            variant='default'
            className='hidden md:flex items-center gap-2'
          >
            <Search className='h-4 w-4' />
            <span>Buscar</span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' className='flex items-center gap-1.5'>
                <Filter className='h-4 w-4' />
                <span className='hidden md:inline'>Filtros</span>
                {hasActiveFilters && (
                  <span className='ml-1.5 text-xs bg-primary/10 text-primary rounded-full px-2 py-0.5'>
                    {
                      [startDate, endDate, reason, batchNumber].filter(Boolean)
                        .length
                    }
                  </span>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className='w-[calc(100vw-2rem)] md:w-[600px] p-4'
              align='center'
            >
              <div className='space-y-4'>
                {/* Fechas */}
                <div className='space-y-2'>
                  <h4 className='font-medium text-sm'>Rango de fechas</h4>
                  <div className='flex flex-col md:flex-row gap-4'>
                    <div className='flex-1'>
                      <label className='text-xs text-muted-foreground mb-1 block'>
                        Fecha inicio
                      </label>
                      <DatePicker
                        placeholder='Fecha inicio'
                        value={localStartDate}
                        onChange={(date) => setLocalStartDate(date)}
                        className='w-full'
                      />
                    </div>
                    <div className='flex-1'>
                      <label className='text-xs text-muted-foreground mb-1 block'>
                        Fecha fin
                      </label>
                      <DatePicker
                        placeholder='Fecha fin'
                        value={localEndDate}
                        onChange={(date) => setLocalEndDate(date)}
                        minDate={localStartDate}
                        className='w-full'
                      />
                    </div>
                  </div>
                </div>

                <div className='space-y-2'>
                  <h4 className='font-medium text-sm'>Detalles adicionales</h4>
                  <div className='flex flex-col md:flex-row gap-4'>
                    <div className='flex-1'>
                      <label className='text-xs text-muted-foreground mb-1 block'>
                        Motivo
                      </label>
                      <Select
                        value={localReason}
                        onValueChange={setLocalReason}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Filtrar por motivo' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='ALL'>Todos los motivos</SelectItem>
                          {STOCK_MOVEMENT_REASONS_OPTIONS.OUTPUT.map(
                            (option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className='flex-1'>
                      <label className='text-xs text-muted-foreground mb-1 block'>
                        Número de lote
                      </label>
                      <Input
                        placeholder='Ingrese número'
                        value={localBatchNumber || ''}
                        onChange={(e) => setLocalBatchNumber(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <div className='flex justify-end gap-2 pt-4'>
                  <Button variant='outline' onClick={handleReset} type='button'>
                    Limpiar
                  </Button>
                  <Button onClick={handleApplyFilters} type='button'>
                    Aplicar filtros
                  </Button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </TooltipProvider>
  );
}

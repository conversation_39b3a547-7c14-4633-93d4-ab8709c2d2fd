import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Package,
  Save,
  Loader2,
  Calendar,
  Box,
  Tag,
  Hash,
  Archive,
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  updateStockMovementSchema,
  type UpdateStockMovementSchemaType,
  type EditStockEntryDialogProps,
} from '@/features/inventory/stock';
import { formatDate } from '@/lib/utils/format';
import { FormLabelWithTooltip } from '@/components/common/FormLabelWithTooltip';

export function EditStockEntryDialog({
  entry,
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: EditStockEntryDialogProps) {
  const form = useForm<UpdateStockMovementSchemaType>({
    resolver: zodResolver(updateStockMovementSchema),
    defaultValues: {
      reference: entry?.reference || '',
      observations: entry?.observations || '',
    },
  });

  useEffect(() => {
    if (entry && open) {
      form.reset({
        reference: entry.reference || '',
        observations: entry.observations || '',
      });
    }
  }, [entry, open, form]);

  const handleSubmit = async (formData: UpdateStockMovementSchemaType) => {
    try {
      if (!entry?._id) return;

      await onSubmit({
        _id: entry._id,
        reference: formData.reference,
        observations: formData.observations,
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Error updating stock entry:', error);
    }
  };

  const typeConfig = {
    IN: {
      label: 'Entrada',
      className:
        'bg-emerald-100 text-emerald-700 dark:bg-emerald-500/20 dark:text-emerald-400',
    },
    OUT: {
      label: 'Salida',
      className: 'bg-red-100 text-red-700 dark:bg-red-500/20 dark:text-red-400',
    },
    RETURN: {
      label: 'Devolución',
      className:
        'bg-blue-100 text-blue-700 dark:bg-blue-500/20 dark:text-blue-400',
    },
  } as const;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[700px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <Package className='w-5 h-5 text-primary' />
            Editar Movimiento
          </DialogTitle>
          <div className='flex items-center gap-2 mt-2'>
            <Badge
              variant='outline'
              className={typeConfig[entry?.type || 'IN'].className}
            >
              {typeConfig[entry?.type || 'IN'].label}
            </Badge>
            <Badge variant='outline' className='bg-muted/50'>
              <Calendar className='w-3.5 h-3.5 mr-1.5' />
              {formatDate(entry?.createdAt)}
            </Badge>
          </div>
        </DialogHeader>

        <Card>
          <CardContent className='p-4 grid grid-cols-1 sm:grid-cols-2 gap-4'>
            <div className='space-y-3'>
              <div className='flex items-center gap-2'>
                <Box className='w-4 h-4 text-muted-foreground' />
                <span className='text-muted-foreground'>Producto:</span>
                <span className='font-medium'>{entry?.product?.name}</span>
              </div>
              <div className='flex items-center gap-2'>
                <Tag className='w-4 h-4 text-muted-foreground' />
                <span className='text-muted-foreground'>SKU:</span>
                <span>{entry?.product?.sku}</span>
              </div>
            </div>
            <div className='space-y-3'>
              <div className='flex items-center gap-2'>
                <Hash className='w-4 h-4 text-muted-foreground' />
                <span className='text-muted-foreground'>Cantidad:</span>
                <Badge
                  variant={entry?.type === 'OUT' ? 'destructive' : 'default'}
                  className='ml-auto'
                >
                  {entry?.type === 'OUT' ? '-' : '+'}
                  {entry?.quantity}
                </Badge>
              </div>
              <div className='flex items-center gap-2'>
                <Archive className='w-4 h-4 text-muted-foreground' />
                <span className='text-muted-foreground'>Lote:</span>
                <span className='ml-auto'>{entry?.batchNumber}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 mt-4'
          >
            <FormField
              control={form.control}
              name='reference'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Referencia'
                    tooltip='Número o código de referencia para identificar este movimiento (ej: número de factura, orden, etc.)'
                  />
                  <FormControl>
                    <Input
                      {...field}
                      disabled={isLoading}
                      placeholder='Ej: FAC-2023-001, ORD-123'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='observations'
              render={({ field }) => (
                <FormItem>
                  <FormLabelWithTooltip
                    label='Observaciones'
                    tooltip='Notas adicionales sobre este movimiento de stock'
                  />
                  <FormControl>
                    <Textarea
                      {...field}
                      className='min-h-[100px] resize-none'
                      disabled={isLoading}
                      placeholder='Ej: Producto recibido en buen estado, Entrada por devolución de cliente...'
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end gap-2 pt-4 border-t'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='flex items-center gap-2'
              >
                {isLoading ? (
                  <Loader2 className='w-4 h-4 animate-spin' />
                ) : (
                  <Save className='w-4 h-4' />
                )}
                Guardar cambios
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

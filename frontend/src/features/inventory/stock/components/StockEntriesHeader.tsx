import { cn } from '@/lib/utils/styles';
import { StockEntriesHeaderProps } from '@/features/inventory/stock';

export function StockEntriesHeader({
  title,
  description,
  className,
}: StockEntriesHeaderProps) {
  return (
    <div className={cn('space-y-2 pb-4', className)}>
      <div className='flex items-center gap-4'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>{title}</h1>
          {description && (
            <p className='text-sm text-muted-foreground mt-1'>{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}

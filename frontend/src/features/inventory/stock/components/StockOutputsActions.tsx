import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { StockOutputsActionsProps } from '@/features/inventory/stock';

export function StockOutputsActions({
  onCreate,
  canCreate,
}: StockOutputsActionsProps) {
  return (
    <div className='flex justify-end'>
      {canCreate && (
        <Button
          onClick={() => onCreate(true)}
          className='flex items-center gap-2'
        >
          <Plus className='h-4 w-4' />
          <span>Nueva salida</span>
        </Button>
      )}
    </div>
  );
}

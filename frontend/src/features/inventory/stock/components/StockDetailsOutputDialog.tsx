import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  Calendar,
  User,
  FileText,
  Hash,
  ArrowRightLeft,
  Barcode,
  ArrowDownCircle,
  RotateCcw,
} from 'lucide-react';
import { formatDate } from '@/lib/utils/format';
import type { ViewStockEntryDialogProps } from '@/features/inventory/stock';
import { InfoCard } from '@/components/common/InfoCard';

export function StockDetailsOutputDialog({
  entry,
  open,
  onOpenChange,
}: ViewStockEntryDialogProps) {
  if (!entry) return null;

  const typeConfig = {
    IN: {
      label: 'Entrada',
      icon: ArrowDownCircle,
      variant: 'default',
      className: 'bg-emerald-100 text-emerald-700',
    },
    OUT: {
      label: 'Salida',
      icon: ArrowDownCircle,
      variant: 'destructive',
      className: 'bg-red-100 text-red-700',
    },
    RETURN: {
      label: 'Devolución',
      icon: RotateCcw,
      variant: 'secondary',
      className: 'bg-blue-100 text-blue-700',
    },
  } as const;

  const {
    label: typeLabel,
    icon: TypeIcon,
    className,
  } = typeConfig[entry.type];

  const reasonLabels: Record<string, string> = {
    PURCHASE: 'Compra',
    SALE: 'Venta',
    ADJUSTMENT: 'Ajuste',
    DAMAGED: 'Dañado',
    EXPIRED: 'Vencido',
    CLIENT_RETURN: 'Devolución de Cliente',
    SUPPLIER_RETURN: 'Devolución a Proveedor',
    OTHER: 'Otro',
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex flex-col h-auto w-full sm:w-[95vw] md:w-[90vw] lg:w-[85vw] xl:w-[80vw] max-w-[1000px] p-3 sm:p-4'>
        <DialogHeader className='pb-2 border-b shrink-0'>
          <DialogTitle className='flex items-center gap-2 text-base font-semibold'>
            <Package className='w-4 h-4 text-primary' />
            Detalles del Movimiento de Stock
          </DialogTitle>
          <div className='flex items-center gap-2 mt-1'>
            <Badge variant='outline' className={className}>
              <TypeIcon className='w-3 h-3 mr-1' />
              {typeLabel}
            </Badge>
            <Badge variant='outline' className='bg-muted'>
              <Calendar className='w-3 h-3 mr-1' />
              {formatDate(entry.createdAt)}
            </Badge>
          </div>
        </DialogHeader>

        <ScrollArea className='flex-1 overflow-y-auto'>
          <div className='space-y-3 py-2'>
            {/* Información del Producto */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
              <InfoCard icon={Package} title='Información del Producto'>
                <div className='flex items-start gap-3'>
                  <div className='flex-1'>
                    <p className='font-medium text-lg'>{entry.product?.name}</p>
                    <p className='text-sm text-muted-foreground'>
                      SKU: {entry.product?.sku}
                    </p>
                    {entry.product?.barcode && (
                      <p className='text-sm text-muted-foreground flex items-center gap-1'>
                        <Barcode className='w-3 h-3' />
                        {entry.product.barcode}
                      </p>
                    )}
                  </div>
                </div>
              </InfoCard>

              <InfoCard icon={ArrowRightLeft} title='Cambio de Stock'>
                <div className='space-y-2'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Anterior
                    </span>
                    <Badge variant='outline'>{entry.previousStock}</Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Cantidad
                    </span>
                    <Badge
                      variant={entry.type === 'OUT' ? 'destructive' : 'default'}
                    >
                      {entry.type === 'OUT' ? '-' : '+'}
                      {entry.quantity}
                    </Badge>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>Nuevo</span>
                    <Badge variant='outline' className='bg-primary/10'>
                      {entry.newStock}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard icon={FileText} title='Detalles de la Operación'>
                <div className='space-y-3'>
                  <div>
                    <span className='text-sm text-muted-foreground'>
                      Motivo
                    </span>
                    <p className='font-medium'>
                      {reasonLabels[entry.reason] || entry.reason}
                    </p>
                  </div>
                  {entry.reference && (
                    <div>
                      <span className='text-sm text-muted-foreground'>
                        Referencia
                      </span>
                      <p className='font-medium'>{entry.reference}</p>
                    </div>
                  )}
                </div>
              </InfoCard>
            </div>

            {/* Detalles del Movimiento */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
              {(entry.batchNumber || entry.expirationDate) && (
                <InfoCard icon={Hash} title='Información de Lote'>
                  <div className='space-y-3'>
                    {entry.batchNumber && (
                      <div>
                        <span className='text-sm text-muted-foreground'>
                          Número de Lote
                        </span>
                        <p className='font-medium'>{entry.batchNumber}</p>
                      </div>
                    )}
                    {entry.expirationDate && (
                      <div>
                        <span className='text-sm text-muted-foreground'>
                          Fecha de Vencimiento
                        </span>
                        <p className='font-medium'>
                          {formatDate(entry.expirationDate)}
                        </p>
                      </div>
                    )}
                  </div>
                </InfoCard>
              )}

              <InfoCard icon={User} title='Información de Registro'>
                <div className='space-y-3'>
                  <div>
                    <span className='text-sm text-muted-foreground'>
                      Registrado por
                    </span>
                    <p className='font-medium'>
                      {entry.createdBy?.username || 'Sistema'}
                    </p>
                  </div>
                  <div>
                    <span className='text-sm text-muted-foreground'>
                      Fecha de Registro
                    </span>
                    <p className='font-medium'>{formatDate(entry.createdAt)}</p>
                  </div>
                </div>
              </InfoCard>
            </div>

            {/* Observaciones */}
            {entry.observations && (
              <InfoCard icon={FileText} title='Observaciones'>
                <p className='text-sm whitespace-pre-wrap'>
                  {entry.observations}
                </p>
              </InfoCard>
            )}
          </div>
        </ScrollArea>

        <DialogFooter className='mt-auto pt-4 border-t'>
          <Button
            onClick={() => onOpenChange(false)}
            className='w-full sm:w-auto'
          >
            Cerrar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { Alert<PERSON>ir<PERSON>, Loader } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import type { DeleteStockEntryDialogProps } from '@/features/inventory/stock';

export function DeleteStockEntryDialog({
  entry,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: DeleteStockEntryDialogProps) {
  if (!entry) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-[95vw] max-w-[500px]'>
        <DialogHeader className='pb-4 border-b'>
          <DialogTitle className='flex items-center gap-2 text-lg sm:text-xl'>
            <AlertCircle className='w-5 h-5 text-destructive' />
            Eliminar Movimiento
          </DialogTitle>
          <DialogDescription className='text-left pt-2'>
            ¿Estás seguro que deseas eliminar este movimiento de stock? Esta
            acción no se puede deshacer y afectará al inventario actual.
          </DialogDescription>
        </DialogHeader>

        <div className='bg-muted/50 rounded-lg p-3 space-y-2 text-sm my-4'>
          <div>
            <span className='text-muted-foreground'>Producto:</span>{' '}
            <span className='font-medium'>{entry.product?.name}</span>
          </div>
          <div>
            <span className='text-muted-foreground'>Cantidad:</span>{' '}
            <span className='font-medium'>{entry.quantity}</span>
          </div>
          <div>
            <span className='text-muted-foreground'>Referencia:</span>{' '}
            <span>{entry.reference || 'N/A'}</span>
          </div>
        </div>

        <div className='flex justify-end gap-2 pt-4 border-t'>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant='destructive'
            onClick={onConfirm}
            disabled={isLoading}
            className='flex items-center gap-2'
          >
            {isLoading && <Loader className='w-4 h-4 animate-spin' />}
            Eliminar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

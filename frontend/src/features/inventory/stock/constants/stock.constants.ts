export const STOCK_MOVEMENT_TYPES = {
  ENTRY: 'IN',
  OUTPUT: 'OUT',
} as const;

export const STOCK_MOVEMENT_REASONS = {
  // Entradas
  PURCHASE: 'PURCHASE', // Compra
  CLIENT_RETURN: 'CLIENT_RETURN', // Devolución de cliente
  CORRECTION_ADD: 'CORRECTION_ADD', // Corrección sumando
  TRANSFER: 'TRANSFER', // Transferencia
  OTHER: 'OTHER', // Otros

  // Salidas
  SALE: 'SALE', // Venta
  SUPPLIER_RETURN: 'SUPPLIER_RETURN', // Devolución a proveedor
  CORRECTION_REMOVE: 'CORRECTION_REMOVE', // Corrección restando
  DAMAGE: 'DAMAGE', // Producto dañado
  EXPIRATION: 'EXPIRATION', // Producto vencido
  INVENTORY_LOSS: 'INVENTORY_LOSS', // Pérdida inventario
} as const;

export const STOCK_MOVEMENT_REASONS_LABELS = {
  ALL: 'Todos',
  PURCHASE: 'Compra',
  SALE: 'Venta',
  CLIENT_RETURN: 'Devolución de Cliente',
  SUPPLIER_RETURN: 'Devolución a Proveedor',
  CORRECTION_ADD: 'Corrección (+)',
  CORRECTION_REMOVE: 'Corrección (-)',
  DAMAGE: 'Daño',
  EXPIRATION: 'Vencimiento',
  INVENTORY_LOSS: 'Pérdida de Inventario',
  TRANSFER: 'Transferencia',
  OTHER: 'Otro',
} as const;

export const STOCK_MOVEMENT_REASONS_OPTIONS = {
  ENTRY: [
    { value: 'PURCHASE', label: 'Compra' },
    { value: 'CLIENT_RETURN', label: 'Devolución de Cliente' },
    { value: 'CORRECTION_ADD', label: 'Corrección (+)' },
    { value: 'TRANSFER', label: 'Transferencia' },
    { value: 'OTHER', label: 'Otros' },
  ],
  OUTPUT: [
    { value: 'SALE', label: 'Venta' },
    { value: 'SUPPLIER_RETURN', label: 'Devolución a Proveedor' },
    { value: 'CORRECTION_REMOVE', label: 'Corrección (-)' },
    { value: 'DAMAGE', label: 'Producto Dañado' },
    { value: 'EXPIRATION', label: 'Producto Vencido' },
    { value: 'INVENTORY_LOSS', label: 'Pérdida de Inventario' },
    { value: 'TRANSFER', label: 'Transferencia' },
    { value: 'OTHER', label: 'Otros' },
  ],
} as const;

export const STOCK_ERRORS = {
  FETCH_ERROR: 'Error al obtener los movimientos de stock',
  PRODUCT_NOT_FOUND: 'Producto no encontrado',
  CREATION_FAILED: 'Error al crear el movimiento de stock',
  UPDATE_FAILED: 'Error al actualizar el movimiento de stock',
  DELETE_FAILED: 'Error al eliminar el movimiento de stock',
  INSUFFICIENT_STOCK: 'Stock insuficiente para realizar la operación',
  INVALID_QUANTITY: 'La cantidad debe ser mayor a 0',
  INVALID_BATCH_NUMBER: 'El número de lote es inválido',
  INVALID_EXPIRATION_DATE: 'La fecha de vencimiento es inválida',
  INVALID_MOVEMENT_TYPE: 'Tipo de movimiento inválido',
  INVALID_REASON: 'Razón de movimiento inválida',
  BATCH_NOT_FOUND: 'Lote no encontrado',
} as const;

export const STOCK_MESSAGES = {
  ENTRY_CREATED: 'Entrada de stock creada exitosamente',
  OUTPUT_CREATED: 'Salida de stock creada exitosamente',
  ADJUSTMENT_CREATED: 'Ajuste de stock creado exitosamente',
  MOVEMENT_UPDATED: 'Movimiento de stock actualizado exitosamente',
  MOVEMENT_DELETED: 'Movimiento de stock eliminado exitosamente',
  LOW_STOCK_ALERT: 'Productos con stock bajo detectados',
  EXPIRING_ALERT: 'Productos próximos a vencer detectados',
} as const;

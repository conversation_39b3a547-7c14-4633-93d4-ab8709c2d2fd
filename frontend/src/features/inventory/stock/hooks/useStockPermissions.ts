import { useAuth, PERMISSIONS } from '@/features/auth';

export function useStockPermissions() {
  const { checkPermission } = useAuth();

  return {
    checkPermission,
    permissions: {
      entries: {
        canList: checkPermission(PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST),
        canCreate: checkPermission(PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE),
        canEdit: checkPermission(PERMISSIONS.INVENTORY.STOCK.ENTRIES.EDIT),
        canDelete: checkPermission(PERMISSIONS.INVENTORY.STOCK.ENTRIES.DELETE),
      },
      outputs: {
        canList: checkPermission(PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST),
        canCreate: checkPermission(PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE),
        canEdit: checkPermission(PERMISSIONS.INVENTORY.STOCK.OUTPUTS.EDIT),
        canDelete: checkPermission(PERMISSIONS.INVENTORY.STOCK.OUTPUTS.DELETE),
      },
      movements: {
        canList: checkPermission(PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST),
        canCreate: checkPermission(
          PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.CREATE
        ),
        canEdit: checkPermission(PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.EDIT),
        canDelete: checkPermission(
          PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.DELETE
        ),
      },
    },
  };
}

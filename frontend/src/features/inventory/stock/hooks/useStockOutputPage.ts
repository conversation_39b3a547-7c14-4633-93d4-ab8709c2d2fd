import { useState } from 'react';
import { useStockOutput, type StockMovement } from '@/features/inventory/stock';

type DialogState = {
  create: boolean;
  edit: boolean;
  details: boolean;
  delete: boolean;
};

export function useStockOutputPage() {
  const [dialogState, setDialogState] = useState<DialogState>({
    create: false,
    edit: false,
    details: false,
    delete: false,
  });
  const [selectedEntry, setSelectedEntry] = useState<StockMovement | null>(
    null
  );

  const {
    movements: outputs,
    isLoading,
    createOutput,
    deleteMovement,
    editOutput,
    isUpdating,
    isDeleting,
    filters: stockFilters,
    pagination: stockPagination,
    products,
    isLoadingProducts,
  } = useStockOutput({
    isDialogOpen: dialogState.create || dialogState.edit,
  });

  const handleDialogChange = (dialog: keyof DialogState, open: boolean) => {
    setDialogState((prev) => ({ ...prev, [dialog]: open }));
    if (!open) setSelectedEntry(null);
  };

  const handleEntryAction = (
    action: keyof DialogState,
    entry: StockMovement
  ) => {
    setSelectedEntry(entry);
    setDialogState((prev) => ({ ...prev, [action]: true }));
  };

  return {
    outputs,
    isLoading,
    filters: stockFilters,
    pagination: stockPagination,

    actions: {
      handlers: {
        onCreate: () => handleDialogChange('create', true),
        onViewDetails: (entry: StockMovement) =>
          handleEntryAction('details', entry),
        onEdit: (entry: StockMovement) => handleEntryAction('edit', entry),
        onDelete: (entry: StockMovement) => handleEntryAction('delete', entry),
      },
    },

    dialog: {
      isOpen: dialogState.create,
      onOpenChange: (open: boolean) => handleDialogChange('create', open),
      onSubmit: createOutput,
      isLoading: isLoadingProducts,
      products,
    },

    deleteDialog: {
      entry: selectedEntry,
      isOpen: dialogState.delete,
      onOpenChange: (open: boolean) => handleDialogChange('delete', open),
      onConfirm: async () => {
        if (!selectedEntry?._id) return;
        await deleteMovement(selectedEntry._id);
        handleDialogChange('delete', false);
      },
      isLoading: isDeleting,
    },

    editDialog: {
      entry: selectedEntry,
      isOpen: dialogState.edit,
      onOpenChange: (open: boolean) => handleDialogChange('edit', open),
      onSubmit: async (data) => {
        await editOutput(data);
        handleDialogChange('edit', false);
      },
      isLoading: isUpdating,
    },

    detailsDialog: {
      entry: selectedEntry,
      isOpen: dialogState.details,
      onOpenChange: (open: boolean) => handleDialogChange('details', open),
    },
  };
}

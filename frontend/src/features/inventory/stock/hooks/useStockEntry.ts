import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { useProducts } from '@/features/inventory/products';
import type {
  StockMovementFilters,
  StockMovementDto,
  UseStockOptions,
} from '@/features/inventory/stock';
import { stockService } from '@/features/inventory/stock';

export function useStockEntry({
  type = 'IN',
  productId,
  initialFilters = {},
  isDialogOpen = false,
}: UseStockOptions = {}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<StockMovementFilters>({
    search: '',
    type,
    startDate: undefined,
    endDate: undefined,
    reason: undefined,
    batchNumber: undefined,
    ...initialFilters,
  });
  const [shouldApplyFilters, setShouldApplyFilters] = useState(false);
  const [page, setPage] = useState(1);
  const limit = 10;

  const handleFiltersChange = {
    onSearchChange: (search: string) =>
      setFilters((prev) => ({ ...prev, search })),
    onStartDateChange: (startDate?: Date) =>
      setFilters((prev) => ({ ...prev, startDate })),
    onEndDateChange: (endDate?: Date) =>
      setFilters((prev) => ({ ...prev, endDate })),
    onReasonChange: (reason?: string) =>
      setFilters((prev) => ({ ...prev, reason })),
    onBatchNumberChange: (batchNumber?: string) =>
      setFilters((prev) => ({ ...prev, batchNumber })),
    onTypeChange: (newType: typeof type) => {
      setFilters((prev) => ({ ...prev, type: newType }));
      setPage(1);
    },
    onApplyFilters: () => {
      setShouldApplyFilters(true);
      setPage(1);
    },
    onReset: () => {
      setFilters({
        search: '',
        type,
        startDate: undefined,
        endDate: undefined,
        reason: undefined,
        batchNumber: undefined,
      });
      setPage(1);
      setShouldApplyFilters(true);
    },
  };

  const { data, isLoading } = useQuery({
    queryKey: ['stock-movements', filters, page, limit, shouldApplyFilters],
    queryFn: () => {
      setShouldApplyFilters(false);
      return stockService.getStockMovements({
        page,
        limit,
        search: filters.search || undefined,
        startDate: filters.startDate
          ? new Date(filters.startDate).toISOString()
          : undefined,
        endDate: filters.endDate
          ? new Date(
              new Date(filters.endDate).setHours(23, 59, 59)
            ).toISOString()
          : undefined,
        type: filters.type !== 'ALL' ? filters.type : undefined,
        reason: filters.reason,
        batchNumber: filters.batchNumber,
        productId,
      });
    },
    staleTime: 1000 * 60,
    refetchOnWindowFocus: false,
  });

  const { products, isLoading: isLoadingProducts } = useProducts({
    enabled: isDialogOpen,
    getAllProducts: true,
  });

  const createEntryMutation = useMutation({
    mutationFn: stockService.createStockEntry,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Entrada registrada',
        description: 'Entrada de stock registrada exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error ||
          'Error al registrar la entrada de stock',
        variant: 'destructive',
      });
    },
  });

  const deleteMovementMutation = useMutation({
    mutationFn: stockService.deleteStockMovement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Movimiento eliminado',
        description: 'El movimiento ha sido eliminado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al eliminar el movimiento',
        variant: 'destructive',
      });
    },
  });

  const updateMovementMutation = useMutation({
    mutationFn: ({ id, ...data }: { id: string } & StockMovementDto) =>
      stockService.updateStockMovement(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Movimiento actualizado',
        description: 'El movimiento ha sido actualizado exitosamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al actualizar el movimiento',
        variant: 'destructive',
      });
    },
  });

  return {
    movements: data?.entries || [],
    total: data?.total || 0,
    products,
    isLoadingProducts,
    isLoading,
    isCreating: createEntryMutation.isPending,
    isDeleting: deleteMovementMutation.isPending,
    isUpdating: updateMovementMutation.isPending,
    filters: {
      ...filters,
      ...handleFiltersChange,
    },
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((data?.total || 0) / limit),
      onPageChange: setPage,
    },
    createEntry: createEntryMutation.mutateAsync,
    deleteMovement: deleteMovementMutation.mutateAsync,
    updateMovement: updateMovementMutation.mutateAsync,
  };
}

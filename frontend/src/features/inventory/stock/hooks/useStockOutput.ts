import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/useToast';
import { useProducts } from '@/features/inventory/products/hooks/useProducts';
import {
  stockService,
  type StockMovement,
  type StockMovementFilters,
  type StockOutputDto,
} from '@/features/inventory/stock';

interface UseStockOutputOptions {
  productId?: string;
  initialFilters?: Partial<StockMovementFilters>;
  isDialogOpen?: boolean;
}

export function useStockOutput({
  productId,
  initialFilters = {},
  isDialogOpen = false,
}: UseStockOutputOptions = {}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<StockMovementFilters>({
    search: '',
    type: 'OUT',
    startDate: undefined,
    endDate: undefined,
    reason: undefined,
    batchNumber: undefined,
    ...initialFilters,
  });
  const [shouldApplyFilters, setShouldApplyFilters] = useState(false);
  const [page, setPage] = useState(1);
  const limit = 10;

  const handleFiltersChange = {
    onSearchChange: (search: string) =>
      setFilters((prev) => ({ ...prev, search })),
    onStartDateChange: (startDate?: Date) =>
      setFilters((prev) => ({ ...prev, startDate })),
    onEndDateChange: (endDate?: Date) =>
      setFilters((prev) => ({ ...prev, endDate })),
    onReasonChange: (reason?: string) =>
      setFilters((prev) => ({ ...prev, reason })),
    onBatchNumberChange: (batchNumber?: string) =>
      setFilters((prev) => ({ ...prev, batchNumber })),
    onApplyFilters: () => {
      setShouldApplyFilters(true);
      setPage(1);
    },
    onReset: () => {
      setFilters({
        search: '',
        type: 'OUT',
        startDate: undefined,
        endDate: undefined,
        reason: undefined,
        batchNumber: undefined,
      });
      setPage(1);
      setShouldApplyFilters(true);
    },
  };

  const { data, isLoading } = useQuery({
    queryKey: ['stock-movements', filters, page, limit, shouldApplyFilters],
    queryFn: () => {
      setShouldApplyFilters(false);
      return stockService.getStockMovements({
        page,
        limit,
        search: filters.search || undefined,
        startDate: filters.startDate
          ? new Date(filters.startDate).toISOString()
          : undefined,
        endDate: filters.endDate
          ? new Date(
              new Date(filters.endDate).setHours(23, 59, 59)
            ).toISOString()
          : undefined,
        type: 'OUT',
        reason: filters.reason,
        batchNumber: filters.batchNumber,
      });
    },
    staleTime: 1000 * 60,
    refetchOnWindowFocus: false,
  });

  const { products, isLoading: isLoadingProducts } = useProducts({
    enabled: isDialogOpen,
    getAllProducts: true,
  });

  const createOutputMutation = useMutation({
    mutationFn: (data: StockOutputDto) => stockService.createStockOutput(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Salida registrada',
        description: 'La salida de stock se ha registrado correctamente',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'No se pudo registrar la salida de stock',
        variant: 'destructive',
      });
    },
  });

  const deleteMovementMutation = useMutation({
    mutationFn: stockService.deleteStockMovement,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Movimiento eliminado',
        description: 'El movimiento se ha eliminado correctamente',
      });
    },
  });

  const editOutputMutation = useMutation({
    mutationFn: ({ _id, reference, observations }: Partial<StockMovement>) =>
      stockService.updateStockMovement(_id!, {
        ...(reference !== undefined && { reference }),
        ...(observations !== undefined && { observations }),
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stock-movements'] });
      toast({
        title: 'Movimiento actualizado',
        description: 'El movimiento se ha actualizado correctamente',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description:
          error.response?.data?.error || 'Error al actualizar el movimiento',
        variant: 'destructive',
      });
    },
  });

  return {
    movements: data?.entries || [],
    total: data?.total || 0,
    products,
    isLoadingProducts,
    isLoading,
    isCreating: createOutputMutation.isPending,
    isDeleting: deleteMovementMutation.isPending,
    isUpdating: editOutputMutation.isPending,
    filters: {
      ...filters,
      ...handleFiltersChange,
    },
    pagination: {
      currentPage: page,
      totalPages: Math.ceil((data?.total || 0) / limit),
      onPageChange: setPage,
    },
    createOutput: createOutputMutation.mutateAsync,
    deleteMovement: deleteMovementMutation.mutateAsync,
    editOutput: editOutputMutation.mutateAsync,
  };
}

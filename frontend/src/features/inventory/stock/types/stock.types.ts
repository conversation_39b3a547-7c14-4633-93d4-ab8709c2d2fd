import { z } from 'zod';
import type { createStockEntrySchema } from '@/features/inventory/stock';
import type { Product } from '@/features/inventory/products';

export type StockMovementType = 'IN' | 'OUT' | 'ADJUSTMENT' | 'ALL';

// Domain Types
export interface StockMovement {
  _id: string;
  productId: string;
  type: 'IN' | 'OUT' | 'ADJUSTMENT';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  batchNumber: string;
  expirationDate: string;
  cost?: number;
  reference?: string;
  observations?: string;
  createdAt: string;
  createdBy: {
    _id: string;
    username: string;
  };
  product?: {
    _id: string;
    name: string;
    sku: string;
    barcode?: string;
  };
}

// DTO Types
export interface StockMovementDto {
  _id: string;
  productId: string;
  type: StockMovementType;
  quantity: number;
  reason: string;
  reference?: string;
  notes?: string;
  observations?: string;
}

export interface StockEntryDto {
  productId: string;
  quantity: number;
  reason: string;
  reference?: string;
  batchNumber: string;
  expirationDate: string; // YYYY-MM-DD format
  cost: number;
  observations?: string;
}

export interface StockOutputDto {
  productId: string;
  quantity: number;
  reason: string;
  batchNumber: string;
  observations?: string;
}

export interface StockAdjustmentDto {
  productId: string;
  quantity: number;
  reason: string;
  observations?: string;
}

// API Response Types
export interface StockMovementsResponse {
  entries: StockMovement[];
  total: number;
  totalPages: number;
}

export interface StockMovementResponse {
  movement: StockMovement;
  product: Product;
  message: string;
}

export interface ExpiringProductsResponse {
  products: Product[];
  total: number;
  totalPages: number;
}

export interface LowStockProductsResponse {
  products: Product[];
  total: number;
  totalPages: number;
}

export type CreateStockMovementSchema = z.infer<typeof createStockEntrySchema>;

// Filter Types
export interface StockMovementFilters {
  search: string;
  type: StockMovementType;
  startDate?: Date;
  endDate?: Date;
  reason?: string;
  batchNumber?: string;
}

// UI Component Props Types
export interface StockEntriesFiltersProps {
  search: string;
  startDate?: Date;
  endDate?: Date;
  reason?: string;
  batchNumber?: string;
  onSearchChange: (value: string) => void;
  onStartDateChange: (date?: Date) => void;
  onEndDateChange: (date?: Date) => void;
  onReasonChange: (value?: string) => void;
  onBatchNumberChange: (value?: string) => void;
  onApplyFilters: () => void;
  onReset: () => void;
}

export interface StockMovementFiltersProps {
  search: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  type: StockMovementType | 'ALL';
  onSearchChange: (value: string) => void;
  onStartDateChange: (date: Date | undefined) => void;
  onEndDateChange: (date: Date | undefined) => void;
  onTypeChange: (value: StockMovementType | 'ALL') => void;
  onReset: () => void;
}

// Table Props Types
export interface StockMovementTableProps {
  entries: StockMovement[];
  isLoading?: boolean;
  permissions?: {
    canView?: boolean;
    canDelete?: boolean;
    canEdit?: boolean;
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  };
  onViewDetails: (movement: StockMovement) => void;
  onEdit: (movement: StockMovement) => void;
  onDelete: (movement: StockMovement) => void;
}

export interface StockEntriesTableProps extends StockMovementTableProps {}

export interface StockOutputsTableProps extends StockMovementTableProps {}

// Dialog Props Types
export interface ViewStockEntryDialogProps {
  entry: StockMovement | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface ViewStockOutputDialogProps {
  entry: StockMovement | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface CreateStockEntryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: StockEntryDto) => Promise<void>;
  isLoading?: boolean;
  products: Product[];
}

export interface CreateStockOutputDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: StockOutputDto) => Promise<unknown>;
  isLoading: boolean;
  products: Product[];
}

export interface CreateStockAdjustmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: StockAdjustmentDto) => Promise<void>;
  isLoading: boolean;
  products: Product[];
}

export interface UpdateStockMovementDto {
  _id: string;
  reference?: string;
  observations?: string;
}

export interface EditStockEntryDialogProps {
  entry: StockMovement | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: UpdateStockMovementDto) => Promise<void>;
  isLoading?: boolean;
}

export interface StockEntriesHeaderProps {
  title: string;
  description?: string;
  className?: string;
}

export interface StockOutputsHeaderProps {
  title: string;
  description?: string;
  className?: string;
}

// Stats Props Types
export interface StockStatsProps {
  totalEntries: number;
  totalOutputs: number;
  currentStock: number;
}

export interface StockEntriesTableColumnProps {
  permissions: {
    canEdit?: boolean;
    canDelete?: boolean;
  };
  onViewDetails: (movement: StockMovement) => void;
  onEdit: (movement: StockMovement) => void;
  onDelete: (movement: StockMovement) => void;
}

export interface StockMovementParams {
  page?: number;
  limit?: number;
  search?: string;
  startDate?: string;
  endDate?: string;
  type?: StockMovementType;
  reason?: string;
  batchNumber?: string;
  productId?: string;
}

export interface UseStockOptions {
  type?: 'IN' | 'OUT' | 'ADJUSTMENT' | 'ALL';
  productId?: string;
  initialFilters?: Partial<StockMovementFilters>;
  isDialogOpen?: boolean;
}

export interface DialogState {
  create: boolean;
  edit: boolean;
  details: boolean;
  delete: boolean;
}

export interface StockCreateOutputDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: StockOutputDto) => Promise<unknown>;
  isLoading?: boolean;
  products: Product[];
}

export interface DeleteStockEntryDialogProps {
  entry: StockMovement | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  isLoading: boolean;
}

export interface StockEntriesActionsProps {
  onCreate: (value: boolean) => void;
  canCreate: boolean;
}

export interface StockOutputsActionsProps {
  onCreate: (value: boolean) => void;
  canCreate: boolean;
}

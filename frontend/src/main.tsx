import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/toaster';
import './index.css';
import { reportWebVitals } from '@/lib/monitoring/performance';
import { App } from './App';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // How long data remains "fresh" before being considered stale (15 seconds)
      // During this time, React Query won't refetch the data when components mount or window refocuses
      staleTime: 15 * 1000,

      // How long unused data remains in cache before being garbage collected (10 minutes)
      // This helps avoid unnecessary refetches when navigating back to previously visited pages
      gcTime: 10 * 60 * 1000,

      // Number of retry attempts if a query fails
      retry: 2,

      // Whether to refetch data when the browser window regains focus
      // Set to false if you don't want queries to run when users return to your app
      refetchOnWindowFocus: true,

      // Whether to refetch data when a component using the query mounts
      // Set to false if you want to rely solely on cached data when components mount
      refetchOnMount: true,

      // Automatically refetch data at this interval (60 * 1000 = 60 seconds, or false)
      // Set to false to disable periodic refetching
      // This is causing the repeated API calls you're seeing in the console
      refetchInterval: false,
    },
  },
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute='class' defaultTheme='system' enableSystem>
        <App />
        <Toaster />
      </ThemeProvider>
    </QueryClientProvider>
  </React.StrictMode>
);

reportWebVitals((metric) => {
  // console.log(metric);
});

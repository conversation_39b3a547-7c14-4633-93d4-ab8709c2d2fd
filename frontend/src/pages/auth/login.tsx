import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore, LoginForm, AuthNavbar } from '@/features/auth';

export default function Login() {
  const { isAuthenticated } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect to the attempted route or the dashboard by default
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  return (
    <div className='min-h-screen bg-gray-100 dark:bg-gray-900'>
      <AuthNavbar />
      <main className='pt-16 min-h-[calc(100vh-4rem)] flex items-center justify-center p-4'>
        <LoginForm />
      </main>
    </div>
  );
}

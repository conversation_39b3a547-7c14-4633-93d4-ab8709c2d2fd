import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  CustomersHeader,
  CustomersFilters,
  CustomersActions,
  CustomersTable,
  CustomerCreateDialog,
  CustomerEditDialog,
  CustomerDetailsDialog,
  DeleteCustomerDialog,
  useCustomersPage,
  useCustomerPermissions,
} from '@/features/customers';

export default function CustomersPage() {
  const { permissions } = useCustomerPermissions();
  const {
    customers,
    isLoading,
    isEmpty,
    selectedCustomer,
    filters,
    pagination,
    actions,
    dialog,
  } = useCustomersPage();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  return (
    <div className='space-y-4 p-8 pt-6'>
      <CustomersHeader
        title='Clientes'
        description='Gestiona los clientes del sistema'
      />

      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-[70%]'>
              <CustomersFilters {...filters} />
            </div>
            <div className='w-full lg:w-[30%] flex justify-end items-center'>
              <CustomersActions
                {...actions}
                canCreate={permissions.canCreate}
              />
            </div>
          </div>
        </div>

        {isEmpty ? (
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <p className='text-muted-foreground mb-4'>
              No se encontraron clientes
            </p>
          </div>
        ) : (
          <CustomersTable
            customers={customers}
            isLoading={isLoading}
            pagination={pagination}
            permissions={permissions}
            onEdit={actions.onEdit}
            onDelete={actions.onDelete}
            onViewDetails={actions.onViewDetails}
          />
        )}
      </div>

      <CustomerCreateDialog {...dialog.create} />

      <CustomerEditDialog {...dialog.edit} customer={selectedCustomer} />

      <DeleteCustomerDialog {...dialog.delete} customer={selectedCustomer} />

      <CustomerDetailsDialog {...dialog.details} customer={selectedCustomer} />
    </div>
  );
}

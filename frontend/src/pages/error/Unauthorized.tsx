import { Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

export default function UnauthorizedPage() {
  const navigate = useNavigate();

  return (
    <div className='h-screen w-full flex flex-col items-center justify-center bg-background'>
      <div className='text-center space-y-6'>
        <Shield className='h-16 w-16 text-destructive mx-auto' />
        <h1 className='text-3xl font-bold'>Acceso No Autorizado</h1>
        <p className='text-muted-foreground max-w-md'>
          No tienes los permisos necesarios para acceder a esta página.
        </p>
        <div className='space-x-4'>
          <Button onClick={() => navigate('/')}>Ir al Inicio</Button>
        </div>
      </div>
    </div>
  );
}

import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { ArrowLeft, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      '404 Error: User attempted to access non-existent route:',
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className='min-h-screen bg-background p-6'>
      <div className='max-w-7xl mx-auto'>
        <button
          onClick={() => navigate(-1)}
          className='mb-6 flex items-center text-muted-foreground hover:text-foreground'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Volver
        </button>

        <div className='text-center mb-12'>
          <h1 className='text-4xl font-bold mb-4'>
            404 - Página no encontrada
          </h1>
          <p className='text-xl text-muted-foreground mb-8'>
            La página que buscas no existe.
          </p>

          <div className='flex justify-center gap-4'>
            <Button
              onClick={() => navigate('/')}
              className='flex items-center gap-2'
            >
              <Home className='w-4 h-4' />
              Ir al Inicio
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;

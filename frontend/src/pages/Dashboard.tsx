import { Card } from '@/components/ui/card';

const Dashboard = () => {
  const mockData = {
    statCards: [
      {
        title: 'Ventas de Hoy',
        value: '$1,234.56',
        trend: '+12.3%',
        isPositive: true,
      },
      {
        title: 'Productos en Stock Bajo',
        value: '23',
        trend: '-5',
        isPositive: false,
      },
      {
        title: 'Balance de Caja',
        value: '$5,678.90',
        trend: '+$890.12',
        isPositive: true,
      },
      {
        title: 'Productos por Caducar',
        value: '15',
        trend: '+3',
        isPositive: false,
      },
    ],
  };

  return (
    <div className='p-6'>
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-between mb-8'>
          <div>
            <h1 className='text-2xl font-bold'>Dashboard</h1>
            <p className='text-muted-foreground mt-1'>
              Bienvenido al panel de control
            </p>
          </div>
        </div>

        {/* <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          {mockData.statCards.map((stat, index) => (
            <Card key={index} className='p-6 hover:shadow-lg transition-shadow'>
              <h3 className='text-sm font-medium text-muted-foreground'>
                {stat.title}
              </h3>
              <div className='mt-2 flex items-baseline justify-between'>
                <p className='text-2xl font-semibold'>{stat.value}</p>
                <span
                  className={`text-sm font-medium ${
                    stat.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stat.trend}
                </span>
              </div>
            </Card>
          ))}
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          <Card className='p-6'>
            <h3 className='text-lg font-medium mb-4'>Ventas Recientes</h3>
            <div className='space-y-4'>
              {[1, 2, 3].map((i) => (
                <div key={i} className='flex items-center justify-between py-2'>
                  <div>
                    <p className='font-medium'>Venta #{i}</p>
                    <p className='text-sm text-muted-foreground'>
                      Hace {i * 5} minutos
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div> */}
      </div>
    </div>
  );
};

export default Dashboard;

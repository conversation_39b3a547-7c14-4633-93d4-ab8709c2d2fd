import { useState } from 'react';
import { ChevronLeft, ChevronRight, FileDown, Printer } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { InventoryReportFilters } from '@/features/reports/components/InventoryReportFilters';
import { inventoryReportColumns } from '@/features/reports/components/InventoryReportColumns';
import type { InventoryReportProduct } from '@/features/reports/types/report.types';
import { useInventoryReport } from '@/features/reports/hooks/useInventoryReport';
import { useReportPermissions } from '@/features/reports/hooks/useReportPermissions';

export default function InventoryReportPage() {
  const { permissions } = useReportPermissions();
  const {
    data,
    isLoading,
    filters,
    handleFiltersChange,
    resetFilters, // Changed from handleReset
    exportToExcel,
    generatePDF,
    isExporting,
  } = useInventoryReport();

  // Paginación local
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Cálculos para la paginación
  const totalItems = data?.products?.length ?? 0;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentItems = data?.products?.slice(startIndex, endIndex) ?? [];

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newSize: string) => {
    setPageSize(Number(newSize));
    setCurrentPage(1); // Reset a primera página cuando cambia el tamaño
  };

  if (!permissions.canViewInventory) {
    return <RestrictedAccess />;
  }

  return (
    <main className='space-y-4 p-8 pt-6'>
      <header className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            Reporte de Inventario
          </h2>
          <p className='text-muted-foreground'>
            Analiza y exporta reportes detallados del inventario
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            onClick={generatePDF}
            disabled={isExporting || isLoading}
          >
            {isExporting ? (
              <LoadingSpinner className='mr-2' />
            ) : (
              <Printer className='h-4 w-4 mr-2' />
            )}
            PDF
          </Button>
          <Button
            variant='outline'
            onClick={exportToExcel}
            disabled={isExporting || isLoading}
          >
            {isExporting ? (
              <LoadingSpinner className='mr-2' />
            ) : (
              <FileDown className='h-4 w-4 mr-2' />
            )}
            Excel
          </Button>
        </div>
      </header>

      <div className='space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <InventoryReportFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onReset={resetFilters} // Changed from handleReset
          />
        </div>

        {isLoading ? (
          <div className='flex justify-center items-center py-8'>
            <LoadingSpinner size='lg' />
          </div>
        ) : (
          <>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='bg-background/95 p-4 rounded-lg border'>
                <h3 className='font-medium text-sm text-muted-foreground'>
                  Productos en Stock
                </h3>
                <p className='text-2xl font-bold mt-1'>
                  {data?.summary?.totalProducts ?? 0}
                </p>
              </div>
              <div className='bg-background/95 p-4 rounded-lg border'>
                <h3 className='font-medium text-sm text-muted-foreground'>
                  Productos Bajo Mínimo
                </h3>
                <p className='text-2xl font-bold mt-1 text-destructive'>
                  {data?.summary?.lowStockProducts ?? 0}
                </p>
              </div>
            </div>

            <div className='rounded-md border'>
              <div className='relative w-full'>
                {/* Tabla de encabezado fija */}
                <div className='sticky top-0 z-10 w-full bg-background border-b'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className='w-[350px]'>Producto</TableHead>
                        <TableHead className='w-[150px]'>Categoría</TableHead>
                        <TableHead className='w-[150px]'>Laboratorio</TableHead>
                        <TableHead className='w-[120px]'>Stock</TableHead>
                        <TableHead className='w-[100px] text-right'>
                          Costo
                        </TableHead>
                        <TableHead className='w-[100px] text-right'>
                          Precio
                        </TableHead>
                        <TableHead className='w-[120px] text-right'>
                          Valor Total
                        </TableHead>
                        <TableHead className='w-[120px]'>Estado</TableHead>
                        <TableHead className='w-[100px] text-right'>
                          Acciones
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                  </Table>
                </div>

                {/* Contenido de la tabla con scroll */}
                <div className='max-h-[600px] overflow-auto'>
                  <Table>
                    <TableBody>
                      {currentItems.map((product: InventoryReportProduct) => (
                        <TableRow key={product._id}>
                          <TableCell className='w-[350px]'>
                            {inventoryReportColumns[0].cell(product)}
                          </TableCell>
                          <TableCell className='w-[150px]'>
                            {inventoryReportColumns[1].cell(product)}
                          </TableCell>
                          <TableCell className='w-[150px]'>
                            {inventoryReportColumns[2].cell(product)}
                          </TableCell>
                          <TableCell className='w-[120px]'>
                            {inventoryReportColumns[3].cell(product)}
                          </TableCell>
                          <TableCell className='w-[100px] text-right'>
                            {inventoryReportColumns[4].cell(product)}
                          </TableCell>
                          <TableCell className='w-[100px] text-right'>
                            {inventoryReportColumns[5].cell(product)}
                          </TableCell>
                          <TableCell className='w-[120px] text-right'>
                            {inventoryReportColumns[6].cell(product)}
                          </TableCell>
                          <TableCell className='w-[120px]'>
                            {inventoryReportColumns[7].cell(product)}
                          </TableCell>
                          <TableCell className='w-[100px] text-right'>
                            {inventoryReportColumns[8].cell(product)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Paginación */}
                <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 border-t'>
                  <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                    <Select
                      value={pageSize.toString()}
                      onValueChange={handlePageSizeChange}
                    >
                      <SelectTrigger className='h-8 w-[70px]'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[10, 20, 30, 50, 100].map((size) => (
                          <SelectItem key={size} value={size.toString()}>
                            {size}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <span>registros por página</span>
                  </div>

                  <div className='flex items-center justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className='h-4 w-4' />
                      <span className='sr-only'>Página anterior</span>
                    </Button>

                    <div className='text-sm whitespace-nowrap'>
                      Página {currentPage} de {totalPages}
                    </div>

                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      <ChevronRight className='h-4 w-4' />
                      <span className='sr-only'>Página siguiente</span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </main>
  );
}

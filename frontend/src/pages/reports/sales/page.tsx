import { useState } from 'react';
import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import { useReportPermissions } from '@/features/reports/hooks/useReportPermissions';
import { SalesReportFilters } from '@/features/reports/components/SalesReportFilters';
import { useSalesReport } from '@/features/reports/hooks/useSalesReport';
import { Button } from '@/components/ui/button';
import {
  FileDown,
  Printer,
  ChevronLeft,
  ChevronRight,
  Eye,
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatCurrency, formatDate } from '@/lib/utils/format';
import {
  SALE_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
} from '@/features/sales/constants/sale.constants';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { SaleDetailsDialog } from '@/features/sales/components/SaleDetailsDialog';
import { Sale } from '@/features/sales';
import { memo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Receipt, Calendar, User2, CreditCard } from 'lucide-react';

const SaleNumber = memo(({ number }: { number: string }) => (
  <div className='flex items-center gap-2'>
    <Receipt className='w-4 h-4 text-primary' />
    <span className='font-medium'>{number}</span>
  </div>
));

const SaleDate = memo(({ date }: { date: string }) => (
  <div className='flex items-center gap-2'>
    <Calendar className='w-4 h-4 text-muted-foreground' />
    <span>{formatDate(date)}</span>
  </div>
));

const CustomerInfo = memo(({ customer }: { customer?: Sale['customer'] }) => (
  <div className='flex items-center gap-2'>
    <User2 className='w-4 h-4 text-muted-foreground' />
    <div className='flex flex-col'>
      <span className='font-medium'>
        {customer?.businessName || 'CONSUMIDOR FINAL'}
      </span>
      {customer && (
        <span className='text-xs text-muted-foreground'>
          {customer.documentType} {customer.documentNumber}
        </span>
      )}
    </div>
  </div>
));

const PaymentInfo = memo(
  ({
    paymentMethod,
    total,
    subtotal,
    discount,
  }: {
    paymentMethod: string;
    total: number;
    subtotal: number;
    discount: number;
  }) => (
    <div className='flex items-center gap-3'>
      <CreditCard className='w-4 h-4 text-muted-foreground' />
      <div className='flex flex-col gap-1'>
        <Badge variant='outline' className='w-fit'>
          {PAYMENT_METHOD_LABELS[paymentMethod]}
        </Badge>
        <div className='flex flex-col text-xs text-muted-foreground'>
          <span>Subtotal: {formatCurrency(subtotal)}</span>
          <span>Descuento: {formatCurrency(discount)}</span>
          <span className='font-medium text-primary mt-1'>
            Total: {formatCurrency(total)}
          </span>
        </div>
      </div>
    </div>
  )
);

const StatusBadge = memo(({ status }: { status: Sale['status'] }) => (
  <Badge
    variant={status === 'CANCELED' ? 'destructive' : 'default'}
    className='w-fit'
  >
    {SALE_STATUS_LABELS[status]}
  </Badge>
));

export default function SalesReportPage() {
  const { permissions } = useReportPermissions();
  const {
    data,
    isLoading,
    filters,
    setFilters,
    resetFilters,
    summary,
    exportToExcel,
    generatePDF,
    isExporting,
  } = useSalesReport();

  // Paginación local
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Cálculos para la paginación
  const totalItems = data.sales.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentItems = data.sales.slice(startIndex, endIndex);

  // Handlers para la paginación
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handlePageSizeChange = (newSize: string) => {
    setPageSize(Number(newSize));
    setCurrentPage(1); // Reset a primera página cuando cambia el tamaño
  };

  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  const handleViewDetails = (sale: Sale) => {
    setSelectedSale(sale);
    setShowDetailsDialog(true);
  };

  if (!permissions.canViewSales) {
    return <RestrictedAccess />;
  }

  return (
    <main className='space-y-4 p-8 pt-6'>
      <header className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>
            Reporte de Ventas
          </h2>
          <p className='text-muted-foreground'>
            Analiza y exporta reportes detallados de ventas
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            onClick={generatePDF}
            disabled={isExporting || isLoading}
          >
            {isExporting ? (
              <LoadingSpinner className='mr-2' />
            ) : (
              <Printer className='h-4 w-4 mr-2' />
            )}
            PDF
          </Button>
          <Button
            variant='outline'
            onClick={exportToExcel}
            disabled={isExporting || isLoading}
          >
            {isExporting ? (
              <LoadingSpinner className='mr-2' />
            ) : (
              <FileDown className='h-4 w-4 mr-2' />
            )}
            Excel
          </Button>
        </div>
      </header>

      <div className='space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <SalesReportFilters
            filters={{
              search: filters.search,
              status: filters.status,
              customerId: filters.customerId,
              startDate: filters.startDate,
              endDate: filters.endDate,
            }}
            onFiltersChange={setFilters}
            onReset={resetFilters}
          />
        </div>

        {isLoading ? (
          <div className='flex justify-center items-center py-8'>
            <LoadingSpinner size='lg' />
          </div>
        ) : (
          <>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='bg-background/95 p-4 rounded-lg border'>
                <h3 className='font-medium text-sm text-muted-foreground'>
                  Total Ventas
                </h3>
                <p className='text-2xl font-bold mt-1'>
                  {formatCurrency(summary.totalAmount)}
                </p>
              </div>
              <div className='bg-background/95 p-4 rounded-lg border'>
                <h3 className='font-medium text-sm text-muted-foreground'>
                  Cantidad de Ventas
                </h3>
                <p className='text-2xl font-bold mt-1'>{summary.totalSales}</p>
              </div>
              <div className='bg-background/95 p-4 rounded-lg border'>
                <h3 className='font-medium text-sm text-muted-foreground'>
                  Estado de Ventas
                </h3>
                <div className='mt-1'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Completadas:
                    </span>
                    <p className='text-lg font-semibold text-green-600'>
                      {summary.completedSales}
                    </p>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm text-muted-foreground'>
                      Canceladas:
                    </span>
                    <p className='text-lg font-semibold text-red-600'>
                      {summary.canceledSales}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className='rounded-md border'>
              <ScrollArea className='h-[600px]'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Número</TableHead>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Pago</TableHead>
                      <TableHead className='text-right'>Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentItems.map((sale) => (
                      <TableRow key={sale._id}>
                        <TableCell>
                          <SaleNumber number={sale.number} />
                        </TableCell>
                        <TableCell>
                          <SaleDate date={sale.date} />
                        </TableCell>
                        <TableCell>
                          <CustomerInfo customer={sale.customer} />
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={sale.status} />
                        </TableCell>
                        <TableCell>
                          <PaymentInfo
                            paymentMethod={sale.paymentMethod}
                            total={sale.total}
                            subtotal={sale.subtotal}
                            discount={sale.discount}
                          />
                        </TableCell>
                        <TableCell className='text-right'>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant='ghost'
                                  size='icon'
                                  onClick={() => handleViewDetails(sale)}
                                  className='text-muted-foreground hover:text-primary hover:bg-primary/10'
                                >
                                  <Eye className='h-4 w-4' />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Ver detalles de la venta</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>

              {/* Paginación */}
              <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 border-t'>
                <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={handlePageSizeChange}
                  >
                    <SelectTrigger className='h-8 w-[70px]'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[10, 20, 30, 50, 100].map((size) => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <span>registros por página</span>
                </div>

                <div className='flex items-center justify-end gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className='h-4 w-4' />
                    <span className='sr-only'>Página anterior</span>
                  </Button>

                  <div className='text-sm whitespace-nowrap'>
                    Página {currentPage} de {totalPages}
                  </div>

                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    <ChevronRight className='h-4 w-4' />
                    <span className='sr-only'>Página siguiente</span>
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      <SaleDetailsDialog
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
        sale={selectedSale}
        hideActions={true}
        permissions={{}}
      />
    </main>
  );
}

import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { GeneralSettingsForm } from '@/features/settings/components/forms/GeneralSettingsForm';
import { useGeneralSettings } from '@/features/settings/hooks/useGeneralSettings';
import { useSettingsStore } from '@/features/settings/store/settings.store';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import type { GeneralSettingsFormValues } from '@/features/settings/schemas/settings.schema';

export default function GeneralSettingsPage() {
  const navigate = useNavigate();
  const { settings, isLoading, updateSettings, isUpdating } =
    useGeneralSettings();
  const { generalSettings } = useSettingsStore();

  const handleSubmit = async (data: GeneralSettingsFormValues) => {
    const formData = new FormData();

    // Si hay un nuevo archivo
    if (data.logo instanceof File) {
      formData.append('logo', data.logo);
    }

    const settings = {
      ...data,
      logo: data.logo instanceof File ? undefined : data.logo,
    };
    formData.append('settings', JSON.stringify(settings));

    await updateSettings(formData);
  };

  if (isLoading) {
    return (
      <div className='flex h-full items-center justify-center'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return (
    <div className='p-4 md:p-6 space-y-6'>
      <div className='flex items-center space-x-2 md:space-x-4'>
        <Button variant='outline' size='icon' onClick={() => navigate(-1)}>
          <ArrowLeft className='h-4 w-4' />
        </Button>
        <h1 className='text-xl md:text-2xl font-bold'>Configuración General</h1>
      </div>

      <GeneralSettingsForm
        initialData={generalSettings || settings}
        onSubmit={handleSubmit}
        isLoading={isUpdating}
      />
    </div>
  );
}

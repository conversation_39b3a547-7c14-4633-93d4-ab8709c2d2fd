import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  LaboratoriesHeader,
  LaboratoriesFilters,
  LaboratoriesActions,
  LaboratoriesTable,
  useLaboratoriesPage,
  LaboratoryCreateDialog,
  DeleteLaboratoryDialog,
  LaboratoryEditDialog,
  LaboratoryStatusDialog,
  useLaboratoryPermissions,
} from '@/features/inventory/laboratories';

export default function LaboratoriesPage() {
  const { permissions } = useLaboratoryPermissions();
  const {
    laboratories,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    editDialog,
    statusDialog,
  } = useLaboratoriesPage();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  return (
    <div className='space-y-4 p-8 pt-6'>
      <LaboratoriesHeader
        title='Laboratorios'
        description='Gestiona los laboratorios farmacéuticos del sistema'
      />
      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <LaboratoriesFilters
                search={filters.search}
                onSearchChange={filters.setSearch}
                onReset={filters.reset}
              />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <LaboratoriesActions
                onCreate={actions.handlers.onCreate}
                canCreate={permissions.canCreate}
              />
            </div>
          </div>
        </div>

        {laboratories.length === 0 && !isLoading ? (
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <p className='text-muted-foreground mb-4'>
              No se encontraron laboratorios
            </p>
          </div>
        ) : (
          <LaboratoriesTable
            laboratories={laboratories}
            isLoading={isLoading}
            pagination={pagination}
            permissions={permissions}
            onEdit={actions.handlers.onEdit}
            onDelete={actions.handlers.onDelete}
            onToggleStatus={actions.handlers.onToggleStatus}
          />
        )}
      </div>

      {permissions.canCreate && (
        <LaboratoryCreateDialog
          open={dialog.isOpen}
          onOpenChange={dialog.onOpenChange}
          onSubmit={dialog.onSubmit}
          isLoading={dialog.isLoading}
        />
      )}

      {permissions.canDelete && (
        <DeleteLaboratoryDialog
          laboratory={deleteDialog.laboratory}
          open={deleteDialog.isOpen}
          onOpenChange={deleteDialog.onOpenChange}
          onConfirm={deleteDialog.onConfirm}
          isLoading={deleteDialog.isLoading}
        />
      )}

      {permissions.canEdit && (
        <>
          <LaboratoryEditDialog
            laboratory={editDialog.laboratory}
            isOpen={editDialog.isOpen}
            onOpenChange={editDialog.onOpenChange}
            onUpdate={editDialog.onUpdate}
            isLoading={editDialog.isLoading}
          />

          <LaboratoryStatusDialog
            laboratory={statusDialog.laboratory}
            open={statusDialog.isOpen}
            onOpenChange={statusDialog.onOpenChange}
            onConfirm={statusDialog.onConfirm}
            isLoading={statusDialog.isLoading}
          />
        </>
      )}
    </div>
  );
}

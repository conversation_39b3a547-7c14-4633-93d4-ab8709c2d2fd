import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  CategoriesHeader,
  CategoriesFilters,
  CategoriesActions,
  CategoriesTable,
  useCategoriesPage,
  CategoryCreateDialog,
  DeleteCategoryDialog,
  CategoryEditDialog,
  CategoryStatusDialog,
  useCategoryPermissions,
} from '@/features/inventory/categories';

export default function CategoriesPage() {
  const { permissions } = useCategoryPermissions();
  const {
    categories,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    editDialog,
    statusDialog,
  } = useCategoriesPage();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  return (
    <div className='space-y-4 p-8 pt-6'>
      <CategoriesHeader
        title='Categorias'
        description='Gestiona los categorías de productos del sistema'
      />
      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <CategoriesFilters
                search={filters.search}
                onSearchChange={filters.setSearch}
                onReset={filters.reset}
              />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <CategoriesActions
                onCreate={actions.handlers.onCreate}
                canCreate={permissions.canCreate}
              />
            </div>
          </div>
        </div>

        {categories.length === 0 && !isLoading ? (
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <p className='text-muted-foreground mb-4'>
              No se encontraron categorías
            </p>
          </div>
        ) : (
          <CategoriesTable
            categories={categories}
            isLoading={isLoading}
            pagination={pagination}
            permissions={permissions}
            onEdit={actions.handlers.onEdit}
            onDelete={actions.handlers.onDelete}
            onToggleStatus={actions.handlers.onToggleStatus}
          />
        )}
      </div>

      <CategoryCreateDialog
        open={dialog.isOpen}
        onOpenChange={dialog.onOpenChange}
        onSubmit={dialog.onSubmit}
        isLoading={dialog.isLoading}
      />

      <DeleteCategoryDialog
        category={deleteDialog.category}
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
      />

      <CategoryEditDialog
        category={editDialog.category}
        isOpen={editDialog.isOpen}
        onOpenChange={editDialog.onOpenChange}
        onUpdate={editDialog.onUpdate}
        isLoading={editDialog.isLoading}
      />

      <CategoryStatusDialog
        category={statusDialog.category}
        open={statusDialog.isOpen}
        onOpenChange={statusDialog.onOpenChange}
        onConfirm={statusDialog.onConfirm}
        isLoading={statusDialog.isLoading}
      />
    </div>
  );
}

import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  StockOutputsHeader,
  StockOutputsFilters,
  StockOutputsActions,
  StockOutputsTable,
  useStockOutputPage,
  StockCreateOutputDialog,
  DeleteStockOutputDialog,
  StockEditOutputDialog,
  StockDetailsOutputDialog,
  useStockPermissions,
} from '@/features/inventory/stock';

export default function StockOutputsPage() {
  const { permissions } = useStockPermissions();
  const {
    outputs,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    editDialog,
    detailsDialog,
  } = useStockOutputPage();

  if (!permissions.outputs.canList) {
    return <RestrictedAccess />;
  }

  return (
    <div className='space-y-4'>
      <StockOutputsHeader
        title='Salidas de Stock'
        description='Gestiona las salidas de productos del inventario'
      />
      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <StockOutputsFilters
                search={filters.search}
                startDate={filters.startDate}
                endDate={filters.endDate}
                reason={filters.reason}
                batchNumber={filters.batchNumber}
                onSearchChange={filters.onSearchChange}
                onStartDateChange={filters.onStartDateChange}
                onEndDateChange={filters.onEndDateChange}
                onReasonChange={filters.onReasonChange}
                onBatchNumberChange={filters.onBatchNumberChange}
                onApplyFilters={filters.onApplyFilters}
                onReset={filters.onReset}
              />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <StockOutputsActions
                onCreate={actions.handlers.onCreate}
                canCreate={permissions.outputs.canCreate}
              />
            </div>
          </div>
        </div>

        <StockOutputsTable
          entries={outputs}
          isLoading={isLoading}
          pagination={pagination}
          permissions={permissions.outputs}
          onViewDetails={actions.handlers.onViewDetails}
          onEdit={actions.handlers.onEdit}
          onDelete={actions.handlers.onDelete}
        />
      </div>

      <StockCreateOutputDialog
        open={dialog.isOpen}
        onOpenChange={dialog.onOpenChange}
        onSubmit={dialog.onSubmit}
        isLoading={dialog.isLoading}
        products={dialog.products}
      />

      <DeleteStockOutputDialog
        entry={deleteDialog.entry}
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
      />

      <StockEditOutputDialog
        entry={editDialog.entry}
        open={editDialog.isOpen}
        onOpenChange={editDialog.onOpenChange}
        onSubmit={editDialog.onSubmit}
        isLoading={editDialog.isLoading}
      />

      <StockDetailsOutputDialog
        entry={detailsDialog.entry}
        open={detailsDialog.isOpen}
        onOpenChange={detailsDialog.onOpenChange}
      />
    </div>
  );
}

import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  StockEntriesHeader,
  StockEntriesFilters,
  StockEntriesActions,
  StockEntriesTable,
  useStockEntryPage,
  CreateStockEntryDialog,
  DeleteStockEntryDialog,
  EditStockEntryDialog,
  StockDetailsEntryDialog,
  useStockPermissions,
  type StockEntryDto,
} from '@/features/inventory/stock';

export default function StockEntriesPage() {
  const { permissions } = useStockPermissions();
  const {
    entries,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    editDialog,
    detailsDialog,
  } = useStockEntryPage();

  const handleSubmit = async (data: StockEntryDto) => {
    await dialog.onSubmit(data);
  };

  if (!permissions.entries.canList) {
    return <RestrictedAccess />;
  }

  return (
    <div className='space-y-4'>
      <StockEntriesHeader
        title='Entradas de Stock'
        description='Gestiona las entradas de productos al inventario'
      />
      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <StockEntriesFilters
                search={filters.search}
                startDate={filters.startDate}
                endDate={filters.endDate}
                reason={filters.reason}
                batchNumber={filters.batchNumber}
                onSearchChange={filters.onSearchChange}
                onStartDateChange={filters.onStartDateChange}
                onEndDateChange={filters.onEndDateChange}
                onReasonChange={filters.onReasonChange}
                onBatchNumberChange={filters.onBatchNumberChange}
                onApplyFilters={filters.onApplyFilters}
                onReset={filters.onReset}
              />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <StockEntriesActions
                onCreate={actions.handlers.onCreate}
                canCreate={permissions.entries.canCreate}
              />
            </div>
          </div>
        </div>

        <StockEntriesTable
          entries={entries}
          isLoading={isLoading}
          pagination={pagination}
          permissions={permissions.entries}
          onViewDetails={actions.handlers.onViewDetails}
          onEdit={actions.handlers.onEdit}
          onDelete={actions.handlers.onDelete}
        />
      </div>

      <CreateStockEntryDialog
        open={dialog.isOpen}
        onOpenChange={dialog.onOpenChange}
        onSubmit={handleSubmit}
        isLoading={dialog.isLoading}
        products={dialog.products}
      />

      <DeleteStockEntryDialog
        entry={deleteDialog.entry}
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
      />

      <EditStockEntryDialog
        entry={editDialog.entry}
        open={editDialog.isOpen}
        onOpenChange={editDialog.onOpenChange}
        onSubmit={editDialog.onSubmit}
        isLoading={editDialog.isLoading}
      />

      <StockDetailsEntryDialog
        entry={detailsDialog.entry}
        open={detailsDialog.isOpen}
        onOpenChange={detailsDialog.onOpenChange}
      />
    </div>
  );
}

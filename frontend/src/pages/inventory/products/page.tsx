import {
  ProductsHeader,
  ProductsFilters,
  ProductsActions,
  ProductsTable,
  useProductsPage,
  useProductPermissions,
  ProductCreateDialog,
  DeleteProductDialog,
  ProductDetailsDialog,
  ProductEditDialog,
  ProductTicketDialog,
} from '@/features/inventory/products';
import { useCategories } from '@/features/inventory/categories';
import { useLaboratories } from '@/features/inventory/laboratories';

export default function ProductsPage() {
  const { permissions } = useProductPermissions();
  const { categories } = useCategories();
  const { laboratories } = useLaboratories();
  const {
    products,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    detailsDialog,
    editDialog,
    ticketDialog,
  } = useProductsPage();

  if (!permissions.canList) {
    return (
      <div className='flex h-[50vh] items-center justify-center'>
        <div className='text-center'>
          <h2 className='text-2xl font-semibold'>Acceso Restringido</h2>
          <p className='text-muted-foreground mt-2'>
            No tienes permisos para ver esta sección
          </p>
        </div>
      </div>
    );
  }

  const filtersProps = {
    search: filters.search,
    selectedCategory: filters.category,
    selectedLaboratory: filters.laboratory,
    showInactive: filters.showInactive,
    onSearchChange: filters.onSearchChange,
    onCategoryChange: filters.onCategoryChange,
    onLaboratoryChange: filters.onLaboratoryChange,
    onActiveStatusChange: filters.onActiveStatusChange,
    onReset: filters.reset,
  };

  const actionsProps = {
    canCreate: permissions.canCreate,
    onCreate: actions.handlers.onCreate,
  };

  const showEmptyState =
    !isLoading && Array.isArray(products) && products.length === 0;

  return (
    <div className='space-y-6 p-4'>
      <ProductsHeader
        title='Productos'
        description='Gestiona los productos del sistema'
      />

      <div className='flex flex-col space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <ProductsFilters {...filtersProps} />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <ProductsActions {...actionsProps} />
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className='flex justify-center items-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900'></div>
          </div>
        ) : showEmptyState ? (
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <p className='text-muted-foreground mb-4'>
              No se encontraron productos
            </p>
          </div>
        ) : (
          <ProductsTable
            products={products}
            isLoading={isLoading}
            pagination={pagination}
            permissions={permissions}
            onEdit={actions.handlers.onEdit}
            onDelete={actions.handlers.onDelete}
            onViewDetails={actions.handlers.onViewDetails}
            onPrintTicket={actions.handlers.onPrintTicket}
          />
        )}
      </div>

      <ProductCreateDialog
        open={dialog.isOpen}
        onOpenChange={dialog.onOpenChange}
        onSubmit={dialog.onSubmit}
        isLoading={dialog.isLoading}
        categories={categories}
        laboratories={laboratories}
      />

      <DeleteProductDialog
        product={deleteDialog.product}
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
      />

      <ProductDetailsDialog
        product={detailsDialog.product}
        open={detailsDialog.isOpen}
        onOpenChange={detailsDialog.onOpenChange}
      />

      <ProductEditDialog
        product={editDialog.product}
        open={editDialog.isOpen}
        onOpenChange={editDialog.onOpenChange}
        onUpdate={editDialog.onUpdate}
        isLoading={editDialog.isLoading}
        categories={categories}
        laboratories={laboratories}
      />

      <ProductTicketDialog
        product={ticketDialog.product}
        isOpen={ticketDialog.isOpen}
        onOpenChange={ticketDialog.onOpenChange}
        ticketType={ticketDialog.ticketType}
        onTicketTypeChange={ticketDialog.onTicketTypeChange}
      />
    </div>
  );
}

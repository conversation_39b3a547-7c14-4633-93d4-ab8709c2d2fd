import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import { LoadingScreen } from '@/components/ui/loading-screen';
import {
  CashRegisterHeader,
  CashRegisterHistoryTable,
  CashRegisterDeleteDialog,
  useCashHistory,
  useCashPermissions,
  CashRegisterHistoryFilters,
} from '@/features/cash';

export default function CashHistoryPage() {
  const { permissions } = useCashPermissions();
  const { registers, isLoading, filters, pagination, actions, deleteDialog } =
    useCashHistory();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className='space-y-4 p-8 pt-6'>
      <CashRegisterHeader
        title='Historial de Cajas'
        description='Consulta el historial de aperturas y cierres de caja'
        onOpenRegister={() => {}}
        onCloseRegister={() => {}}
        canManage={false}
        isRegisterOpen={false}
      />

      <div className='rounded-md border'>
        <CashRegisterHistoryFilters {...filters} />
        <CashRegisterHistoryTable
          registers={registers}
          isLoading={isLoading}
          pagination={pagination}
          permissions={permissions}
          onViewDetails={actions.viewDetails}
          onDeleteRegister={
            permissions.canManage ? actions.deleteRegister : undefined
          }
        />
      </div>

      <CashRegisterDeleteDialog
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
        register={deleteDialog.register}
      />
    </div>
  );
}

import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import { LoadingScreen } from '@/components/ui/loading-screen';
import {
  CashRegisterHeader,
  CashRegisterOverview,
  CashRegisterOpenDialog,
  CashRegisterCloseDialog,
  CashMovementCreateDialog,
  CashMovementEditDialog,
  CashMovementDeleteDialog,
  CashRegisterCloseReceipt,
  useCurrentRegister,
  useCashPermissions,
  type CashRegisterFilters,
} from '@/features/cash';

export default function CurrentRegisterPage() {
  const { permissions } = useCashPermissions();
  const {
    currentRegister,
    isLoading,
    movements,
    filters,
    pagination,
    actions,
    dialogs,
    summary,
  } = useCurrentRegister();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  if (isLoading) {
    return <LoadingScreen />;
  }

  const mappedFilters: CashRegisterFilters = {
    search: filters.search,
    type: filters.type,
    onSearchChange: filters.onSearchChange,
    onTypeChange: filters.onTypeChange,
    onReset: filters.onReset,
  };

  return (
    <div className='space-y-4 p-8 pt-6'>
      <CashRegisterHeader
        title='Caja Actual'
        description='Gestiona las operaciones de la caja actual'
        onOpenRegister={actions.openCurrentRegister}
        onCloseRegister={actions.closeRegister}
        canManage={permissions.canManage}
        isRegisterOpen={Boolean(currentRegister)}
      />

      <CashRegisterOverview
        currentRegister={currentRegister}
        movements={movements}
        isLoading={isLoading}
        filters={mappedFilters}
        pagination={pagination}
        permissions={{
          canCreate: permissions.canManage,
          canManage: permissions.canManage,
        }}
        actions={{
          onCreateMovement: actions.createMovement,
          onEdit: actions.editMovement,
          onDelete: actions.deleteMovement,
        }}
        summary={summary}
      />

      {/* Dialogs Section */}
      <div className='dialogs'>
        <CashRegisterOpenDialog {...dialogs.currentRegister} />
        <CashRegisterCloseDialog {...dialogs.close} />
        <CashMovementCreateDialog {...dialogs.movement} />
        <CashMovementEditDialog {...dialogs.edit} />
        <CashMovementDeleteDialog {...dialogs.delete} />

        {dialogs.receipt.open && summary && (
          <CashRegisterCloseReceipt {...dialogs.receipt} />
        )}
      </div>
    </div>
  );
}

import { LoadingScreen } from '@/components/ui/loading-screen';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileDown } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  CashRegisterHeader,
  CashRegisterOverview,
  useCashPermissions,
  useRegisterDetails,
} from '@/features/cash';

export default function CashRegisterDetailsPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const { permissions } = useCashPermissions();
  const { register, movements, isLoading, summary } = useRegisterDetails(id!);

  const formatRegisterId = (register: any) => {
    return register._id.slice(0, 8).toUpperCase();
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!register) {
    return (
      <div className='flex flex-col items-center justify-center h-[400px] gap-4'>
        <h3 className='text-xl font-semibold'>Registro no encontrado</h3>
        <p className='text-muted-foreground'>
          El registro de caja que buscas no existe o fue eliminado
        </p>
        <Button variant='outline' onClick={() => navigate('/cashier/history')}>
          Volver al historial
        </Button>
      </div>
    );
  }

  return (
    <div className='space-y-6 p-8 pt-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='icon'
            onClick={() => navigate(-1)}
            className='hover:bg-muted'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div>
            <div className='flex items-baseline gap-2'>
              <h1 className='text-2xl font-bold'>Detalle de Caja</h1>
              <span className='text-sm text-muted-foreground font-mono'>
                #{formatRegisterId(register)}
              </span>
            </div>
            <p className='text-sm text-muted-foreground'>
              Historial completo de ingresos y egresos realizados en esta caja
            </p>
          </div>
        </div>

        <Button
          variant='outline'
          size='sm'
          onClick={() => navigate('/cashier/history')}
        >
          Volver al historial
        </Button>
      </div>

      <div className='rounded-lg border bg-card'>
        <CashRegisterOverview
          currentRegister={register}
          movements={movements}
          isLoading={isLoading}
          filters={{
            search: '',
            type: 'ALL',
            onSearchChange: () => {},
            onTypeChange: () => {},
            onReset: () => {},
          }}
          pagination={{
            currentPage: 1,
            totalPages: 1,
            totalRecords: movements.length,
            onPageChange: () => {},
            onLimitChange: () => {},
            limit: movements.length,
          }}
          permissions={{
            canCreate: false,
            canManage: permissions.canManage,
          }}
          actions={{
            onCreateMovement: () => {},
            onEdit: () => {},
            onDelete: () => {},
          }}
          summary={summary}
          showFilters={false}
          showPagination={false}
        />
      </div>
    </div>
  );
}

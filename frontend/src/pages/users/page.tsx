import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  UsersHeader,
  UsersFilters,
  UsersActions,
  UsersTable,
  useUsersPage,
  usePermissions,
  UserCreateDialog,
  UserDeleteDialog,
  UserDetailsDialog,
  UserEditDialog,
  UserPasswordDialog,
  UserPermissionsDialog,
  UserStatusDialog,
} from '@/features/users';

export default function UsersPage() {
  const { permissions } = usePermissions();
  const {
    users,
    isLoading,
    filters,
    pagination,
    actions,
    dialog,
    deleteDialog,
    detailsDialog,
    editDialog,
    passwordDialog,
    permissionsDialog,
    statusDialog,
  } = useUsersPage();

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  const filtersProps = {
    search: filters.search,
    selectedRole: filters.selectedRole,
    onSearchChange: filters.setSearch,
    onRoleChange: filters.setSelectedRole,
    onReset: filters.resetFiltersAndReload,
  };

  const actionsProps = {
    canCreate: actions.permissions.canCreate,
    onCreate: actions.handlers.onCreate,
  };

  const showEmptyState =
    !isLoading && Array.isArray(users) && users.length === 0;

  return (
    <div className='space-y-6 p-4'>
      <UsersHeader
        title='Usuarios'
        description='Gestiona los usuarios del sistema'
      />

      <div className='space-y-4'>
        <div className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
          <div className='flex flex-col lg:flex-row gap-4 items-center'>
            <div className='w-full lg:w-3/4'>
              <UsersFilters {...filtersProps} />
            </div>
            <div className='w-full lg:w-1/4 flex justify-end items-center'>
              <UsersActions {...actionsProps} />
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className='flex justify-center items-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900'></div>
          </div>
        ) : showEmptyState ? (
          <div className='flex flex-col items-center justify-center py-12 text-center'>
            <p className='text-muted-foreground mb-4'>
              No se encontraron usuarios
            </p>
          </div>
        ) : (
          <UsersTable
            users={users}
            isLoading={isLoading}
            pagination={pagination}
            permissions={actions.permissions}
            onEdit={actions.handlers.onEdit}
            onDelete={actions.handlers.onDelete}
            onChangePassword={actions.handlers.onChangePassword}
            onManagePermissions={actions.handlers.onManagePermissions}
            onViewDetails={actions.handlers.onViewDetails}
            onToggleStatus={actions.handlers.onToggleStatus}
          />
        )}
      </div>
      <UserCreateDialog
        open={dialog.isOpen}
        onOpenChange={dialog.onOpenChange}
        onSubmit={dialog.onSubmit}
        isLoading={dialog.isLoading}
      />

      <UserDeleteDialog
        user={deleteDialog.user}
        open={deleteDialog.isOpen}
        onOpenChange={deleteDialog.onOpenChange}
        onConfirm={deleteDialog.onConfirm}
        isLoading={deleteDialog.isLoading}
      />

      <UserDetailsDialog
        user={detailsDialog.user}
        open={detailsDialog.isOpen}
        onOpenChange={detailsDialog.onOpenChange}
      />

      <UserEditDialog
        user={editDialog.user}
        isOpen={editDialog.isOpen}
        onOpenChange={editDialog.onOpenChange}
        onUpdate={editDialog.onUpdate}
        isLoading={editDialog.isLoading}
      />

      <UserPasswordDialog
        user={passwordDialog.user}
        open={passwordDialog.isOpen}
        onOpenChange={passwordDialog.onOpenChange}
        onSubmit={passwordDialog.onSubmit}
        isLoading={passwordDialog.isLoading}
      />

      <UserPermissionsDialog
        user={permissionsDialog.user}
        open={permissionsDialog.isOpen}
        onOpenChange={permissionsDialog.onOpenChange}
        onSavePermissions={actions.handlers.onSavePermissions}
        isLoading={permissionsDialog.isLoading}
      />

      <UserStatusDialog
        user={statusDialog.user}
        open={statusDialog.isOpen}
        onOpenChange={statusDialog.onOpenChange}
        onConfirm={statusDialog.onConfirm}
        isLoading={statusDialog.isLoading}
      />
    </div>
  );
}

import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SaleForm } from '@/features/sales/components/SaleForm';
import { useCreateSale } from '@/features/sales';
import { useProducts } from '@/features/inventory/products';
import { useCustomerSelector } from '@/features/customers/hooks/useCustomerSelector';
import { CashRegister, useCurrentRegister } from '@/features/cash';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  createSaleSchema,
  type CreateSaleSchema,
} from '@/features/sales/schemas/sale.schema';
import { useState } from 'react';
import { useCreateCustomer } from '@/features/customers/hooks/useCreateCustomer';
import type { CreateCustomerDto } from '@/features/customers/types/customer.types';
import type { Customer } from '@/features/customers/types/customer.types';
import type { CreateSaleDto, Sale } from '@/features/sales/types/sale.types';
import { PAYMENT_METHODS } from '@/features/sales/constants/sale.constants';
import { SaleSuccessDialog } from '@/features/sales/components/SaleSuccessDialog';

export default function CreateSalePage() {
  const navigate = useNavigate();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [completedSale, setCompletedSale] = useState<Sale | null>(null);
  const [showCreateCustomer, setShowCreateCustomer] = useState(false);
  const form = useForm<CreateSaleSchema>({
    resolver: zodResolver(createSaleSchema),
    defaultValues: {
      items: [],
      discountType: 'fixed',
      discount: 0,
      paymentMethod: 'CASH',
    },
  });
  const { products = [] } = useProducts({
    getAllProducts: true,
    enabled: true,
  });
  const { customers = [] } = useCustomerSelector();
  const { currentRegister, isLoading } = useCurrentRegister({
    includeMovements: false,
  });
  const createCustomerMutation = useCreateCustomer();
  const { createSale, isLoading: isCreatingSale } = useCreateSale();

  if (isLoading) {
    return <div>Cargando...</div>;
  }

  if (!currentRegister) {
    return <div>No hay caja abierta</div>;
  }

  const formatRegisterId = (register: CashRegister) => {
    return register._id.slice(0, 8).toUpperCase();
  };

  const handleCreateCustomer = async (
    data: CreateCustomerDto
  ): Promise<Customer> => {
    return await createCustomerMutation.mutateAsync(data);
  };

  const resetEntireForm = () => {
    form.reset({
      items: [],
      discountType: 'fixed',
      discount: 0,
      customer: undefined,
      paymentMethod: 'CASH',
      notes: undefined,
      amountReceived: undefined,
    });

    setShowSuccessDialog(false);
    setCompletedSale(null);
    setShowCreateCustomer(false);
    form.trigger();
  };

  const handleSuccessDialogClose = (open: boolean) => {
    if (!open) {
      setShowSuccessDialog(false);
      // Solo reseteamos si estamos cerrando el diálogo
      if (showSuccessDialog) {
        resetEntireForm();
      }
    }
  };

  const handleSubmit = async (data: CreateSaleSchema) => {
    try {
      const saleData: CreateSaleDto = {
        customerId: data.customer,
        items: data.items.map((item) => ({
          productId: item.product!,
          quantity: item.quantity!,
          unitPrice: item.unitPrice!, // Usar unitPrice
          discount: item.discount || 0,
        })),
        paymentMethod: data.paymentMethod as keyof typeof PAYMENT_METHODS,
        discount: data.discount || 0,
        notes: data.notes,
        amountReceived: data.amountReceived,
        cashRegisterId: currentRegister._id,
      };

      const response = await createSale(saleData);

      // Asegurarnos de que la respuesta contiene una venta completa
      if (response?.sale && 'number' in response.sale) {
        setCompletedSale(response.sale as Sale);
        setShowSuccessDialog(true);
      }
    } catch (error) {
      console.error('Error al crear la venta:', error);
    }
  };

  return (
    <main className='space-y-4 p-8 pt-6'>
      <header className='flex items-center justify-between border-b pb-4'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='icon'
            onClick={() => navigate('/sales')}
          >
            <ArrowLeft className='h-5 w-5' />
          </Button>
          <div>
            <div className='flex items-baseline gap-2'>
              <h2 className='text-3xl font-bold tracking-tight'>Nueva Venta</h2>
              <span className='text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded'>
                #{formatRegisterId(currentRegister)}
              </span>
            </div>
            <p className='text-sm text-muted-foreground'>
              Operador: {currentRegister.openedBy.username}
            </p>
          </div>
        </div>
      </header>

      <div className='max-w-[1200px] mx-auto'>
        <SaleForm
          key={completedSale?._id}
          form={form}
          products={products}
          customers={customers}
          onSubmit={handleSubmit}
          isLoading={isCreatingSale}
          onCancel={() => navigate('/sales')}
          showCreateCustomer={showCreateCustomer}
          setShowCreateCustomer={setShowCreateCustomer}
          handleCreateCustomer={handleCreateCustomer}
          isCreatingCustomer={createCustomerMutation.isPending}
          onResetForm={resetEntireForm}
        />

        {showSuccessDialog && completedSale && (
          <SaleSuccessDialog
            open={showSuccessDialog}
            onOpenChange={handleSuccessDialogClose}
            sale={completedSale}
            onNewSale={resetEntireForm}
          />
        )}
      </div>
    </main>
  );
}

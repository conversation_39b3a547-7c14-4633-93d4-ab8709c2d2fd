import { RestrictedAccess } from '@/components/common/RestrictedAccess';
import {
  SalesFilters,
  SalesActions,
  SalesTable,
  SaleDetailsDialog,
  SaleCancelDialog,
  SaleDeleteDialog,
  useSalesPage,
  useSalePermissions,
} from '@/features/sales';
import { useNavigate } from 'react-router-dom';

export default function SalesPage() {
  const navigate = useNavigate();
  const { permissions } = useSalePermissions();
  const {
    data,
    isLoading,
    dialogProps,
    filters,
    setFilters,
    pagination,
    actions,
  } = useSalesPage();

  const handleExportPdf = () => {
    navigate('/reports/sales');
  };

  const handleExportExcel = () => {
    navigate('/reports/sales');
  };

  if (!permissions.canList) {
    return <RestrictedAccess />;
  }

  return (
    <main className='space-y-4 p-8 pt-6'>
      <header className='flex items-center justify-between'>
        <div>
          <h2 className='text-3xl font-bold tracking-tight'>Ventas</h2>
          <p className='text-muted-foreground'>
            Gestiona las ventas del sistema
          </p>
        </div>
      </header>

      <section className='bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 rounded-lg border'>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <div className='w-full lg:w-[70%]'>
            <SalesFilters filters={filters} onFiltersChange={setFilters} />
          </div>
          <div className='w-full lg:w-[30%] flex justify-end'>
            <SalesActions
              canCreate={permissions.canCreate}
              onExportPdf={handleExportPdf}
              onExportExcel={handleExportExcel}
              onExportCSV={undefined}
            />
          </div>
        </div>
      </section>

      <SalesTable
        data={data}
        isLoading={isLoading}
        permissions={permissions}
        onViewDetails={actions.onViewDetails}
        onCancel={actions.onCancel}
        onDelete={actions.handleDelete}
        dialogProps={dialogProps}
        pagination={pagination}
      />

      <SaleDetailsDialog {...dialogProps.details} />
      <SaleCancelDialog {...dialogProps.cancel} />
      <SaleDeleteDialog {...dialogProps.delete} />
    </main>
  );
}

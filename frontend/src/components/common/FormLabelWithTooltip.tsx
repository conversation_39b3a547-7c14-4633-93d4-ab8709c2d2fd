import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { InfoIcon } from 'lucide-react';
import { cn } from '@/lib/utils/styles';

interface FormLabelWithTooltipProps {
  label: string;
  tooltip?: string;
  required?: boolean;
  error?: boolean;
}

export const FormLabelWithTooltip = ({
  label,
  tooltip,
  required = false,
  error = false,
}: FormLabelWithTooltipProps) => (
  <div className='flex items-center gap-1'>
    <span className={cn(error && 'text-destructive')}>{label}</span>
    {required && <span className='text-destructive'>*</span>}
    {tooltip && (
      <TooltipProvider delayDuration={0}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant='ghost'
              size='icon'
              type='button'
              className='h-4 w-4 p-0 hover:bg-transparent'
            >
              <InfoIcon className='h-4 w-4 text-muted-foreground' />
            </Button>
          </TooltipTrigger>
          <TooltipContent side='right'>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )}
  </div>
);

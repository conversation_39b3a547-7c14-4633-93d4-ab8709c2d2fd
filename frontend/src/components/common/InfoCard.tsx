import { LucideIcon } from 'lucide-react';

interface InfoCardProps {
  icon: LucideIcon;
  title: string;
  children: React.ReactNode;
}

export function InfoCard({ icon: Icon, title, children }: InfoCardProps) {
  return (
    <div className='bg-card rounded-lg p-2.5 space-y-1.5 shadow-sm hover:shadow-md transition-all duration-200 border'>
      <div className='flex items-center gap-1.5 text-muted-foreground mb-1.5'>
        <Icon className='w-3.5 h-3.5' />
        <p className='text-xs font-medium'>{title}</p>
      </div>
      {children}
    </div>
  );
}
import { Loader2 } from 'lucide-react';

interface LoadingScreenProps {
  message?: string;
}

export function LoadingScreen({ message = 'Cargando...' }: LoadingScreenProps) {
  return (
    <div className='h-screen w-full flex flex-col items-center justify-center bg-background'>
      <Loader2 className='h-8 w-8 animate-spin text-primary' />
      <p className='mt-4 text-muted-foreground'>{message}</p>
    </div>
  );
}

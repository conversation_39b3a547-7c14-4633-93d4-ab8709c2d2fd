import { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { router } from './router';
import { useAuthStore } from '@/features/auth';
import { useSettingsStore } from '@/features/settings';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export function App() {
  const { isLoading: isAuthLoading, initializeAuth } = useAuthStore();
  const { fetchPublicSettings, fetchGeneralSettings } = useSettingsStore();

  useEffect(() => {
    const initialize = async () => {
      await Promise.all([
        initializeAuth(),
        fetchPublicSettings(),
        fetchGeneralSettings(), // Agregamos esta llamada
      ]);
    };

    initialize();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  if (isAuthLoading) {
    return (
      <div className='flex h-screen w-full items-center justify-center'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  return <RouterProvider router={router} />;
}

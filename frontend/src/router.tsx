import { lazy, Suspense } from 'react';
import {
  createBrowserRouter,
  Navigate,
  RouterProvider,
} from 'react-router-dom';
import { LoadingScreen } from '@/components/ui/loading-screen';
import { Permission, ProtectedRoute, RoleBasedRoute } from '@/features/auth';
import { PERMISSIONS, USER_ROLES } from '@/features/auth';

// Lazy load components
const Login = lazy(() => import('@/pages/auth/login'));
const DashboardLayout = lazy(
  () => import('@/features/dashboard/components/layouts/DashboardLayout')
);
const Dashboard = lazy(() => import('@/pages/dashboard'));
const NewSalePage = lazy(() => import('@/pages/sales/create/page'));
const SalesPage = lazy(() => import('@/pages/sales/page'));
// const SalesHistoryPage = lazy(() => import('@/pages/sales/history/page'));
// const SalesReturnsPage = lazy(() => import('@/pages/sales/returns/page'));
const ProductsPage = lazy(() => import('@/pages/inventory/products/page'));
const StockEntriesPage = lazy(
  () => import('@/pages/inventory/stock/entry/page')
);
const StockOutputsPage = lazy(
  () => import('@/pages/inventory/stock/output/page')
);
const UsersPage = lazy(() => import('@/pages/users/page'));
const SettingsGeneralPage = lazy(() => import('@/pages/settings/general'));
const UnauthorizedPage = lazy(() => import('@/pages/error/unauthorized'));
const NotFound = lazy(() => import('@/pages/error/not-found'));
const CategoriesPage = lazy(() => import('@/pages/inventory/categories/page'));
const LaboratoriesPage = lazy(
  () => import('@/pages/inventory/laboratories/page')
);
const CashDashboardPage = lazy(() => import('@/pages/cash/current/page'));
const CashHistoryPage = lazy(() => import('@/pages/cash/history/page'));
const CashRegisterDetailsPage = lazy(() => import('@/pages/cash/details/page'));
const CustomersPage = lazy(() => import('@/pages/customers/page'));
const SalesReportPage = lazy(() => import('@/pages/reports/sales/page'));
const InventoryReportPage = lazy(
  () => import('@/pages/reports/inventory/page')
);

const LazyLoad = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingScreen />}>{children}</Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Navigate to='/auth/login' replace />,
  },
  {
    path: '/auth/login',
    element: (
      <LazyLoad>
        <Login />
      </LazyLoad>
    ),
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <LazyLoad>
          <DashboardLayout />
        </LazyLoad>
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyLoad>
            <Dashboard />
          </LazyLoad>
        ),
      },
      // Ventas
      {
        path: 'sales',
        children: [
          {
            index: true,
            element: (
              <RoleBasedRoute requiredPermissions={[PERMISSIONS.SALES.LIST]}>
                <LazyLoad>
                  <SalesPage />
                </LazyLoad>
              </RoleBasedRoute>
            ),
          },
          {
            path: 'create',
            element: (
              <RoleBasedRoute requiredPermissions={[PERMISSIONS.SALES.CREATE]}>
                <LazyLoad>
                  <NewSalePage />
                </LazyLoad>
              </RoleBasedRoute>
            ),
          },
        ],
      },
      // Inventario
      {
        path: 'inventory/products',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.INVENTORY.PRODUCTS.LIST]}
          >
            <LazyLoad>
              <ProductsPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'inventory/stock/entries',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST]}
          >
            <LazyLoad>
              <StockEntriesPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'inventory/stock/outputs',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST]}
          >
            <LazyLoad>
              <StockOutputsPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Configuración
      {
        path: 'settings/general',
        element: (
          <RoleBasedRoute
            allowedRoles={[USER_ROLES.ADMIN]}
            requiredPermissions={[PERMISSIONS.SETTINGS.EDIT]}
          >
            <LazyLoad>
              <SettingsGeneralPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'settings/users',
        element: (
          <RoleBasedRoute
            allowedRoles={[USER_ROLES.ADMIN]}
            requiredPermissions={[PERMISSIONS.USERS.LIST]}
          >
            <LazyLoad>
              <UsersPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Categorías
      {
        path: 'inventory/categories',
        element: (
          <RoleBasedRoute requiredPermissions={[PERMISSIONS.CATEGORIES.LIST]}>
            <LazyLoad>
              <CategoriesPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Laboratorios
      {
        path: 'inventory/laboratories',
        element: (
          <RoleBasedRoute requiredPermissions={[PERMISSIONS.LABORATORIES.LIST]}>
            <LazyLoad>
              <LaboratoriesPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'cashier/operations',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.CASH.LIST as Permission]}
          >
            <LazyLoad>
              <CashDashboardPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'cashier/history',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.CASH.LIST as Permission]}
          >
            <LazyLoad>
              <CashHistoryPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'cashier/history/:id',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.CASH.LIST as Permission]}
          >
            <LazyLoad>
              <CashRegisterDetailsPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Clientes
      {
        path: 'customers',
        element: (
          <RoleBasedRoute requiredPermissions={[PERMISSIONS.CUSTOMERS.LIST]}>
            <LazyLoad>
              <CustomersPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Reportes
      {
        path: 'reports/sales',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.REPORTS.SALES.LIST]}
          >
            <LazyLoad>
              <SalesReportPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
      // Reportes
      {
        path: 'reports/inventory',
        element: (
          <RoleBasedRoute
            requiredPermissions={[PERMISSIONS.REPORTS.INVENTORY.LIST]}
          >
            <LazyLoad>
              <InventoryReportPage />
            </LazyLoad>
          </RoleBasedRoute>
        ),
      },
    ],
  },
  {
    path: '/unauthorized',
    element: (
      <LazyLoad>
        <UnauthorizedPage />
      </LazyLoad>
    ),
  },
  {
    path: '*',
    element: (
      <LazyLoad>
        <NotFound />
      </LazyLoad>
    ),
  },
]);

export default function Router() {
  return <RouterProvider router={router} />;
}

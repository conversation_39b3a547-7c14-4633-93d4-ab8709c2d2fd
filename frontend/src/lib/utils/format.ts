/**
 * Formatea un número como moneda en formato BOB (Boliviano)
 * @param amount - El monto numérico a formatear
 * @returns String formateado como moneda (ej: "Bs 1.234,50")
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-BO', {
    style: 'currency',
    currency: 'BOB',
    minimumFractionDigits: 2,
  }).format(amount);
};

/**
 * Formatea un número como porcentaje
 * @param value - El valor numérico a formatear
 * @returns String formateado como porcentaje (ej: "12,34%")
 */
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('es-BO', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
};

/**
 * Convierte una fecha a UTC ISO String con hora 00:00:00
 */
export const toUTCISOString = (date: Date | string): string => {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  return d.toISOString();
};

/**
 * Valida si una fecha es válida
 */
export const isValidDate = (date: any): boolean => {
  const d = new Date(date);
  return !isNaN(d.getTime());
};

/**
 * Valida si una fecha es futura (mayor que hoy)
 */
export const isFutureDate = (date: Date | string): boolean => {
  const d = new Date(date);
  const today = new Date();
  d.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);
  return d > today;
};

/**
 * Formatea una fecha en formato dd/mm/yyyy
 */
export const formatDate = (date: string | Date | null | undefined): string => {
  // Si la fecha es nula, undefined o una cadena vacía
  if (!date) return 'N/A';

  // Intentar crear un objeto Date
  let dateObject: Date;
  try {
    dateObject = typeof date === 'string' ? new Date(date) : date;

    // Verificar si la fecha es válida
    if (isNaN(dateObject.getTime())) {
      return 'N/A';
    }

    return new Intl.DateTimeFormat('es-BO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      timeZone: 'UTC', // Esto asegura que use la fecha UTC sin conversiones
    }).format(dateObject);
  } catch (error) {
    return 'N/A';
  }
};

export const formatMeasurementUnit = (unit: string) => {
  const formats = {
    TABLETA: 'Tableta',
    CAPSULA: 'Cápsula',
    AMPOLLA: 'Ampolla',
    FRASCO: 'Frasco',
    CREMA: 'Crema',
    GOTAS: 'Gotas',
  };
  return formats[unit] || unit;
};

export const formatAdministrationRoute = (route: string) => {
  const routes = {
    ORAL: 'Oral',
    INYECTABLE: 'Inyectable',
    TOPICA: 'Tópica',
    OFTALMICA: 'Oftálmica',
    OTRO: 'Otro',
  };
  return routes[route] || route;
};

export const formatStorageCondition = (condition: string) => {
  const conditions = {
    TEMPERATURA_AMBIENTE: 'Temperatura Ambiente',
    REFRIGERACION: 'Refrigeración',
    CONGELACION: 'Congelación',
  };
  return conditions[condition] || condition;
};

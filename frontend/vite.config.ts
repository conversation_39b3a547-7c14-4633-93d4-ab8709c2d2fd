import { defineConfig, loadEnv, UserConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import compression from 'vite-plugin-compression';
import path from 'path';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    plugins: [
      react(),
      compression({
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 10240, // Solo comprimir archivos mayores a 10KB
        deleteOriginFile: false,
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: mode !== 'production',
      minify: 'terser', // Usar terser para mejor minificación
      terserOptions: {
        compress: {
          drop_console: mode === 'production', // Eliminar console.log en producción
          drop_debugger: mode === 'production',
        },
      },
      rollupOptions: {
        output: {
          manualChunks: {
            'vendor-react': ['react', 'react-dom', 'react-router-dom'],
            'vendor-ui': [
              '@radix-ui/react-alert-dialog',
              '@radix-ui/react-checkbox',
              '@radix-ui/react-dialog',
              '@radix-ui/react-dropdown-menu',
              '@radix-ui/react-label',
              '@radix-ui/react-popover',
              '@radix-ui/react-scroll-area',
              '@radix-ui/react-select',
              '@radix-ui/react-slot',
              '@radix-ui/react-switch',
              '@radix-ui/react-toast',
              '@radix-ui/react-toggle',
              '@radix-ui/react-tooltip',
            ],
            'vendor-utils': [
              'axios',
              'date-fns',
              'clsx',
              'class-variance-authority',
              'tailwind-merge',
            ],
            'vendor-excel': ['exceljs'],
          },
          inlineDynamicImports: false,
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
      },
      chunkSizeWarningLimit: 1000,
      // Optimizaciones adicionales
      target: 'es2015', // Target moderno para mejor optimización
      assetsInlineLimit: 4096, // Inline assets menores a 4KB
      cssCodeSplit: true, // Separar CSS en chunks
      reportCompressedSize: false, // Deshabilitar reporte de tamaño comprimido para builds más rápidos
    },
    server: {
      port: parseInt(env.VITE_PORT || '8080'),
      proxy: {
        '/api': {
          target: env.VITE_API_URL,
          changeOrigin: true,
          secure: false,
        },
      },
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@radix-ui/react-alert-dialog',
        '@radix-ui/react-checkbox',
        '@radix-ui/react-dialog',
        '@radix-ui/react-dropdown-menu',
        '@radix-ui/react-label',
        '@radix-ui/react-popover',
        '@radix-ui/react-scroll-area',
        '@radix-ui/react-select',
        '@radix-ui/react-slot',
        '@radix-ui/react-switch',
        '@radix-ui/react-toast',
        '@radix-ui/react-toggle',
        '@radix-ui/react-tooltip',
        'axios',
        'date-fns',
        'clsx',
        'class-variance-authority',
        'tailwind-merge',
        'html2canvas',
        'jspdf',
      ],
      exclude: ['exceljs'],
    },
  } satisfies UserConfig;
});

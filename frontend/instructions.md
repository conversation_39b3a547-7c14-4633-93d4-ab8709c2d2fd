# Guía de Desarrollo del Sistema de Farmacia

Este documento describe las características, datos necesarios, interacciones con el backend y consideraciones técnicas para el desarrollo del sistema de farmacia. Está diseñado para ser utilizado como referencia por los desarrolladores frontend y backend.

---

## **1. Dashboard**
El dashboard es la página principal después de iniciar sesión y proporciona un resumen visual de las métricas clave.

### **Características**
- **Navbar Responsivo:**
  - Muestra el logo de la farmacia, el nombre del usuario, su rol (ej., "Administrador" o "Cajero") y un botón de "Cerrar Sesión".
  - Incluye un ícono de notificaciones para alertas importantes (ej., stock bajo, productos próximos a caducar).
- **Menú Lateral Colapsable:**
  - Proporciona enlaces de navegación a los módulos: Ventas, Inventario, Caja, Reportes y Configuración.
  - Se colapsa en un menú de hamburguesa en pantallas pequeñas.
- **Widgets de Resumen:**
  - Muestra métricas clave:
    - Total de ventas del día/mes.
    - Número de productos con stock bajo.
    - Productos próximos a caducar.
    - Balance actual de la caja.
  - Usa gráficos visuales (barras, donas o líneas) para representar tendencias.

### **Datos Necesarios (MongoDB):**
- Ventas diarias/mensuales (`sales` collection).
- Niveles de inventario (`inventory` collection).
- Balance de la caja (`cashRegister` collection).

### **Interacción con el Backend:**
- Realizar llamadas GET a `/api/sales`, `/api/inventory` y `/api/cash-register` para obtener los datos necesarios.
- Usar `fetch` o `axios` para consumir las APIs.

---

## **2. Módulo de Ventas**
Este módulo permite registrar y gestionar transacciones de ventas.

### **Características**
- **Registro de Ventas:**
  - Permite agregar productos mediante escaneo de código de barras/QR o ingreso manual.
  - Muestra detalles del producto seleccionado (nombre, precio, stock).
  - Soporta agregar múltiples productos a un carrito temporal.
- **Historial de Compras:**
  - Muestra el historial de transacciones vinculado a clientes específicos.
  - Proporciona filtros por cliente, fecha o producto.
- **Generación Automática de Recibos:**
  - Genera un recibo/ticket al finalizar cada transacción con:
    - Fecha y hora de la transacción.
    - Lista de productos comprados.
    - Subtotal, descuentos aplicados y total final.
  - Incluye opciones para imprimir o descargar el recibo en formato PDF.

### **Datos Necesarios (MongoDB):**
- Detalles del producto (`products` collection).
- Información del cliente (`customers` collection).
- Registros de transacciones (`transactions` collection).

### **Interacción con el Backend:**
- POST a `/api/sales` para registrar una nueva venta.
- GET a `/api/sales/history` para obtener el historial de compras.

---

## **3. Módulo de Inventario**
Este módulo gestiona el inventario de la farmacia.

### **Características**
- **Control de Stock:**
  - Muestra el estado actual del inventario.
  - Envía notificaciones automáticas cuando los niveles de stock caen por debajo de un umbral configurable.
- **Gestión de Inventario:**
  - Registra compras realizadas a proveedores.
  - Maneja devoluciones de productos (defectuosos o no deseados).
- **Notificaciones de Caducidad:**
  - Rastrea las fechas de caducidad de los productos.
  - Envía alertas automáticas para productos próximos a caducar.

### **Datos Necesarios (MongoDB):**
- Detalles del producto (`products` collection).
- Información del proveedor (`suppliers` collection).

### **Interacción con el Backend:**
- GET a `/api/inventory` para obtener el estado del inventario.
- POST a `/api/inventory/purchase` para registrar una compra.
- PUT a `/api/inventory/return` para manejar devoluciones.

---

## **4. Módulo de Caja**
Este módulo gestiona la apertura y cierre de la caja registradora.

### **Características**
- **Apertura y Cierre:**
  - Permite a los cajeros abrir y cerrar la caja al inicio y fin del día.
  - Registra todas las transacciones durante el período abierto.
- **Informe de Cierre:**
  - Genera un informe detallado al final del día, incluyendo:
    - Total de ventas.
    - Efectivo recibido.
    - Diferencias entre el saldo esperado y real.

### **Datos Necesarios (MongoDB):**
- Registros de transacciones (`transactions` collection).
- Balance de la caja (`cashRegister` collection).

### **Interacción con el Backend:**
- POST a `/api/cash-register/open` para abrir la caja.
- POST a `/api/cash-register/close` para cerrar la caja y generar el informe.

---

## **5. Módulo de Reportes**
Este módulo genera diversos reportes para ayudar en la toma de decisiones.

### **Características**
- **Reportes de Ventas:**
  - Filtra ventas por rango de fechas, producto o usuario.
  - Muestra gráficos visuales (barras, donas o líneas) para representar tendencias.
- **Niveles de Inventario:**
  - Identifica productos más vendidos y menos vendidos.
  - Proporciona sugerencias basadas en patrones de compra.
- **Exportación a PDF:**
  - Permite exportar cualquier informe en formato PDF.

### **Datos Necesarios (MongoDB):**
- Datos de ventas (`sales` collection).
- Datos de inventario (`inventory` collection).

### **Interacción con el Backend:**
- GET a `/api/reports/sales` para obtener datos de ventas.
- GET a `/api/reports/inventory` para obtener datos de inventario.

---

## **6. Módulo de Seguridad**
Este módulo gestiona la autenticación y autorización.

### **Características**
- **Acceso Basado en Roles:**
  - Define dos roles:
    - Administrador: Acceso completo al sistema.
    - Cajero: Acceso limitado (ventas, caja).
  - Implementa autenticación segura usando JWT.
- **Protección Contra Ataques Comunes:**
  - Valida todas las entradas de usuario para evitar inyecciones maliciosas.

### **Datos Necesarios (MongoDB):**
- Credenciales de usuario (`users` collection).

### **Interacción con el Backend:**
- POST a `/api/auth/login` para iniciar sesión.
- POST a `/api/auth/register` para registrar nuevos usuarios.

---

## **Indicaciones Técnicas para el Desarrollo**

1. **Diseño Responsivo:**
   - Usar frameworks como TailwindCSS o Material-UI para asegurar que todas las pantallas sean completamente responsivas.
   - Asegurar que el menú lateral y los widgets se ajusten bien en dispositivos móviles.

2. **Llamadas API:**
   - Usar `fetch` o `axios` para realizar llamadas al backend.
   - Implementar manejo de errores para mostrar mensajes claros al usuario en caso de fallos.

3. **Validación de Entradas:**
   - Validar todas las entradas de usuario en el frontend antes de enviarlas al backend.
   - Mostrar mensajes de error en español (ej., "El campo es requerido").

4. **Generación de PDF:**
   - Usar librerías como `pdf-lib` o `jsPDF` para exportar reportes en formato PDF.

5. **Notificaciones:**
   - Usar librerías como `react-toastify` para mostrar notificaciones automáticas (ej., stock bajo, productos próximos a caducar).
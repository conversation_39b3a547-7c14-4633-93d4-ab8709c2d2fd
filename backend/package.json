{"name": "farmafacil-backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "NODE_ENV=production node dist/server.js", "clean": "rm -rf dist", "prebuild": "npm run clean && mkdir -p dist/public", "seed:users": "tsx src/seeders/userSeeder.ts", "seed:settings": "tsx src/seeders/settingsSeeder.ts", "seed:products": "tsx src/seeders/productSeeder.ts", "seed:categories": "tsx src/seeders/categorySeeder.ts", "seed:laboratories": "tsx src/seeders/laboratorySeeder.ts", "seed:customers": "tsx src/seeders/customerSeeder.ts", "seed:sales": "tsx src/seeders/saleSeeder.ts", "seed:all": "npm run seed:settings && npm run seed:categories && npm run seed:laboratories && npm run seed:customers && npm run seed:users && npm run seed:products && npm run seed:sales", "clear-db": "tsx src/seeders/clearDb.ts", "reset-db": "npm run clear-db && npm run seed:all", "lint": "eslint . --ext .ts", "test": "NODE_OPTIONS=--experimental-vm-modules jest --config jest.config.cjs --no-cache --detectOpenHandles --testTimeout=30000", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@types/multer": "^1.4.12", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "ms": "^2.1.3", "multer": "^1.4.5-lts.1", "zod": "^3.21.4"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.1", "@types/morgan": "^1.9.4", "@types/ms": "^2.1.0", "@types/node": "^18.15.11", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.3", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "tsx": "^4.7.0", "typescript": "^5.0.4"}}
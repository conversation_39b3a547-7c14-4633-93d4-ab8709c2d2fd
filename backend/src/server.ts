import app from './app.js';
import { config } from './config/config.js';
import { connectDB } from './config/database.js';
import { logger } from './utils/logger.js';
import { networkInterfaces } from 'os';

const getLocalNetworkIP = (): string => {
  const nets = networkInterfaces();
  let localIP = 'No encontrada';

  for (const name of Object.keys(nets)) {
    const interfaces = nets[name];
    if (!interfaces) continue;

    for (const net of interfaces) {
      // Skip IPv6 addresses and loopback
      if (net.family === 'IPv4' && !net.internal) {
        localIP = net.address;
        return localIP;
      }
    }
  }
  return localIP;
};

const startServer = async () => {
  if (process.env.NODE_ENV !== 'test') {
    await connectDB();
  }

  const localIP = getLocalNetworkIP();

  app.listen(config.port, '0.0.0.0', () => {
    logger.section('Server Status');
    logger.info(`Running on port ${config.port} (${config.env})`);
    logger.info(`CORS: ${config.corsOrigin}`);

    if (config.isProduction) {
      logger.section('API Endpoints');
      logger.url('Network', `http://${localIP}:${config.port}/api`);
    } else if (config.isDevelopment) {
      logger.section('API Endpoints');
      logger.url('Local', `http://localhost:${config.port}/api`);
      logger.url('Network', `http://${localIP}:${config.port}/api`);
    }
  });
};

startServer();

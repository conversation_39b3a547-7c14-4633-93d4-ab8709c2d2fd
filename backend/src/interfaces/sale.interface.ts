import { Types } from 'mongoose';
import { PAYMENT_METHODS, SALE_STATUS } from '../constants/sale.constants.js';

// Interfaces para documentos populados
export interface PopulatedProduct {
  _id: Types.ObjectId;
  name: string;
  sku: string;
}

export interface PopulatedCustomer {
  _id: Types.ObjectId;
  businessName: string;
  documentNumber: string;
}

export interface PopulatedUser {
  _id: Types.ObjectId;
  username: string;
}

// Interface para items de venta (con y sin populate)
export interface ISaleItem {
  _id?: Types.ObjectId;
  product: Types.ObjectId | PopulatedProduct;
  quantity: number;
  unitPrice: number;
  discount: number;
  subtotal: number;
}

// Interface para item populado
export interface PopulatedSaleItem extends Omit<ISaleItem, 'product'> {
  product: PopulatedProduct;
  name?: string;
  productId?: string | Types.ObjectId;
}

// Interface base de venta
export interface ISale {
  _id: Types.ObjectId;
  number: string;
  date: Date;
  customer?: Types.ObjectId | PopulatedCustomer;
  items: ISaleItem[];
  subtotal: number;
  discount: number;
  total: number;
  paymentMethod: keyof typeof PAYMENT_METHODS;
  status: keyof typeof SALE_STATUS;
  cashRegister: Types.ObjectId;
  invoice?: Types.ObjectId;
  createdBy: Types.ObjectId | PopulatedUser;
  updatedBy?: Types.ObjectId | PopulatedUser;
  canceledBy?: Types.ObjectId | PopulatedUser;
  canceledAt?: Date;
  cancelReason?: string;
  cancelNotes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Interface para venta populada
export interface PopulatedSale extends Omit<ISale, 'items'> {
  items: PopulatedSaleItem[];
}

// DTOs
export interface CreateSaleItemDto {
  productId: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
}

export interface CreateSaleDto {
  customerId?: string;
  items: CreateSaleItemDto[];
  discountType?: 'percentage' | 'fixed';
  discount?: number;
  paymentMethod: keyof typeof PAYMENT_METHODS;
  cashRegisterId: string;
  notes?: string;
  amountReceived?: number | null;
}

export interface UpdateSaleDto {
  customer?: string;
  items?: {
    product: string;
    quantity: number;
    unitPrice: number;
    discount?: number;
  }[];
  paymentMethod?: keyof typeof PAYMENT_METHODS;
  discount?: number;
}

export interface CancelSaleDto {
  reason: string;
  notes?: string;
  canceledAt?: Date;
}

// Agregar la interfaz para eliminación
export interface DeleteSaleDto {
  deletedAt?: Date;
  deletedBy: string;
}

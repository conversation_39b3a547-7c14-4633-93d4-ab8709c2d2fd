import { Request } from 'express';
import { Types } from 'mongoose';

export interface TokenPayload {
  _id: string;
  username: string;
  role: string;
  permissions: string[];
  isActive: boolean;
}

export interface JwtPayload {
  _id: string | Types.ObjectId;
  username: string;
  role: string;
  permissions: string[];
  isActive: boolean;
  isSuperAdmin?: boolean;
}

export interface AuthRequest extends Request {
  user?: JwtPayload;
  token?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    _id: string | Types.ObjectId;
    username: string;
    role: string;
    permissions: string[];
    isActive: boolean;
  };
}

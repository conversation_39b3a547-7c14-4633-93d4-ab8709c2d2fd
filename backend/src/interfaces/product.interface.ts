import { Document, Types } from 'mongoose';

export interface IProduct {
  sku: string;
  name: string;
  description: string;
  image?: string | null;
  laboratory: Types.ObjectId;
  category: Types.ObjectId;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  location: string;
  expirationDate: Date;
  batchNumber: string;
  barcode?: string;
  requiresPrescription: boolean;
  presentation: string;
  concentration: string;
  measurementUnit:
    | 'TABLETA'
    | 'CAPSULA'
    | 'AMPOLLA'
    | 'FRASCO'
    | 'CREMA'
    | 'GOTAS';
  administrationRoute: 'ORAL' | 'INYECTABLE' | 'TOPICA' | 'OFTALMICA' | 'OTRO';
  storageCondition: 'TEMPERATURA_AMBIENTE' | 'REFRIGERACION' | 'CONGELACION';
  sanitaryRegistration: string;
  isActive: boolean;
  stockAlert: boolean;
  expirationAlert: boolean;
  createdBy: Types.ObjectId;
}

export interface IProductDocument extends IProduct, Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  deleteImage?: boolean;
}

export interface IProductResponse
  extends Omit<IProduct, 'laboratory' | 'category' | 'createdBy'> {
  _id: Types.ObjectId;
  laboratory: {
    _id: Types.ObjectId;
    name: string;
  };
  category: {
    _id: Types.ObjectId;
    name: string;
  };
  createdBy: {
    _id: Types.ObjectId;
    username: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface IProductUpdate {
  name?: string;
  description?: string;
  image?: string;
  laboratory?: Types.ObjectId;
  category?: Types.ObjectId;
  price?: number;
  cost?: number;
  stock?: number;
  minStock?: number;
  location?: string;
  isActive?: boolean;
}

export interface IStockMovement {
  type: 'IN' | 'OUT' | 'RETURN';
  quantity: number;
  reason: string;
  reference?: string;
  batchNumber: string;
  expirationDate: Date;
  cost?: number;
}

export interface IStockMovementDocument extends IStockMovement, Document {
  _id: Types.ObjectId;
  productId: Types.ObjectId;
  createdBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

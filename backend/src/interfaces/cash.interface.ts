import { Types } from 'mongoose';
import {
  MovementType,
  CashRegisterStatus,
} from '../constants/cash.constants.js';

export interface ICashRegister {
  _id: Types.ObjectId;
  openingDate: Date;
  closingDate?: Date;
  initialBalance: number;
  finalBalance?: number;
  expectedBalance?: number;
  difference?: number;
  status: CashRegisterStatus;
  openedBy: Types.ObjectId;
  closedBy?: Types.ObjectId;
  closedAt?: Date;
  observations?: string;
  createdBy: Types.ObjectId;
  updatedBy?: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICashMovement {
  _id: Types.ObjectId;
  type: MovementType;
  amount: number;
  concept: string;
  reference?: string;
  observations?: string;
  cashRegister: Types.ObjectId;
  createdBy: Types.ObjectId;
  updatedBy?: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICashMovementDocument extends ICashMovement, Document {}

export interface CreateCashMovementDto {
  type: MovementType;
  amount: number;
  concept: string;
  reference?: string;
  observations?: string;
}

export interface UpdateCashMovementDto {
  reference?: string;
  observations?: string;
}

export interface CashMovementQueryParams {
  page?: number;
  limit?: number;
  type?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CashRegisterQueryParams {
  page?: number;
  limit?: number;
  status?: CashRegisterStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
export interface BalanceValidation {
  expectedBalance: number;
  providedBalance: number;
  difference: number;
  isBalanced: boolean;
}

export interface MovementTypeSummary {
  count: number;
  total: number;
}

export interface CashRegisterSummary {
  initialBalance: number;
  totalIncome: number;
  totalExpense: number;
  currentBalance: number;
  totalMovements: number;
  byType: {
    INCOME: MovementTypeSummary;
    EXPENSE: MovementTypeSummary;
  };
  incomeCount: number;
  expenseCount: number;
}

export interface CashMovementsResponse {
  movements: ICashMovement[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface CashRegistersResponse {
  registers: ICashRegister[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface CashRegisterEvent {
  registerId: string;
  userId: string;
  action: 'OPEN' | 'CLOSE' | 'UPDATE';
  timestamp: Date;
  details?: Record<string, any>;
}

export interface CashMovementEvent {
  movementId: string;
  registerId: string;
  userId: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  timestamp: Date;
  details?: Record<string, any>;
}

export interface CashRegisterHistoryDetail {
  register: ICashRegister;
  movements: ICashMovement[];
  summary: CashRegisterSummary;
}

export interface CashRegisterHistoryResponse {
  registers: ICashRegister[];
  totalRecords: number;
  totalPages: number;
  currentPage: number;
}

export interface ExportData {
  register: ICashRegister;
  movements: ICashMovement[];
  summary: CashRegisterSummary;
  metadata: {
    generatedAt: Date;
    totalMovements: number;
  };
}

import { Types } from 'mongoose';
import { DOCUMENT_TYPES } from '../constants/customer.constants.js';

export type DocumentType = typeof DOCUMENT_TYPES[keyof typeof DOCUMENT_TYPES];

export interface ICustomer {
  documentType: DocumentType;
  documentNumber: string;
  complement?: string;
  businessName: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive: boolean;
  createdBy: Types.ObjectId;
  updatedAt?: Date;
  createdAt: Date;
}

export interface ICustomerDocument extends ICustomer, Document {
  _id: Types.ObjectId;
}

export interface CreateCustomerDto {
  documentType: DocumentType;
  documentNumber: string;
  complement?: string;
  businessName: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface UpdateCustomerDto {
  businessName?: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive?: boolean;
}
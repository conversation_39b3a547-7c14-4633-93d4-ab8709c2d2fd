import { PERMISSIONS } from '../constants/permissions.constants.js';

export interface IUser {
  _id: string;
  username: string;
  password: string;
  role: string;
  permissions: string[];
  isSuperAdmin?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  isActive: boolean;
}

export interface IUserResponse extends Omit<IUser, 'password'> {}

// Crear VALID_PERMISSIONS a partir de PERMISSIONS
export const VALID_PERMISSIONS = [
  '*',
  ...Object.values(PERMISSIONS.INVENTORY.PRODUCTS),
  ...Object.values(PERMISSIONS.INVENTORY.STOCK.MOVEMENTS),
  ...Object.values(PERMISSIONS.INVENTORY.STOCK.ENTRIES),
  ...Object.values(PERMISSIONS.INVENTORY.STOCK.OUTPUTS),
  ...Object.values(PERMISSIONS.CUSTOMERS),
  ...Object.values(PERMISSIONS.SALES),
  ...Object.values(PERMISSIONS.CATEGORIES),
  ...Object.values(PERMISSIONS.LABORATORIES),
  ...Object.values(PERMISSIONS.USERS),
  PERMISSIONS.REPORTS.SALES.LIST,
  PERMISSIONS.REPORTS.INVENTORY.LIST,
  ...Object.values(PERMISSIONS.SETTINGS),
  ...Object.values(PERMISSIONS.CASH),
] as const;

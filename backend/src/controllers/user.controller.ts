import { Request, Response } from 'express';
import { User } from '../models/user.model.js';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { IUserResponse } from '../interfaces/user.interface.js';
import { USER_ERRORS, USER_MESSAGES } from '../constants/user.constants.js';
import { ROLE_PERMISSIONS } from '../constants/permissions.constants.js';
import { AUTH_ERRORS } from '../constants/auth.constants.js';
import { USER_ROLES } from '../constants/user.constants.js';
import { USER_VALIDATION } from '../constants/user.constants.js';
import { API_CONSTANTS } from '../constants/api.constants.js';
import { VALID_PERMISSIONS } from '../interfaces/user.interface.js';

/**
 * Controller handling user-related operations
 * Manages user CRUD operations, permissions, status changes and profile management
 */
export class UserController {
  /**
   * Get paginated list of users with optional filters
   * @param req Request object containing query parameters
   * @param res Response object
   * @returns Paginated list of users
   */
  public async getUsers(req: Request, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_PAGE;
      const limit =
        parseInt(req.query.limit as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
      const search = req.query.search as string;
      const role = req.query.role as string;

      const query: any = {};

      if (search) {
        query.username = { $regex: search, $options: 'i' };
      }

      if (role) {
        query.role = role;
      }

      const skip = (page - 1) * limit;

      const [users, total] = await Promise.all([
        User.find(query).select('-password').skip(skip).limit(limit).lean(),
        User.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return res.status(200).json({
        users,
        totalRecords: total,
        totalPages,
        currentPage: page,
      });
    } catch (error) {
      return res.status(500).json({ error: USER_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Create a new user
   * @param req Request object containing user data
   * @param res Response object
   * @returns Created user data
   */
  public async create(req: Request, res: Response) {
    try {
      const { username, password, role } = req.body;

      if (!Object.values(USER_ROLES).includes(role)) {
        return res.status(400).json({ error: USER_ERRORS.INVALID_ROLE });
      }

      const permissions =
        ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || [];

      const userDoc = await User.create({
        username,
        password,
        role,
        permissions,
      });

      const userResponse: IUserResponse = {
        _id: userDoc._id,
        username: userDoc.username,
        role: userDoc.role,
        permissions: userDoc.permissions,
        isSuperAdmin: userDoc.isSuperAdmin,
        createdAt: userDoc.createdAt,
        updatedAt: userDoc.updatedAt,
        isActive: userDoc.isActive,
      };

      return res.status(201).json(userResponse);
    } catch (error: any) {
      if (error.code === 11000 && error.keyPattern?.username) {
        return res.status(400).json({
          error: USER_ERRORS.USERNAME_TAKEN,
        });
      }

      return res.status(500).json({ error: USER_ERRORS.CREATION_FAILED });
    }
  }

  /**
   * Get current authenticated user profile
   * @param req AuthRequest object containing user data from JWT
   * @param res Response object
   * @returns Current user data
   */
  public async getCurrentUser(req: AuthRequest, res: Response) {
    try {
      if (!req.user?._id) {
        return res.status(401).json({ error: AUTH_ERRORS.UNAUTHORIZED });
      }

      const user = await User.findById(req.user._id).select('-password');
      if (!user) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      return res.status(200).json(user);
    } catch (error) {
      return res.status(500).json({ error: USER_ERRORS.PROFILE_FETCH_ERROR });
    }
  }

  /**
   * Update user information
   * @param req Request object containing user data
   * @param res Response object
   * @returns Updated user data
   */
  public async update(req: Request, res: Response) {
    try {
      const userId = req.params.id;
      const { username, role } = req.body;

      // Validate that the user exists
      const existingUser = await User.findById(userId);
      if (!existingUser) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      // Protection for superAdmin - prevent role or username changes
      if (existingUser.isSuperAdmin) {
        if (role && role !== existingUser.role) {
          return res.status(403).json({
            error: USER_ERRORS.SUPERADMIN_ROLE_CHANGE,
          });
        }

        // Block superAdmin username update attempts
        if (username && username !== existingUser.username) {
          return res.status(403).json({
            error: USER_ERRORS.SUPERADMIN_USERNAME_CHANGE,
          });
        }
      }

      // If username is being updated, check if it's already taken by another user
      if (username && username !== existingUser.username) {
        const usernameExists = await User.findOne({
          username,
          _id: { $ne: userId }, // Exclude current user from the search
        });
        if (usernameExists) {
          return res.status(400).json({
            error: USER_ERRORS.USERNAME_TAKEN,
          });
        }
      }

      // If the role is changed, update the permissions
      let updateData: any = { username };
      if (role && role !== existingUser.role) {
        const permissions =
          ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || [];
        updateData = { ...updateData, role, permissions };
      }

      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $set: updateData },
        { new: true }
      ).select('-password');

      if (!updatedUser) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      return res.status(200).json(updatedUser);
    } catch (error: any) {
      return res.status(500).json({ error: USER_ERRORS.UPDATE_FAILED });
    }
  }

  /**
   * Get user by ID
   * @param req Request object containing user ID in params
   * @param res Response object
   * @returns User data
   */
  public async getById(req: Request, res: Response) {
    try {
      const user = await User.findById(req.params.id).select('-password');
      if (!user) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }
      return res.status(200).json(user);
    } catch (error) {
      return res.status(500).json({ error: USER_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Delete user by ID
   * @param req Request object containing user ID in params
   * @param res Response object
   * @returns Deleted user confirmation
   */
  public async delete(req: Request, res: Response) {
    try {
      // First check if user exists and if it's superAdmin
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      // Protection for superAdmin
      if (user.isSuperAdmin) {
        return res.status(403).json({
          error: USER_ERRORS.SUPERADMIN_DELETE,
        });
      }

      // If not superAdmin, proceed with deletion
      await User.findByIdAndDelete(req.params.id);

      return res.status(200).json({
        message: USER_MESSAGES.DELETED,
        user: {
          _id: user._id,
          username: user.username,
        },
      });
    } catch (error) {
      return res.status(500).json({ error: USER_ERRORS.DELETION_FAILED });
    }
  }

  /**
   * Change user password
   * @param req Request object containing user ID and new password
   * @param res Response object
   * @returns Success message
   */
  public async changePassword(req: AuthRequest, res: Response) {
    try {
      const userId = req.params.id;
      const { newPassword } = req.body;

      if (
        !newPassword ||
        newPassword.length < USER_VALIDATION.PASSWORD_MIN_LENGTH
      ) {
        return res.status(400).json({
          error: USER_ERRORS.INVALID_PASSWORD_LENGTH,
        });
      }

      // Validate if the user exists
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      // Protection for superAdmin - only he can change this own password
      if (
        user.isSuperAdmin &&
        req.user?._id.toString() !== user._id.toString()
      ) {
        return res.status(403).json({
          error: USER_ERRORS.SUPERADMIN_PASSWORD,
        });
      }

      // Update password
      user.password = newPassword;
      // Save and hash the new password
      await user.save();

      return res.status(200).json({
        message: USER_MESSAGES.PASSWORD_UPDATED,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ error: USER_ERRORS.PASSWORD_CHANGE_FAILED });
    }
  }

  /**
   * Update user permissions
   * @param req Request object containing user ID and permissions array
   * @param res Response object
   * @returns Updated user with new permissions
   */
  public async updatePermissions(req: Request, res: Response) {
    try {
      const userId = req.params.id;
      const { permissions } = req.body;

      // Validate if permissions is an array
      if (!Array.isArray(permissions)) {
        return res.status(400).json({ error: USER_ERRORS.INVALID_PERMISSIONS });
      }

      // Validate if all permissions are valid
      const invalidPermissions = permissions.filter(
        (permission) => !VALID_PERMISSIONS.includes(permission)
      );

      if (invalidPermissions.length > 0) {
        return res.status(400).json({
          error: USER_ERRORS.INVALID_PERMISSIONS,
          invalidPermissions,
        });
      }

      // Validate if the user exists
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      // Protection for superAdmin - no allow changes in permissions
      if (user.isSuperAdmin) {
        return res.status(403).json({
          error: USER_ERRORS.SUPERADMIN_PERMISSIONS,
        });
      }

      // Update permissions
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $set: { permissions } },
        { new: true }
      ).select('-password');

      if (!updatedUser) {
        return res.status(404).json({ error: AUTH_ERRORS.USER_NOT_FOUND });
      }

      return res.status(200).json({
        message: USER_MESSAGES.PERMISSIONS_UPDATED,
        user: updatedUser,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ error: USER_ERRORS.PERMISSIONS_UPDATE_ERROR });
    }
  }

  /**
   * Toggle user active status
   * @param req Request object containing user ID and isActive status
   * @param res Response object
   * @returns Updated user with new status
   */
  public async toggleUserStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { isActive } = req.body;
      const authReq = req as AuthRequest;

      // Check if user exists
      const user = await User.findById(id);
      if (!user) {
        return res.status(404).json({
          error: AUTH_ERRORS.USER_NOT_FOUND,
        });
      }

      // Protection for superAdmin
      if (user.isSuperAdmin) {
        return res.status(403).json({
          error: USER_ERRORS.SUPERADMIN_STATUS,
        });
      }

      // Prevent user from deactivating themselves
      if (id === authReq.user?._id.toString()) {
        return res.status(400).json({
          error: USER_ERRORS.SELF_STATUS_CHANGE,
        });
      }

      // Update user status
      const updatedUser = await User.findByIdAndUpdate(
        id,
        { $set: { isActive } },
        { new: true }
      ).select('-password');

      if (!updatedUser) {
        return res.status(404).json({
          error: AUTH_ERRORS.USER_NOT_FOUND,
        });
      }

      return res.status(200).json({
        message: USER_MESSAGES.STATUS_UPDATED(isActive),
        user: updatedUser,
      });
    } catch (error) {
      return res.status(500).json({
        error: USER_ERRORS.STATUS_UPDATE_ERROR,
      });
    }
  }
}

export const userController = new UserController();

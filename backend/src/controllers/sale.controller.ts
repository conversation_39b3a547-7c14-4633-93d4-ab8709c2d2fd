import { Request, Response, NextFunction } from 'express';
import { saleService } from '../services/sale.service.js';
import { SALE_MESSAGES } from '../constants/sale.constants.js';
import type { AuthRequest } from '../interfaces/auth.interface.js';
import type {
  CreateSaleDto,
  CreateSaleItemDto,
} from '../interfaces/sale.interface.js';

export class SaleController {
  async createSale(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user!._id.toString();
      const cashRegisterId =
        typeof req.body.cashRegisterId === 'object'
          ? req.body.cashRegisterId.toString()
          : req.body.cashRegisterId;

      const saleData: CreateSaleDto = {
        customerId: req.body.customerId,
        items: req.body.items.map((item: CreateSaleItemDto) => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount || 0,
        })),
        paymentMethod: req.body.paymentMethod,
        discountType: req.body.discountType || 'percentage',
        discount: req.body.discount || 0,
        notes: req.body.notes,
        amountReceived: req.body.amountReceived,
        cashRegisterId: cashRegisterId,
      };

      const sale = await saleService.createSale(
        saleData,
        userId,
        cashRegisterId
      );

      if (!sale) {
        throw new Error('Error al crear la venta');
      }

      res.status(201).json({
        message: SALE_MESSAGES.CREATED,
        sale: {
          ...sale.toObject(),
          number: sale.number,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async updateSale(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!._id.toString();
      const sale = await saleService.updateSale(id, req.body, userId);

      res.json({
        message: SALE_MESSAGES.UPDATED,
        sale: sale,
      });
    } catch (error) {
      next(error);
    }
  }

  async cancelSale(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!._id.toString();
      const cancelData = {
        reason: req.body.reason,
        notes: req.body.notes,
        canceledAt: req.body.canceledAt || new Date(),
      };

      const sale = await saleService.cancelSale(id, cancelData, userId);

      res.json({
        message: SALE_MESSAGES.CANCELED,
        sale: sale,
      });
    } catch (error) {
      next(error);
    }
  }

  async getSaleById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const sale = await saleService.getSaleById(id);
      res.json({ sale: sale });
    } catch (error) {
      next(error);
    }
  }

  async getSales(req: Request, res: Response, next: NextFunction) {
    try {
      const sales = await saleService.getSales(req.query);
      res.json({ sales: sales });
    } catch (error) {
      next(error);
    }
  }

  async deleteSale(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const userId = req.user!._id.toString();

      await saleService.deleteSale(id, userId);

      res.json({
        message: SALE_MESSAGES.DELETED,
      });
    } catch (error) {
      next(error);
    }
  }
}

export const saleController = new SaleController();

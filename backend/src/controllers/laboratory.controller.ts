import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { Laboratory } from '../models/laboratory.model.js';
import { Product } from '../models/product.model.js';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { API_CONSTANTS } from '../constants/api.constants.js';
import {
  LABORATORY_ERRORS,
  LABORATORY_MESSAGES,
} from '../constants/laboratory.constants.js';

const NO_LABORATORY_ID = process.env.NO_LABORATORY_ID;

export class LaboratoryController {
  public async getAllLaboratories(req: Request, res: Response) {
    try {
      const isActive =
        req.query.isActive === 'true'
          ? true
          : req.query.isActive === 'false'
          ? false
          : undefined;

      const query: any = {};

      if (typeof isActive === 'boolean') {
        query.isActive = isActive;
      }
      const laboratories = await Laboratory.find(query)
        .populate('createdBy', '_id username')
        .sort({ businessName: 1 })
        .lean();

      return res.status(200).json({ laboratories });
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Get paginated list of laboratories with optional search
   */
  public async getLaboratories(req: Request, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_PAGE;
      const limit =
        parseInt(req.query.limit as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
      const search = req.query.search as string;

      const query: any = {};

      if (search) {
        query.name = { $regex: search, $options: 'i' };
      }

      const skip = (page - 1) * limit;

      const [laboratories, total] = await Promise.all([
        Laboratory.find(query)
          .select(
            'name description country website isActive createdAt updatedAt'
          )
          .sort({ name: 1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Laboratory.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return res.status(200).json({
        laboratories,
        totalRecords: total,
        totalPages,
        currentPage: page,
      });
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Get only active laboratories (no pagination)
   */
  public async getActiveLaboratories(req: Request, res: Response) {
    try {
      const laboratories = await Laboratory.find({ isActive: true })
        .select('name description country website')
        .sort({ name: 1 })
        .lean();

      return res.status(200).json({ laboratories });
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Create a new laboratory
   */
  public async createLaboratory(req: AuthRequest, res: Response) {
    try {
      const { name, description, country, website } = req.body;

      if (!req.user?._id) {
        return res.status(401).json({ error: 'Usuario no autenticado' });
      }

      const existingLaboratory = await Laboratory.findOne({ name });
      if (existingLaboratory) {
        return res.status(400).json({ error: LABORATORY_ERRORS.NAME_TAKEN });
      }

      const laboratory = await Laboratory.create({
        name,
        description,
        country,
        website,
        isActive: true,
        createdBy: new Types.ObjectId(req.user._id),
      });

      return res.status(201).json(laboratory);
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.CREATION_FAILED });
    }
  }

  /**
   * Update an existing laboratory
   */
  public async updateLaboratory(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { name, description, country, website } = req.body;

      const existingLaboratory = await Laboratory.findById(id);
      if (!existingLaboratory) {
        return res.status(404).json({ error: LABORATORY_ERRORS.NOT_FOUND });
      }

      if (name && name !== existingLaboratory.name) {
        const nameExists = await Laboratory.findOne({
          name,
          _id: { $ne: id },
        });
        if (nameExists) {
          return res.status(400).json({ error: LABORATORY_ERRORS.NAME_TAKEN });
        }
      }

      const updatedLaboratory = await Laboratory.findByIdAndUpdate(
        id,
        { name, description, country, website },
        { new: true }
      );

      return res.status(200).json(updatedLaboratory);
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.UPDATE_FAILED });
    }
  }

  /**
   * Delete a laboratory
   */
  public async deleteLaboratory(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const laboratory = await Laboratory.findById(id);
      if (!laboratory) {
        return res.status(404).json({ error: LABORATORY_ERRORS.NOT_FOUND });
      }

      // Protection for "Sin laboratorio" laboratory
      if (laboratory.isProtected) {
        return res.status(400).json({
          error: LABORATORY_ERRORS.PROTECTED_LABORATORY,
        });
      }

      // Move products to "Sin laboratorio" laboratory and get the count
      const productsCount = await Product.countDocuments({ laboratory: id });

      if (productsCount > 0) {
        await Product.updateMany(
          { laboratory: id },
          { laboratory: NO_LABORATORY_ID }
        );
      }

      // Delete the laboratory
      await Laboratory.findByIdAndDelete(id);

      return res.status(200).json({
        message: LABORATORY_MESSAGES.DELETED,
        laboratory: {
          _id: laboratory._id,
          name: laboratory.name,
        },
        details:
          productsCount > 0
            ? {
                affectedProducts: productsCount,
                message: LABORATORY_MESSAGES.PRODUCTS_MOVED(productsCount),
              }
            : undefined,
      });
    } catch (error) {
      return res.status(500).json({ error: LABORATORY_ERRORS.DELETION_FAILED });
    }
  }

  /**
   * Toggle laboratory active status
   */
  public async toggleLaboratoryStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { isActive } = req.body;

      const laboratory = await Laboratory.findById(id);
      if (!laboratory) {
        return res.status(404).json({ error: LABORATORY_ERRORS.NOT_FOUND });
      }

      const updatedLaboratory = await Laboratory.findByIdAndUpdate(
        id,
        { isActive },
        { new: true }
      );

      return res.status(200).json({
        message: LABORATORY_MESSAGES.STATUS_UPDATED(isActive),
        laboratory: updatedLaboratory,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ error: LABORATORY_ERRORS.STATUS_UPDATE_FAILED });
    }
  }
}

export const laboratoryController = new LaboratoryController();

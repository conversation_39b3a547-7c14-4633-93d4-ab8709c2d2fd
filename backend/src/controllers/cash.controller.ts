import { Request, Response } from 'express';
import { cashService } from '../services/cash.service.js';
import { CASH_ERRORS, CASH_MESSAGES } from '../constants/cash.constants.js';
import type { AuthRequest } from '../interfaces/auth.interface.js';
import { CashRegister } from '../models/cashRegister.model.js';

export class CashController {
  /**
   * Get current open cash register
   */
  public async getCurrentRegister(req: Request, res: Response) {
    try {
      const register = await cashService.getCurrentRegister();
      return res.status(200).json(register);
    } catch (error) {
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Open a new cash register
   */
  public async openCashRegister(req: AuthRequest, res: Response) {
    try {
      const { initialBalance, observations } = req.body;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const register = await cashService.openCashRegister(
        userId,
        initialBalance,
        observations
      );

      return res.status(201).json({
        message: CASH_MESSAGES.REGISTER_OPENED,
        register,
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_ALREADY_OPEN) {
        return res.status(400).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.CREATION_ERROR });
    }
  }

  /**
   * Close an existing cash register
   */
  public async closeCashRegister(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const { finalBalance, observations, forceClose = false } = req.body;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const register = await cashService.closeCashRegister(
        id,
        userId,
        finalBalance,
        observations,
        forceClose
      );

      return res.status(200).json({
        message: CASH_MESSAGES.REGISTER_CLOSED,
        register,
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.CLOSE_ERROR });
    }
  }

  /**
   * Create a new cash movement
   */
  public async createCashMovement(req: AuthRequest, res: Response) {
    try {
      const { registerId } = req.params;
      const movementData = req.body;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const movement = await cashService.createCashMovement(
        userId,
        registerId,
        movementData
      );

      return res.status(201).json({
        message: CASH_MESSAGES.MOVEMENT_CREATED,
        movement,
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_OPEN) {
        return res.status(400).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.CREATION_ERROR });
    }
  }

  /**
   * Get cash register balance
   */
  public async getRegisterBalance(req: Request, res: Response) {
    try {
      const { registerId } = req.params;
      const balance = await cashService.calculateRegisterBalance(registerId);

      return res.status(200).json({ balance });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.CALCULATION_ERROR });
    }
  }

  /**
   * Validate closing balance
   */
  public async validateClosingBalance(req: Request, res: Response) {
    try {
      const { registerId } = req.params;
      const { finalBalance } = req.body;

      const validation = await cashService.validateClosingBalance(
        registerId,
        finalBalance
      );

      return res.status(200).json(validation);
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.VALIDATION_ERROR });
    }
  }

  /**
   * Get register summary
   */
  public async getRegisterSummary(req: Request, res: Response) {
    try {
      const { registerId } = req.params;
      const summary = await cashService.getRegisterSummary(registerId);

      return res.status(200).json(summary);
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Get cash movements
   */
  public async getCashMovements(req: Request, res: Response) {
    try {
      const { registerId } = req.params;
      const query = req.query;

      const movements = await cashService.getCashMovements(registerId, query);

      return res.status(200).json(movements);
    } catch (error) {
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Update cash movement
   */
  public async updateCashMovement(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const movementData = req.body;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const movement = await cashService.updateCashMovement(
        id,
        movementData,
        userId
      );

      return res.status(200).json({
        message: CASH_MESSAGES.MOVEMENT_UPDATED,
        movement,
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.MOVEMENT_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      if (error.message === CASH_ERRORS.REGISTER_NOT_OPEN) {
        return res.status(400).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.UPDATE_ERROR });
    }
  }

  /**
   * Delete cash movement
   */
  public async deleteCashMovement(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      await cashService.deleteCashMovement(id, userId);

      return res.status(200).json({
        message: CASH_MESSAGES.MOVEMENT_DELETED,
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.MOVEMENT_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      if (error.message === CASH_ERRORS.MOVEMENT_LOCKED) {
        return res.status(400).json({ error: error.message });
      }
      if (error.message === CASH_ERRORS.UNAUTHORIZED_DELETE) {
        return res.status(403).json({ error: error.message });
      }
      console.error('Error in deleteCashMovement:', error);
      return res.status(500).json({ error: CASH_ERRORS.DELETE_ERROR });
    }
  }

  /**
   * Get cash registers history
   */
  public async getCashRegistersHistory(req: Request, res: Response) {
    try {
      const history = await cashService.getCashRegistersHistory(req.query);
      return res.status(200).json(history);
    } catch (error) {
      console.error('Error fetching cash registers history:', error);
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }

  public async getCashRegisterDetail(req: Request, res: Response) {
    try {
      const { registerId } = req.params;
      const detail = await cashService.getRegisterDetails(registerId); // Cambiado de getCashRegisterDetail a getRegisterDetails
      return res.status(200).json(detail);
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      console.error('Error fetching cash register detail:', error);
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Export cash movements
   */
  public async exportCashMovements(req: AuthRequest, res: Response) {
    try {
      const { registerId } = req.params;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const data = await cashService.getAllCashMovementsForExport(registerId);
      return res.status(200).json(data);
    } catch (error: any) {
      console.error('Export error:', error);
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.EXPORT_ERROR });
    }
  }

  public async deleteCashRegister(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?._id.toString();

      if (!userId) {
        return res
          .status(401)
          .json({ error: CASH_ERRORS.UNAUTHORIZED_OPERATION });
      }

      const result = await cashService.deleteCashRegister(id);

      return res.status(200).json({
        message: CASH_MESSAGES.REGISTER_DELETED,
        details: {
          registerId: result.registerId,
          movementsDeleted: result.movements,
        },
      });
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CASH_ERRORS.DELETE_ERROR });
    }
  }

  public async getCashRegisters(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        startDate,
        endDate,
        status,
        search,
      } = req.query;

      const query: any = {};

      // Manejo de fechas similar al stock.controller
      if (startDate || endDate) {
        query.openingDate = {};
        if (startDate) {
          const start = new Date(startDate as string);
          start.setUTCHours(0, 0, 0, 0);
          query.openingDate.$gte = start;
        }
        if (endDate) {
          const end = new Date(endDate as string);
          end.setUTCHours(23, 59, 59, 999);
          query.openingDate.$lte = end;
        }
      }

      if (status && status !== 'ALL') {
        query.status = status;
      }

      if (search) {
        query.$or = [
          { 'openedBy.username': { $regex: search, $options: 'i' } },
          { 'closedBy.username': { $regex: search, $options: 'i' } },
          { observations: { $regex: search, $options: 'i' } },
        ];
      }

      const [registers, total] = await Promise.all([
        CashRegister.find(query)
          .populate('openedBy', 'username')
          .populate('closedBy', 'username')
          .sort({ openingDate: -1 })
          .skip((Number(page) - 1) * Number(limit))
          .limit(Number(limit))
          .lean(),
        CashRegister.countDocuments(query),
      ]);

      return res.status(200).json({
        registers,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
        totalRecords: total,
      });
    } catch (error) {
      console.error('Error fetching cash registers:', error);
      return res
        .status(500)
        .json({ error: 'Error al obtener los registros de caja' });
    }
  }

  /**
   * Get register details by ID
   */
  public async getRegisterDetails(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const register = await cashService.getRegisterDetails(id);
      return res.status(200).json(register);
    } catch (error: any) {
      if (error.message === CASH_ERRORS.REGISTER_NOT_FOUND) {
        return res.status(404).json({ error: error.message });
      }
      console.error('Error fetching register details:', error);
      return res.status(500).json({ error: CASH_ERRORS.FETCH_ERROR });
    }
  }
}

export const cashController = new CashController();

import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/auth.service.js';
import { AUTH_ERRORS } from '../constants/auth.constants.js';
import { ERROR_MESSAGES } from '../constants/error.constants.js';
import type {
  LoginCredentials,
  LoginResponse,
} from '../interfaces/auth.interface.js';

/**
 * Controller handling authentication-related operations
 */
export class AuthController {
  /**
   * Authenticate user and generate JWT token
   */
  public async login(
    req: Request<unknown, unknown, LoginCredentials>,
    res: Response<LoginResponse | { error: string }>,
    next: NextFunction
  ) {
    try {
      const response = await authService.login(req.body);
      return res.status(200).json(response);
    } catch (error: any) {
      if (error.message === AUTH_ERRORS.INVALID_CREDENTIALS) {
        return res.status(401).json({ error: AUTH_ERRORS.INVALID_CREDENTIALS });
      }
      if (error.message === AUTH_ERRORS.USER_INACTIVE) {
        return res.status(401).json({ error: AUTH_ERRORS.USER_INACTIVE });
      }

      return res
        .status(500)
        .json({ error: ERROR_MESSAGES.SERVER.INTERNAL_ERROR });
    }
  }
}

export const authController = new AuthController();

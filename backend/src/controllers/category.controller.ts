import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { Category } from '../models/category.model.js';
import { Product } from '../models/product.model.js';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { API_CONSTANTS } from '../constants/api.constants.js';
import {
  CATEGORY_ERRORS,
  CATEGORY_MESSAGES,
} from '../constants/category.constants.js';

const UNCATEGORIZED_CATEGORY_ID = process.env.NO_CATEGORY_ID;

export class CategoryController {
  public async getAllCategories(req: Request, res: Response) {
    try {
      const isActive =
        req.query.isActive === 'true'
          ? true
          : req.query.isActive === 'false'
          ? false
          : undefined;

      const query: any = {};

      if (typeof isActive === 'boolean') {
        query.isActive = isActive;
      }
      const categories = await Category.find(query)
        .populate('createdBy', '_id username')
        .sort({ businessName: 1 })
        .lean();

      return res.status(200).json({ categories }); // Corregido aquí
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Get paginated list of categories with optional search
   */
  public async getCategories(req: Request, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_PAGE;
      const limit =
        parseInt(req.query.limit as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
      const search = req.query.search as string;

      const query: any = {};

      if (search) {
        query.name = { $regex: search, $options: 'i' };
      }

      const skip = (page - 1) * limit;

      const [categories, total] = await Promise.all([
        Category.find(query)
          .select('name description isActive createdAt updatedAt')
          .sort({ name: 1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Category.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return res.status(200).json({
        categories,
        totalRecords: total,
        totalPages,
        currentPage: page,
      });
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Get only active categories (no pagination)
   */
  public async getActiveCategories(req: Request, res: Response) {
    try {
      const categories = await Category.find({ isActive: true })
        .select('name description')
        .sort({ name: 1 })
        .lean();

      return res.status(200).json({ categories });
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.FETCH_ERROR });
    }
  }

  /**
   * Create a new category
   */
  public async createCategory(req: AuthRequest, res: Response) {
    try {
      const { name, description } = req.body;

      if (!req.user?._id) {
        return res.status(401).json({ error: 'Usuario no autenticado' });
      }

      const existingCategory = await Category.findOne({ name });
      if (existingCategory) {
        return res.status(400).json({ error: CATEGORY_ERRORS.NAME_TAKEN });
      }

      const category = await Category.create({
        name,
        description,
        isActive: true,
        createdBy: new Types.ObjectId(req.user._id),
      });

      return res.status(201).json(category);
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.CREATION_FAILED });
    }
  }

  /**
   * Update an existing category
   */
  public async updateCategory(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { name, description } = req.body;

      // Check if category exists
      const existingCategory = await Category.findById(id);
      if (!existingCategory) {
        return res.status(404).json({ error: CATEGORY_ERRORS.NOT_FOUND });
      }

      // Check if new name is already taken by another category
      if (name && name !== existingCategory.name) {
        const nameExists = await Category.findOne({
          name,
          _id: { $ne: id },
        });
        if (nameExists) {
          return res.status(400).json({ error: CATEGORY_ERRORS.NAME_TAKEN });
        }
      }

      const updatedCategory = await Category.findByIdAndUpdate(
        id,
        { name, description },
        { new: true }
      );

      return res.status(200).json(updatedCategory);
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.UPDATE_FAILED });
    }
  }

  /**
   * Delete a category with detailed validation
   */
  public async deleteCategory(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const category = await Category.findById(id);
      if (!category) {
        return res.status(404).json({ error: CATEGORY_ERRORS.NOT_FOUND });
      }

      // Protection for "Uncategorized" category
      if (category.isProtected) {
        return res.status(400).json({
          error: CATEGORY_ERRORS.PROTECTED_CATEGORY,
        });
      }

      // Move products to "Uncategorized" category and get the count
      const productsCount = await Product.countDocuments({ category: id });

      if (productsCount > 0) {
        await Product.updateMany(
          { category: id },
          { category: UNCATEGORIZED_CATEGORY_ID }
        );
      }

      await Category.findByIdAndDelete(id);

      return res.status(200).json({
        message: CATEGORY_MESSAGES.DELETED,
        category: {
          _id: category._id,
          name: category.name,
        },
        details:
          productsCount > 0
            ? {
                movedProducts: productsCount,
                message: CATEGORY_MESSAGES.PRODUCTS_MOVED(productsCount),
              }
            : undefined,
      });
    } catch (error) {
      return res.status(500).json({ error: CATEGORY_ERRORS.DELETION_FAILED });
    }
  }

  /**
   * Toggle category active status
   */
  public async toggleCategoryStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { isActive } = req.body;

      const category = await Category.findById(id);
      if (!category) {
        return res.status(404).json({ error: CATEGORY_ERRORS.NOT_FOUND });
      }

      const updatedCategory = await Category.findByIdAndUpdate(
        id,
        { isActive },
        { new: true }
      );

      return res.status(200).json({
        message: CATEGORY_MESSAGES.STATUS_UPDATED(isActive),
        category: updatedCategory,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ error: CATEGORY_ERRORS.STATUS_UPDATE_FAILED });
    }
  }
}

export const categoryController = new CategoryController();

import { Request, Response } from 'express';
import * as fs from 'fs/promises';
import * as path from 'path';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { Product } from '../models/product.model.js';
import {
  PRODUCT_ERRORS,
  PRODUCT_MESSAGES,
} from '../constants/product.constants.js';
import { API_CONSTANTS } from '../constants/api.constants.js';

export class ProductController {
  private UPLOADS_DIR = 'uploads/products';

  private async deleteProductImage(
    imagePath: string | null | undefined
  ): Promise<void> {
    if (!imagePath) return;

    try {
      const fullPath = path.join(process.cwd(), imagePath);
      await fs.access(fullPath);
      await fs.unlink(fullPath);
    } catch (error) {
      console.error('Error deleting product image:', error);
    }
  }

  private async handleImageUpload(
    file: Express.Multer.File | undefined
  ): Promise<string | null> {
    if (!file) return null;
    return `${this.UPLOADS_DIR}/${file.filename}`;
  }

  private async cleanupOnError(
    imagePath: string | null,
    error: any
  ): Promise<void> {
    if (imagePath) {
      await this.deleteProductImage(imagePath);
    }
    console.error('Error in product operation:', error);
  }

  public async getProducts(req: Request, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_PAGE;
      const limit =
        parseInt(req.query.limit as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
      const search = req.query.search as string;
      const category = req.query.category as string;
      const laboratory = req.query.laboratory as string;
      const showInactive = req.query.showInactive === 'true';

      const query: any = {};

      // Solo agregar isActive al query si showInactive es false
      if (!showInactive) {
        query.isActive = true;
      }

      if (search) {
        query.$text = { $search: search };
      }

      // Solo agregar category/laboratory si no son 'ALL'
      if (category && category !== 'ALL') {
        query.category = category;
      }

      if (laboratory && laboratory !== 'ALL') {
        query.laboratory = laboratory;
      }

      const [products, total] = await Promise.all([
        Product.find(query)
          .populate(['category', 'laboratory'])
          .skip((page - 1) * limit)
          .limit(limit)
          .sort({ createdAt: -1 }),
        Product.countDocuments(query),
      ]);

      return res.status(200).json({
        products,
        totalRecords: total,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      });
    } catch (error) {
      console.error('Error in getProducts:', error);
      return res.status(500).json({ error: PRODUCT_ERRORS.FETCH_ERROR });
    }
  }

  public async getProductById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const product = await Product.findById(id).populate([
        'category',
        'laboratory',
      ]);

      if (!product) {
        return res.status(404).json({ error: PRODUCT_ERRORS.NOT_FOUND });
      }

      return res.status(200).json(product);
    } catch (error) {
      return res.status(500).json({ error: PRODUCT_ERRORS.FETCH_ERROR });
    }
  }

  public async create(req: AuthRequest, res: Response) {
    let uploadedImagePath: string | null = null;

    try {
      const productData = req.body;
      uploadedImagePath = await this.handleImageUpload(req.file);

      if (uploadedImagePath) {
        productData.image = uploadedImagePath;
      }

      if (!productData.sku || productData.sku.trim() === '') {
        delete productData.sku;
      }

      const product = await Product.create({
        ...productData,
        createdBy: req.user!._id,
      });

      await Promise.all([
        product.populate('category', 'name description'),
        product.populate('laboratory', 'name'),
        product.populate('createdBy', 'username'),
      ]);

      return res.status(201).json({
        message: PRODUCT_MESSAGES.CREATED,
        product,
      });
    } catch (error: any) {
      if (error.code === 11000 && error.keyPattern?.sku) {
        await this.cleanupOnError(uploadedImagePath, PRODUCT_ERRORS.SKU_TAKEN);
        return res.status(400).json({ error: PRODUCT_ERRORS.SKU_TAKEN });
      }
      await this.cleanupOnError(uploadedImagePath, error);
      return res.status(500).json({ error: PRODUCT_ERRORS.CREATION_FAILED });
    }
  }

  public async update(req: AuthRequest, res: Response) {
    let uploadedImagePath: string | null = null;

    try {
      const { id } = req.params;
      const updateData = { ...req.body };
      const shouldDeleteImage = updateData.deleteImage === 'true';

      const product = await Product.findById(id);
      if (!product) {
        if (req.file) {
          await this.deleteProductImage(
            `${this.UPLOADS_DIR}/${req.file.filename}`
          );
        }
        return res.status(404).json({ error: PRODUCT_ERRORS.NOT_FOUND });
      }

      if (!updateData.sku || updateData.sku.trim() === '') {
        delete updateData.sku;
      }

      if (updateData.sku && updateData.sku !== product.sku) {
        const skuExists = await Product.findOne({
          sku: updateData.sku,
          _id: { $ne: id },
        });
        if (skuExists) {
          if (req.file) {
            await this.deleteProductImage(
              `${this.UPLOADS_DIR}/${req.file.filename}`
            );
          }
          return res.status(400).json({ error: PRODUCT_ERRORS.SKU_TAKEN });
        }
      }

      if (shouldDeleteImage || req.file) {
        if (product.image) {
          await this.deleteProductImage(product.image);
        }
        if (req.file) {
          uploadedImagePath = `${this.UPLOADS_DIR}/${req.file.filename}`;
          updateData.image = uploadedImagePath;
        } else {
          updateData.image = null;
        }
      }

      const updatedProduct = await Product.findByIdAndUpdate(
        id,
        { $set: updateData },
        { new: true }
      ).populate(['category', 'laboratory']);

      return res.status(200).json({
        message: PRODUCT_MESSAGES.UPDATED,
        product: updatedProduct,
      });
    } catch (error) {
      if (uploadedImagePath) {
        await this.deleteProductImage(uploadedImagePath);
      }
      return res.status(500).json({ error: PRODUCT_ERRORS.UPDATE_FAILED });
    }
  }

  public async delete(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const product = await Product.findById(id).populate('category', 'name');
      if (!product) {
        return res.status(404).json({ error: PRODUCT_ERRORS.NOT_FOUND });
      }

      if (product.image) {
        await this.deleteProductImage(product.image);
      }

      await Product.findByIdAndDelete(id);

      return res.json({
        message: PRODUCT_MESSAGES.DELETED,
        product: {
          _id: product._id,
          name: product.name,
          category: product.category,
        },
      });
    } catch (error) {
      return res.status(500).json({ error: PRODUCT_ERRORS.DELETION_FAILED });
    }
  }

  public async getInventoryDashboard(req: Request, res: Response) {
    try {
      const [totalProducts, lowStockProducts, activeProducts] =
        await Promise.all([
          Product.countDocuments(),
          Product.countDocuments({
            $expr: { $lte: ['$stock', '$minStock'] },
            isActive: true,
          }),
          Product.countDocuments({ isActive: true }),
        ]);

      return res.status(200).json({
        summary: {
          totalProducts,
          lowStockProducts,
          activeProducts,
        },
      });
    } catch (error) {
      return res.status(500).json({ error: PRODUCT_ERRORS.FETCH_ERROR });
    }
  }

  public async getProductsForSale(req: Request, res: Response) {
    try {
      const products = await Product.find({ isActive: true })
        .select('name sku price stock category laboratory')
        .populate([
          { path: 'category', select: 'name' },
          { path: 'laboratory', select: 'name' },
        ]);

      return res.status(200).json({ products });
    } catch (error) {
      return res.status(500).json({ error: PRODUCT_ERRORS.FETCH_ERROR });
    }
  }
}

export const productController = new ProductController();

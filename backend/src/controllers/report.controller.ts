import { Request, Response, NextFunction } from 'express';
import { reportService } from '../services/report.service.js';
import { REPORT_MESSAGES } from '../constants/report.constants.js';
import type { AuthRequest } from '../interfaces/auth.interface.js';

export class ReportController {
  async getSalesReport(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const userId = req.user!._id.toString();
      const { startDate, endDate, ...restQuery } = req.query;

      const filters = {
        ...restQuery,
        startDate: startDate
          ? (() => {
              const start = new Date(startDate as string);
              start.setUTCHours(0, 0, 0, 0);
              return start;
            })()
          : undefined,
        endDate: endDate
          ? (() => {
              const end = new Date(endDate as string);
              end.setUTCHours(23, 59, 59, 999);
              return end;
            })()
          : undefined,
      };

      const report = await reportService.getSalesReport(filters, userId);

      res.json({
        message: REPORT_MESSAGES.SALES_REPORT_GENERATED,
        ...report,
      });
    } catch (error) {
      next(error);
    }
  }

  async getInventoryReport(
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ) {
    try {
      const userId = req.user!._id.toString();
      const { stockAlert, expirationAlert, isActive, ...restFilters } =
        req.query;

      // Construir el objeto de filtros
      const filters = {
        ...restFilters,
        $and: [
          // Filtro base para productos activos/inactivos
          { isActive: isActive === 'true' },
          // Filtro condicional para stock bajo
          ...(stockAlert === 'true'
            ? [
                {
                  $expr: { $lte: ['$stock', '$minStock'] },
                },
              ]
            : []),
          // Filtro condicional para productos por vencer
          ...(expirationAlert === 'true'
            ? [
                {
                  expirationAlert: true,
                },
              ]
            : []),
        ],
      };

      const report = await reportService.getInventoryReport(filters, userId);

      res.json({
        message: REPORT_MESSAGES.INVENTORY_REPORT_GENERATED,
        ...report,
      });
    } catch (error) {
      next(error);
    }
  }
}

export const reportController = new ReportController();

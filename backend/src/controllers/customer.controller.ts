import { Request, Response } from 'express';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { customerService } from '../services/customer.service.js';
import { API_CONSTANTS } from '../constants/api.constants.js';
import {
  CUSTOMER_ERRORS,
  CUSTOMER_MESSAGES,
} from '../constants/customer.constants.js';

export class CustomerController {
  public async getAllCustomers(req: Request, res: Response) {
    try {
      const isActive =
        req.query.isActive === 'true'
          ? true
          : req.query.isActive === 'false'
          ? false
          : undefined;

      const result = await customerService.getAllCustomers(isActive);
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ error: CUSTOMER_ERRORS.FETCH_ERROR });
    }
  }

  public async getCustomers(req: Request, res: Response) {
    try {
      const page =
        parseInt(req.query.page as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_PAGE;
      const limit =
        parseInt(req.query.limit as string) ||
        API_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
      const search = req.query.search as string;

      const result = await customerService.getCustomers({
        search,
        page,
        limit,
      });
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ error: CUSTOMER_ERRORS.FETCH_ERROR });
    }
  }

  public async createCustomer(req: AuthRequest, res: Response) {
    try {
      if (!req.user?._id) {
        return res.status(401).json({ error: 'Usuario no autenticado' });
      }

      const customer = await customerService.createCustomer(
        req.body,
        req.user._id
      );
      return res.status(201).json({
        message: CUSTOMER_MESSAGES.CREATED,
        customer,
      });
    } catch (error) {
      if (error instanceof Error) {
        return res.status(400).json({ error: error.message });
      }
      return res.status(500).json({ error: CUSTOMER_ERRORS.CREATION_FAILED });
    }
  }

  public async updateCustomer(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const customer = await customerService.updateCustomer(id, req.body);
      return res.status(200).json({
        message: CUSTOMER_MESSAGES.UPDATED,
        customer,
      });
    } catch (error) {
      if (error instanceof Error) {
        return res.status(400).json({ error: error.message });
      }
      return res.status(500).json({ error: CUSTOMER_ERRORS.UPDATE_FAILED });
    }
  }

  public async getCustomerById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const customer = await customerService.getCustomerById(id);
      return res.status(200).json(customer);
    } catch (error) {
      if (error instanceof Error) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CUSTOMER_ERRORS.FETCH_ERROR });
    }
  }

  public async deleteCustomer(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await customerService.deleteCustomer(id);
      return res.status(200).json({
        message: CUSTOMER_MESSAGES.DELETED,
      });
    } catch (error) {
      if (error instanceof Error) {
        return res.status(404).json({ error: error.message });
      }
      return res.status(500).json({ error: CUSTOMER_ERRORS.DELETION_FAILED });
    }
  }

  public async exportCustomers(req: Request, res: Response) {
    try {
      const format = req.query.format as 'pdf' | 'excel' | 'csv';
      const result = await customerService.getCustomersForExport();

      // Enviar los datos sin paginación
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ error: CUSTOMER_ERRORS.EXPORT_ERROR });
    }
  }
}

export const customerController = new CustomerController();

import { Request, Response } from 'express';
import { settingsService } from '../services/settings.service.js';
import { SETTINGS_ERRORS } from '../constants/settings.constants.js';

export class SettingsController {
  async getGeneralSettings(req: Request, res: Response) {
    try {
      const settings = await settingsService.getGeneralSettings();
      return res.json(settings);
    } catch (error) {
      return res.status(500).json({
        error: SETTINGS_ERRORS.FETCH_ERROR,
      });
    }
  }

  async getPublicSettings(req: Request, res: Response) {
    try {
      const settings = await settingsService.getPublicSettings();
      return res.json(settings);
    } catch (error) {
      return res.status(500).json({
        error: SETTINGS_ERRORS.FETCH_ERROR,
      });
    }
  }

  async updateGeneralSettings(req: Request, res: Response) {
    try {
      let settings;

      // Parser the settings
      if (typeof req.body.settings === 'string') {
        settings = JSON.parse(req.body.settings);
      } else {
        settings = req.body.settings;
      }

      const currentSettings = await settingsService.getGeneralSettings();

      if (req.file) {
        if (currentSettings.logo) {
          await settingsService.deleteLogoFile(currentSettings.logo);
        }
        settings.logo = `/uploads/logos/${req.file.filename}`;
      } else if (settings.logo === null) {
        if (currentSettings.logo) {
          await settingsService.deleteLogoFile(currentSettings.logo);
        }
      } else {
        // Mantener el logo actual si no se está modificando
        settings.logo = currentSettings.logo;
      }

      const updatedSettings = await settingsService.updateGeneralSettings(
        settings
      );
      return res.json(updatedSettings);
    } catch (error) {
      return res.status(500).json({
        error: SETTINGS_ERRORS.UPDATE_ERROR,
      });
    }
  }
}

export const settingsController = new SettingsController();

import { Request, Response } from 'express';
import { AuthRequest } from '../interfaces/auth.interface.js';
import { Product } from '../models/product.model.js';
import { StockMovement } from '../models/stock.model.js';
import {
  STOCK_ERRORS,
  STOCK_MESSAGES,
  STOCK_MOVEMENT_TYPES,
} from '../constants/stock.constants.js';

export class StockMovementController {
  public async getStockMovements(req: Request, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        type,
        startDate,
        endDate,
        reason,
        batchNumber,
        search,
      } = req.query;

      const query: any = {};

      if (type && type !== 'ALL') {
        query.type = type;
      }

      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate as string);
        if (endDate) query.createdAt.$lte = new Date(endDate as string);
      }

      if (reason) query.reason = reason;
      if (batchNumber) query.batchNumber = batchNumber;

      if (search) {
        query.$or = [
          { reference: { $regex: search, $options: 'i' } },
          { observations: { $regex: search, $options: 'i' } },
          { batchNumber: { $regex: search, $options: 'i' } },
        ];
      }

      const [movements, total] = await Promise.all([
        StockMovement.find(query)
          .populate('product', 'name sku')
          .populate('createdBy', 'username')
          .sort({ createdAt: -1 })
          .skip((Number(page) - 1) * Number(limit))
          .limit(Number(limit))
          .lean(),
        StockMovement.countDocuments(query),
      ]);

      return res.status(200).json({
        entries: movements,
        total,
        page: Number(page),
        limit: Number(limit),
      });
    } catch (error) {
      return res.status(500).json({ error: STOCK_ERRORS.FETCH_ERROR });
    }
  }

  public async createStockEntry(req: AuthRequest, res: Response) {
    try {
      const { id: productId } = req.params;
      const {
        quantity,
        reason,
        reference,
        batchNumber,
        expirationDate,
        cost,
        observations,
      } = req.body;

      const product = await Product.findById(productId);
      if (!product) {
        return res.status(404).json({ error: STOCK_ERRORS.PRODUCT_NOT_FOUND });
      }

      const previousStock = product.stock;
      const newStock = previousStock + quantity;

      const [stockMovement] = await Promise.all([
        StockMovement.create({
          product: productId,
          type: STOCK_MOVEMENT_TYPES.ENTRY,
          quantity,
          previousStock,
          newStock,
          reason,
          batchNumber,
          expirationDate: expirationDate ? new Date(expirationDate) : undefined,
          cost,
          reference,
          observations,
          createdBy: req.user!._id,
        }),
        Product.findByIdAndUpdate(productId, {
          $inc: { stock: quantity },
          $set: { lastStockUpdate: new Date() },
        }),
      ]);

      await stockMovement.populate([
        { path: 'product', select: 'name sku' },
        { path: 'createdBy', select: 'username' },
      ]);

      return res.status(201).json({
        message: STOCK_MESSAGES.ENTRY_CREATED,
        stockMovement,
      });
    } catch (error) {
      console.error('Error creating stock entry:', error);
      return res.status(500).json({ error: STOCK_ERRORS.CREATION_FAILED });
    }
  }

  public async createStockOutput(req: AuthRequest, res: Response) {
    try {
      const { id: productId } = req.params;
      const { quantity, reason, batchNumber, reference, observations } =
        req.body;

      const product = await Product.findById(productId);
      if (!product) {
        return res.status(404).json({ error: STOCK_ERRORS.PRODUCT_NOT_FOUND });
      }

      if (product.stock < quantity) {
        return res.status(400).json({ error: STOCK_ERRORS.INSUFFICIENT_STOCK });
      }

      const previousStock = product.stock;
      const newStock = previousStock - quantity;

      const [stockMovement] = await StockMovement.create([
        {
          product: productId,
          type: STOCK_MOVEMENT_TYPES.OUTPUT,
          quantity,
          previousStock,
          newStock,
          reason,
          batchNumber,
          reference,
          observations,
          createdBy: req.user!._id,
        },
      ]);

      await Product.findByIdAndUpdate(productId, {
        $inc: { stock: -quantity },
        $set: { lastStockUpdate: new Date() },
      });

      const populatedMovement = await StockMovement.findById(stockMovement._id)
        .populate('product', 'name sku')
        .populate('createdBy', 'username');

      return res.status(201).json({
        message: STOCK_MESSAGES.OUTPUT_CREATED,
        stockMovement: populatedMovement,
      });
    } catch (error) {
      console.error('Error creating stock output:', error);
      return res.status(500).json({ error: STOCK_ERRORS.CREATION_FAILED });
    }
  }

  public async getLowStockProducts(req: Request, res: Response) {
    try {
      const products = await Product.find({
        $expr: {
          $lte: ['$stock', '$minStock'],
        },
      }).populate(['category', 'laboratory']);

      return res.status(200).json(products);
    } catch (error) {
      return res.status(500).json({ error: STOCK_ERRORS.FETCH_ERROR });
    }
  }

  public async getExpiringProducts(req: Request, res: Response) {
    try {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

      const products = await Product.find({
        expirationDate: {
          $lte: thirtyDaysFromNow,
          $gte: new Date(),
        },
      }).populate(['category', 'laboratory']);

      return res.status(200).json(products);
    } catch (error) {
      return res.status(500).json({ error: STOCK_ERRORS.FETCH_ERROR });
    }
  }

  public async getProductStockMovements(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const type = req.query.type as string;

      const query: any = { productId: id };
      if (type && type !== 'ALL') {
        query.type = type;
      }

      const [movements, total] = await Promise.all([
        StockMovement.find(query)
          .populate('productId', 'name sku _id')
          .populate('createdBy', 'username _id')
          .skip((page - 1) * limit)
          .limit(limit)
          .sort({ createdAt: -1 })
          .lean(),
        StockMovement.countDocuments(query),
      ]);

      const transformedMovements = movements.map((movement) => ({
        ...movement,
        product: movement.product,
        productId: undefined,
      }));

      return res.status(200).json({
        entries: transformedMovements,
        pagination: {
          total,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          limit,
        },
      });
    } catch (error) {
      return res.status(500).json({ error: STOCK_ERRORS.FETCH_ERROR });
    }
  }

  public async updateStockMovement(req: AuthRequest, res: Response) {
    try {
      const { id } = req.params;
      const { reference, observations } = req.body;

      const stockMovement = await StockMovement.findById(id);
      if (!stockMovement) {
        return res.status(404).json({ error: STOCK_ERRORS.MOVEMENT_NOT_FOUND });
      }

      const updatedMovement = await StockMovement.findByIdAndUpdate(
        id,
        {
          reference,
          observations,
          updatedAt: new Date(),
        },
        { new: true }
      ).populate([
        { path: 'product', select: 'name sku' },
        { path: 'createdBy', select: 'username' },
      ]);

      return res.status(200).json(updatedMovement);
    } catch (error) {
      console.error('Error updating stock movement:', error);
      return res.status(500).json({ error: STOCK_ERRORS.UPDATE_FAILED });
    }
  }

  public async deleteStockMovement(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const movement = await StockMovement.findById(id);
      if (!movement) {
        return res.status(404).json({ error: STOCK_ERRORS.MOVEMENT_NOT_FOUND });
      }

      // Revert stock change in the product
      await Promise.all([
        Product.findByIdAndUpdate(movement.product, {
          $inc: { stock: -movement.quantity },
          $set: { lastStockUpdate: new Date() },
        }),
        movement.deleteOne(),
      ]);

      return res.status(200).json({
        message: STOCK_MESSAGES.MOVEMENT_DELETED,
      });
    } catch (error) {
      console.error('Error deleting stock movement:', error);
      return res.status(500).json({ error: STOCK_ERRORS.DELETE_FAILED });
    }
  }
}

export const stockMovementController = new StockMovementController();

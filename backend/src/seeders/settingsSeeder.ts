import { connectDB, disconnectDB } from '../config/database.js';
import { Settings } from '../models/settings.model.js';
import { initialSettings } from '../data/initialSettings.js';
import { logger } from '../utils/logger.js';

async function seedSettings() {
  try {
    await connectDB();

    // Check if settings already exist
    const settingsCount = await Settings.countDocuments();
    if (settingsCount > 0) {
      logger.warn('Settings already exist, skipping seeding');
      return;
    }

    // Create initial settings
    const settings = new Settings(initialSettings);
    await settings.save();

    logger.info('✅ Initial settings created successfully');
  } catch (error) {
    logger.error(
      `❌ Error seeding settings: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedSettings();
}

export { seedSettings };

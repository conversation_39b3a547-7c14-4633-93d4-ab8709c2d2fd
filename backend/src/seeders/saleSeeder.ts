import { connectDB, disconnectDB } from '../config/database.js';
import { Sale } from '../models/sale.model.js';
import { initialSales } from '../data/initialSales.js';
import { logger } from '../utils/logger.js';

async function seedSales() {
  try {
    await connectDB();

    // Clean existing sales collection
    await Sale.deleteMany({});

    // Insert initial sales
    await Sale.insertMany(initialSales);

    logger.info(`✅ ${initialSales.length} sales created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding sales: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if running directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedSales().catch(console.error);
}

export { seedSales };
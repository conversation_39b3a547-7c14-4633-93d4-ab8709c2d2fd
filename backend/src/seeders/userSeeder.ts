import { connectDB, disconnectDB } from '../config/database.js';
import { User } from '../models/user.model.js';
import { initialUsers } from '../data/initialUsers.js';
import { logger } from '../utils/logger.js';

async function seedUsers() {
  try {
    await connectDB();

    // Check if users already exist
    const userCount = await User.countDocuments();
    if (userCount > 0) {
      logger.warn('Users already exist, skipping seeding');
      return;
    }

    // Create initial users
    for (const userData of initialUsers) {
      // Ensure permissions is always an array of strings
      const userToCreate = {
        ...userData,
        permissions: Array.isArray(userData.permissions)
          ? userData.permissions.flat()
          : userData.permissions,
      };

      const user = new User(userToCreate);
      await user.save();
      logger.info(`✅ Created user: ${userData.username}`);
    }

    logger.info(`✅ ${initialUsers.length} users created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding users: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    if (error instanceof Error && error.stack) {
      logger.error(error.stack);
    }
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedUsers();
}

export { seedUsers };

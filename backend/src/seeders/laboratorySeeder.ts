import { connectDB, disconnectDB } from '../config/database.js';
import { Laboratory } from '../models/laboratory.model.js';
import { initialLaboratories } from '../data/initialLaboratories.js';
import { logger } from '../utils/logger.js';

async function seedLaboratories() {
  try {
    await connectDB();

    // Clean existing laboratories collection
    await Laboratory.deleteMany({});

    // Insert initial laboratories
    await Laboratory.insertMany(initialLaboratories);

    logger.info(`✅ ${initialLaboratories.length} laboratories created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding laboratories: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedLaboratories();
}

export { seedLaboratories };
import { connectDB, disconnectDB } from '../config/database.js';
import { Category } from '../models/category.model.js';
import { initialCategories } from '../data/initialCategories.js';
import { logger } from '../utils/logger.js';

async function seedCategories() {
  try {
    await connectDB();

    // Clean existing categories collection
    await Category.deleteMany({});

    // Insert initial categories
    await Category.insertMany(initialCategories);

    logger.info(`✅ ${initialCategories.length} categories created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding categories: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedCategories();
}

export { seedCategories };
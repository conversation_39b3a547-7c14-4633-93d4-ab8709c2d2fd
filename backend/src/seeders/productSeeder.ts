import { connectDB, disconnectDB } from '../config/database.js';
import { Product } from '../models/product.model.js';
import { initialProducts } from '../data/initialProducts.js';
import { logger } from '../utils/logger.js';

async function seedProducts() {
  try {
    await connectDB();

    // Clean existing product collection
    await Product.deleteMany({});

    // Insert initial products
    await Product.insertMany(initialProducts);

    logger.info(`✅ ${initialProducts.length} products created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding products: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedProducts();
}

export { seedProducts };

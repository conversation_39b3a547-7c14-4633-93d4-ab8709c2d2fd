import { connectDB, disconnectDB } from '../config/database.js';
import { Customer } from '../models/customer.model.js';
import { initialCustomers } from '../data/initialCustomers.js';
import { logger } from '../utils/logger.js';

async function seedCustomers() {
  try {
    await connectDB();

    // Clean existing customers collection
    await Customer.deleteMany({});

    // Insert initial customers
    await Customer.insertMany(initialCustomers);

    logger.info(`✅ ${initialCustomers.length} customers created successfully`);
  } catch (error) {
    logger.error(
      `❌ Error seeding customers: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  } finally {
    await disconnectDB();
  }
}

// Execute seeder if this script is run directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  seedCustomers();
}

export { seedCustomers };
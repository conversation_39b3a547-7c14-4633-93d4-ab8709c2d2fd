import { connectDB, disconnectDB } from '../config/database.js';
import { logger } from '../utils/logger.js';
import mongoose from 'mongoose';

async function clearDatabase() {
  try {
    logger.info('Starting database cleanup...');
    await connectDB();

    const collections = await mongoose.connection.db.collections();

    // Delete all documents from each collection
    for (const collection of collections) {
      await collection.deleteMany({});
      logger.info(`Collection ${collection.collectionName} cleared`);
    }

    logger.success('Database cleared successfully');
  } catch (error) {
    logger.error(
      `Failed to clear database: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
    process.exit(1);
  } finally {
    await disconnectDB();
    process.exit(0);
  }
}

clearDatabase();

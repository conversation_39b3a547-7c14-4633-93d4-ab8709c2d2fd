import { z } from 'zod';
import { CASH_MOVEMENT_TYPES } from '../constants/cash.constants.js';

// Schema para abrir caja
export const openCashRegisterSchema = z.object({
  body: z.object({
    initialBalance: z.number().min(0, 'El monto debe ser mayor o igual a 0'),
    observations: z
      .string()
      .max(200, 'Las observaciones no deben exceder los 200 caracteres')
      .nullable()
      .optional(),
  }),
});

// Schema para cerrar caja
export const closeCashRegisterSchema = z.object({
  body: z.object({
    finalBalance: z.number().min(0, 'El monto debe ser mayor o igual a 0'),
    observations: z
      .string()
      .max(200, 'Las observaciones no deben exceder los 200 caracteres')
      .nullable()
      .optional(),
  }),
});

// Schema para crear movimiento de caja
export const createCashMovementSchema = z.object({
  params: z.object({
    registerId: z.string(),
  }),
  body: z.object({
    type: z.enum([CASH_MOVEMENT_TYPES.INCOME, CASH_MOVEMENT_TYPES.EXPENSE], {
      required_error: 'El tipo de movimiento es requerido',
    }),
    amount: z
      .number({
        required_error: 'El monto es requerido',
      })
      .min(0, 'El monto debe ser mayor o igual a 0'),
    concept: z
      .string()
      .min(3, 'El concepto debe tener al menos 3 caracteres')
      .max(100, 'El concepto no debe exceder los 100 caracteres'),
    reference: z
      .string()
      .max(50, 'La referencia no debe exceder los 50 caracteres')
      .nullable()
      .optional(),
    observations: z
      .string()
      .max(200, 'Las observaciones no deben exceder los 200 caracteres')
      .nullable()
      .optional(),
  }),
});

// Schema para actualizar movimiento de caja
export const updateCashMovementSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    concept: z
      .string({
        required_error: 'El concepto es requerido',
      })
      .min(1, 'El concepto es requerido'),
    reference: z
      .string()
      .max(50, 'La referencia no debe exceder los 50 caracteres')
      .nullable()
      .optional(),
    observations: z
      .string()
      .max(200, 'Las observaciones no deben exceder los 200 caracteres')
      .nullable()
      .optional(),
  }),
});

// Schema para obtener movimientos
export const getCashMovementsSchema = z.object({
  params: z.object({
    registerId: z.string(),
  }),
  query: z.object({
    page: z.coerce.number().optional(),
    limit: z.coerce.number().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    type: z
      .enum(Object.values(CASH_MOVEMENT_TYPES) as [string, ...string[]])
      .optional(),
    search: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  }),
});

// Schema para obtener historial de cajas
export const getCashRegistersHistorySchema = z.object({
  query: z.object({
    page: z.string().optional().transform(Number),
    limit: z.string().optional().transform(Number),
    status: z.enum(['OPEN', 'CLOSED']).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    search: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  }),
});

// Tipos inferidos para uso interno
export type OpenCashRegisterSchema = z.infer<
  typeof openCashRegisterSchema
>['body'];
export type CloseCashRegisterSchema = z.infer<
  typeof closeCashRegisterSchema
>['body'];
export type CreateCashMovementSchema = z.infer<
  typeof createCashMovementSchema
>['body'];
export type UpdateCashMovementSchema = z.infer<
  typeof updateCashMovementSchema
>['body'];
export type GetCashMovementsSchema = z.infer<
  typeof getCashMovementsSchema
>['query'];
export type GetCashRegistersHistorySchema = z.infer<
  typeof getCashRegistersHistorySchema
>['query'];

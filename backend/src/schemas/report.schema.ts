import { z } from 'zod';
import { isValidObjectId } from 'mongoose';
import { SALE_STATUS } from '../constants/sale.constants.js';

export const getSalesReportSchema = z.object({
  query: z.object({
    page: z.string().optional().transform(Number),
    limit: z.string().optional().transform(Number),
    search: z.string().optional(),
    startDate: z
      .string()
      .optional()
      .refine(
        (date) => {
          if (!date) return true;
          const parsed = new Date(date);
          return !isNaN(parsed.getTime());
        },
        {
          message: 'Fecha de inicio inválida',
        }
      ),
    endDate: z
      .string()
      .optional()
      .refine(
        (date) => {
          if (!date) return true;
          const parsed = new Date(date);
          return !isNaN(parsed.getTime());
        },
        {
          message: 'Fecha de fin inválida',
        }
      ),
    status: z
      .enum(Object.values(SALE_STATUS) as [string, ...string[]])
      .optional(),
    customerId: z
      .string()
      .refine((val) => !val || isValidObjectId(val), {
        message: 'ID de cliente inválido',
      })
      .optional(),
    cashRegisterId: z
      .string()
      .refine((val) => !val || isValidObjectId(val), {
        message: 'ID de caja inválido',
      })
      .optional(),
  }),
});

export const getInventoryReportSchema = z.object({
  query: z.object({
    search: z.string().optional(),
    categoryId: z
      .string()
      .refine((val) => !val || isValidObjectId(val), {
        message: 'ID de categoría inválido',
      })
      .optional(),
    laboratoryId: z
      .string()
      .refine((val) => !val || isValidObjectId(val), {
        message: 'ID de laboratorio inválido',
      })
      .optional(),
    stockAlert: z
      .string()
      .optional()
      .transform((val) => val === 'true'),
    expirationAlert: z
      .string()
      .optional()
      .transform((val) => val === 'true'),
    isActive: z
      .string()
      .optional()
      .transform((val) => val === 'true'),
  }),
});

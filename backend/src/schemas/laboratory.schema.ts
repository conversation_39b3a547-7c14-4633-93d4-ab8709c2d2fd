import { z } from 'zod';
import { LABORATORY_VALIDATION } from '../constants/laboratory.constants.js';

const laboratoryBodySchema = z.object({
  name: z
    .string()
    .min(
      LABORATORY_VALIDATION.NAME.MIN_LENGTH,
      LABORATORY_VALIDATION.NAME.MESSAGE.MIN
    )
    .max(
      LABORATORY_VALIDATION.NAME.MAX_LENGTH,
      LABORATORY_VALIDATION.NAME.MESSAGE.MAX
    ),
  description: z
    .string()
    .max(
      LABORATORY_VALIDATION.DESCRIPTION.MAX_LENGTH,
      LABORATORY_VALIDATION.DESCRIPTION.MESSAGE
    )
    .nullable()
    .optional(),
  country: z
    .string()
    .max(
      LABORATORY_VALIDATION.COUNTRY.MAX_LENGTH,
      LABORATORY_VALIDATION.COUNTRY.MESSAGE
    )
    .nullable()
    .optional(),
  website: z
    .string()
    .url(LABORATORY_VALIDATION.WEBSITE.MESSAGE.URL)
    .max(
      LABORATORY_VALIDATION.WEBSITE.MAX_LENGTH,
      LABORATORY_VALIDATION.WEBSITE.MESSAGE.MAX
    )
    .nullable()
    .optional()
    .or(z.literal('')),
});

export const createLaboratorySchema = z.object({
  body: laboratoryBodySchema,
});

export const updateLaboratorySchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: laboratoryBodySchema,
});

export const deleteLaboratorySchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

export const toggleStatusSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    isActive: z.boolean(),
  }),
});

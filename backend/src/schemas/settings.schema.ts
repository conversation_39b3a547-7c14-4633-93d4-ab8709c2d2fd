import { z } from 'zod';
import {
  SETTINGS_VALIDATION,
  SETTINGS_ERRORS,
} from '../constants/settings.constants.js';

export const generalSettingsSchema = z.object({
  body: z.object({
    settings: z
      .string()
      .transform((str) => {
        try {
          return JSON.parse(str);
        } catch (error) {
          throw new Error(SETTINGS_ERRORS.INVALID_JSON);
        }
      })
      .pipe(
        z.object({
          systemName: z
            .string({
              required_error: SETTINGS_VALIDATION.SYSTEM_NAME.REQUIRED,
            })
            .min(1, SETTINGS_VALIDATION.SYSTEM_NAME.REQUIRED)
            .max(
              SETTINGS_VALIDATION.SYSTEM_NAME.MAX_LENGTH,
              SETTINGS_VALIDATION.SYSTEM_NAME.MAX_MESSAGE
            ),
          businessName: z
            .string({
              required_error: SETTINGS_VALIDATION.BUSINESS_NAME.REQUIRED,
            })
            .min(1, SETTINGS_VALIDATION.BUSINESS_NAME.REQUIRED)
            .max(
              SETTINGS_VALIDATION.BUSINESS_NAME.MAX_LENGTH,
              SETTINGS_VALIDATION.BUSINESS_NAME.MAX_MESSAGE
            ),
          address: z
            .string({
              required_error: SETTINGS_VALIDATION.ADDRESS.REQUIRED,
            })
            .min(1, SETTINGS_VALIDATION.ADDRESS.REQUIRED)
            .max(
              SETTINGS_VALIDATION.ADDRESS.MAX_LENGTH,
              SETTINGS_VALIDATION.ADDRESS.MAX_MESSAGE
            ),
          phone: z
            .string({
              required_error: SETTINGS_VALIDATION.PHONE.REQUIRED,
            })
            .regex(
              SETTINGS_VALIDATION.PHONE.REGEX,
              SETTINGS_VALIDATION.PHONE.INVALID_FORMAT
            ),
          email: z
            .string({
              required_error: SETTINGS_VALIDATION.EMAIL.REQUIRED,
            })
            .email(SETTINGS_VALIDATION.EMAIL.INVALID_FORMAT),
          taxId: z
            .string({
              required_error: SETTINGS_VALIDATION.TAX_ID.REQUIRED,
            })
            .regex(
              SETTINGS_VALIDATION.TAX_ID.REGEX,
              SETTINGS_VALIDATION.TAX_ID.INVALID_FORMAT
            ),
          description: z
            .string()
            .max(
              SETTINGS_VALIDATION.DESCRIPTION.MAX_LENGTH,
              SETTINGS_VALIDATION.DESCRIPTION.MAX_MESSAGE
            )
            .optional(),
          logo: z.string().optional().nullable(),
        })
      ),
  }),
  file: z
    .object({
      fieldname: z.string(),
      originalname: z.string(),
      encoding: z.string(),
      mimetype: z
        .string()
        .refine(
          (mime: string) =>
            SETTINGS_VALIDATION.LOGO.ALLOWED_MIME_TYPES.includes(
              mime as string
            ),
          SETTINGS_ERRORS.INVALID_FILE_TYPE
        ),
      size: z
        .number()
        .max(SETTINGS_VALIDATION.LOGO.MAX_SIZE, SETTINGS_ERRORS.FILE_TOO_LARGE),
      filename: z.string(),
      path: z.string(),
    })
    .optional(),
});

export const getPublicSettingsSchema = z.object({});

export const getGeneralSettingsSchema = z.object({});

export type GeneralSettingsFormValues = z.infer<
  typeof generalSettingsSchema
>['body']['settings'];

import { z } from 'zod';
import { DOCUMENT_TYPES } from '../constants/customer.constants.js';

export const createCustomerSchema = z.object({
  body: z.object({
    documentType: z.enum(
      Object.values(DOCUMENT_TYPES) as [string, ...string[]],
      {
        required_error: 'El tipo de documento es requerido',
        invalid_type_error: 'Tipo de documento no válido',
      }
    ),
    documentNumber: z
      .string({
        required_error: 'El número de documento es requerido',
      })
      .min(5, 'El número de documento debe tener al menos 5 caracteres')
      .max(20, 'El número de documento no debe exceder los 20 caracteres'),
    complement: z
      .string()
      .max(10, 'El complemento no debe exceder los 10 caracteres')
      .optional(),
    businessName: z
      .string({
        required_error: 'El nombre o razón social es requerido',
      })
      .min(3, 'El nombre debe tener al menos 3 caracteres')
      .max(100, 'El nombre no debe exceder los 100 caracteres'),
    email: z
      .string()
      .email('El formato del correo electrónico no es válido')
      .optional(),
    phone: z
      .string()
      .max(20, 'El teléfono no debe exceder los 20 caracteres')
      .optional(),
    address: z
      .string()
      .max(200, 'La dirección no debe exceder los 200 caracteres')
      .optional(),
    notes: z
      .string()
      .max(500, 'Las notas no deben exceder los 500 caracteres')
      .optional(),
    isGeneric: z.boolean().optional(),
    isProtected: z.boolean().optional(),
  }),
});

export const updateCustomerSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del cliente es requerido',
      invalid_type_error: 'ID de cliente inválido',
    }),
  }),
  body: z.object({
    documentType: z
      .enum(Object.values(DOCUMENT_TYPES) as [string, ...string[]])
      .optional(),
    documentNumber: z
      .string()
      .min(5, 'El número de documento debe tener al menos 5 caracteres')
      .max(20, 'El número de documento no debe exceder los 20 caracteres')
      .optional(),
    complement: z
      .string()
      .max(10, 'El complemento no debe exceder los 10 caracteres')
      .optional(),
    businessName: z
      .string()
      .min(3, 'El nombre debe tener al menos 3 caracteres')
      .max(100, 'El nombre no debe exceder los 100 caracteres')
      .optional(),
    email: z
      .string()
      .email('El formato del correo electrónico no es válido')
      .optional(),
    phone: z
      .string()
      .max(20, 'El teléfono no debe exceder los 20 caracteres')
      .optional(),
    address: z
      .string()
      .max(200, 'La dirección no debe exceder los 200 caracteres')
      .optional(),
    notes: z
      .string()
      .max(500, 'Las notas no deben exceder los 500 caracteres')
      .optional(),
    isActive: z.boolean().optional(),
  }),
});

export const getCustomerByIdSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del cliente es requerido',
      invalid_type_error: 'ID de cliente inválido',
    }),
  }),
});

export type CreateCustomerSchema = z.infer<typeof createCustomerSchema>['body'];
export type UpdateCustomerSchema = z.infer<typeof updateCustomerSchema>['body'];

import { z } from 'zod';
import { USER_VALIDATION } from '../constants/user.constants.js';

export const loginSchema = z.object({
  body: z.object({
    username: z
      .string()
      .min(
        USER_VALIDATION.USERNAME_MIN_LENGTH,
        `El usuario debe tener al menos ${USER_VALIDATION.USERNAME_MIN_LENGTH} caracteres`
      ),
    password: z
      .string()
      .min(
        USER_VALIDATION.PASSWORD_MIN_LENGTH,
        `La contraseña debe tener al menos ${USER_VALIDATION.PASSWORD_MIN_LENGTH} caracteres`
      ),
  }),
});

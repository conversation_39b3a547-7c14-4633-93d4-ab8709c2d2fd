import { z } from 'zod';
import { CATEGORY_VALIDATION } from '../constants/category.constants.js';

// Schema para crear una categoría
export const createCategorySchema = z.object({
  body: z.object({
    name: z
      .string()
      .min(
        CATEGORY_VALIDATION.NAME.MIN_LENGTH,
        CATEGORY_VALIDATION.NAME.MESSAGE
      )
      .max(
        CATEGORY_VALIDATION.NAME.MAX_LENGTH,
        CATEGORY_VALIDATION.NAME.MESSAGE
      ),
    description: z
      .string()
      .min(
        CATEGORY_VALIDATION.DESCRIPTION.MIN_LENGTH,
        CATEGORY_VALIDATION.DESCRIPTION.MESSAGE
      )
      .max(
        CATEGORY_VALIDATION.DESCRIPTION.MAX_LENGTH,
        CATEGORY_VALIDATION.DESCRIPTION.MESSAGE
      )
      .nullable()
      .optional(),
  }),
});

// Schema para actualizar una categoría
export const updateCategorySchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    name: z
      .string()
      .min(
        CATEGORY_VALIDATION.NAME.MIN_LENGTH,
        CATEGORY_VALIDATION.NAME.MESSAGE
      )
      .max(
        CATEGORY_VALIDATION.NAME.MAX_LENGTH,
        CATEGORY_VALIDATION.NAME.MESSAGE
      ),
    description: z
      .string()
      .min(
        CATEGORY_VALIDATION.DESCRIPTION.MIN_LENGTH,
        CATEGORY_VALIDATION.DESCRIPTION.MESSAGE
      )
      .max(
        CATEGORY_VALIDATION.DESCRIPTION.MAX_LENGTH,
        CATEGORY_VALIDATION.DESCRIPTION.MESSAGE
      )
      .nullable()
      .optional(),
  }),
});

// Schema para eliminar una categoría
export const deleteCategorySchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

// Schema para cambiar el estado de una categoría
export const toggleStatusSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    isActive: z.boolean(),
  }),
});

// Schema para obtener una categoría por ID
export const getCategoryByIdSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

export type CreateCategorySchema = z.infer<typeof createCategorySchema>['body'];
export type UpdateCategorySchema = z.infer<typeof updateCategorySchema>['body'];
export type ToggleStatusSchema = z.infer<typeof toggleStatusSchema>['body'];

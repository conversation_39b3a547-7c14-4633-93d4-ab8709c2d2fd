import { z } from 'zod';
import {
  STOCK_MOVEMENT_REASONS,
  STOCK_MOVEMENT_TYPES,
} from '../constants/stock.constants.js';

const baseMovementSchema = {
  quantity: z
    .number()
    .min(1, 'La cantidad debe ser mayor a 0')
    .positive('La cantidad debe ser un número positivo'),
  reason: z.enum(
    Object.values(STOCK_MOVEMENT_REASONS) as [string, ...string[]],
    {
      required_error: 'La razón del movimiento es requerida',
      invalid_type_error: 'Razón de movimiento inválida',
    }
  ),
  reference: z
    .string()
    .max(100, 'La referencia no debe exceder los 100 caracteres')
    .optional(),
  observations: z
    .string()
    .max(500, 'Las observaciones no deben exceder los 500 caracteres')
    .optional(),
  batchNumber: z.string().min(1, 'El número de lote es requerido'),
};

export const createStockEntrySchema = z.object({
  body: z.object({
    ...baseMovementSchema,
    expirationDate: z
      .string()
      .min(1, 'La fecha de vencimiento es requerida')
      .refine((date) => {
        const parsed = new Date(`${date}T00:00:00.000Z`);
        const now = new Date();
        parsed.setUTCHours(0, 0, 0, 0);
        now.setHours(0, 0, 0, 0);
        return !isNaN(parsed.getTime()) && parsed >= now;
      }, 'La fecha de vencimiento debe ser posterior a la fecha actual'),
    cost: z.number().min(0, 'El costo debe ser mayor o igual a 0'),
  }),
  params: z.object({
    id: z.string(),
  }),
});

export const createStockOutputSchema = z.object({
  body: z.object({
    ...baseMovementSchema,
  }),
  params: z.object({
    id: z.string(),
  }),
});

export const updateStockMovementSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    reference: z
      .string()
      .max(100, 'La referencia no debe exceder los 100 caracteres')
      .optional(),
    observations: z
      .string()
      .max(500, 'Las observaciones no deben exceder los 500 caracteres')
      .optional(),
  }),
});

export const getStockMovementsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform(Number),
    limit: z.string().optional().transform(Number),
    search: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    type: z
      .enum(Object.values(STOCK_MOVEMENT_TYPES) as [string, ...string[]])
      .optional(),
    reason: z
      .enum(Object.values(STOCK_MOVEMENT_REASONS) as [string, ...string[]])
      .optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  }),
});

export const getProductStockMovementsSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  query: z.object({
    page: z.string().optional().transform(Number),
    limit: z.string().optional().transform(Number),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    type: z
      .enum(Object.values(STOCK_MOVEMENT_TYPES) as [string, ...string[]])
      .optional(),
    reason: z
      .enum(Object.values(STOCK_MOVEMENT_REASONS) as [string, ...string[]])
      .optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  }),
});

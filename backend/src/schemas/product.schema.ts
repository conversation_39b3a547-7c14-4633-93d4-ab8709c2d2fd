import { z } from 'zod';
import { PRODUCT_VALIDATION } from '../constants/product.constants.js';

const numberFromString = z.string().transform((val, ctx) => {
  const parsed = Number(val);
  if (isNaN(parsed)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: PRODUCT_VALIDATION.NUMBER.INVALID,
    });
    return z.NEVER;
  }
  return parsed;
});

const booleanFromString = z.preprocess((val) => {
  if (typeof val === 'string') return val.toLowerCase() === 'true';
  if (typeof val === 'boolean') return val;
  return false;
}, z.boolean());

export const createProductSchema = z.object({
  body: z
    .object({
      sku: z
        .union([
          z
            .string()
            .min(
              PRODUCT_VALIDATION.SKU.MIN_LENGTH,
              PRODUCT_VALIDATION.SKU.MESSAGE
            )
            .max(50, PRODUCT_VALIDATION.SKU.MESSAGE),
          z.string().length(0),
        ])
        .optional(),
      name: z
        .string()
        .min(
          PRODUCT_VALIDATION.NAME.MIN_LENGTH,
          PRODUCT_VALIDATION.NAME.MESSAGE
        ),
      description: z
        .string()
        .min(1, PRODUCT_VALIDATION.DESCRIPTION.REQUIRED)
        .max(
          PRODUCT_VALIDATION.DESCRIPTION.MAX_LENGTH,
          PRODUCT_VALIDATION.DESCRIPTION.MESSAGE
        ),
      laboratory: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LABORATORY),
      category: z.string().min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CATEGORY),
      price: numberFromString.pipe(
        z
          .number()
          .min(PRODUCT_VALIDATION.PRICE.MIN, PRODUCT_VALIDATION.PRICE.MESSAGE)
      ),
      cost: numberFromString.pipe(
        z
          .number()
          .min(
            PRODUCT_VALIDATION.PRICE.MIN,
            PRODUCT_VALIDATION.PRICE.COST_MESSAGE
          )
      ),
      minStock: numberFromString.pipe(
        z
          .number()
          .min(
            PRODUCT_VALIDATION.STOCK.MIN,
            PRODUCT_VALIDATION.STOCK.MIN_MESSAGE
          )
      ),
      maxStock: numberFromString.pipe(
        z.number().min(0, PRODUCT_VALIDATION.STOCK.MAX_MESSAGE)
      ),
      location: z.string().min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LOCATION),
      barcode: z.string().optional(),
      requiresPrescription: booleanFromString,
      presentation: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.PRESENTATION),
      concentration: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CONCENTRATION),
      measurementUnit: z.enum(PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.VALUES),
      administrationRoute: z.enum(
        PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.VALUES
      ),
      storageCondition: z.enum(
        PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.VALUES
      ),
      sanitaryRegistration: z
        .string()
        .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.SANITARY_REGISTRATION),
      stockAlert: booleanFromString.optional(),
      expirationAlert: booleanFromString.optional(),
    })
    .refine(
      (data) => {
        const minStock = Number(data.minStock);
        const maxStock = Number(data.maxStock);
        return maxStock >= minStock;
      },
      {
        message: PRODUCT_VALIDATION.STOCK.COMPARISON_MESSAGE,
        path: ['maxStock'],
      }
    ),
});

export const updateProductSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
  body: z.object({
    sku: z
      .union([
        z
          .string()
          .min(
            PRODUCT_VALIDATION.SKU.MIN_LENGTH,
            PRODUCT_VALIDATION.SKU.MESSAGE
          )
          .max(50, PRODUCT_VALIDATION.SKU.MESSAGE),
        z.string().length(0),
      ])
      .optional(),
    name: z
      .string()
      .min(PRODUCT_VALIDATION.NAME.MIN_LENGTH, PRODUCT_VALIDATION.NAME.MESSAGE)
      .optional(),
    description: z
      .string()
      .min(1, PRODUCT_VALIDATION.DESCRIPTION.REQUIRED)
      .optional(),
    laboratory: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LABORATORY)
      .optional(),
    category: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CATEGORY)
      .optional(),
    price: numberFromString
      .pipe(
        z
          .number()
          .min(PRODUCT_VALIDATION.PRICE.MIN, PRODUCT_VALIDATION.PRICE.MESSAGE)
      )
      .optional(),
    location: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.LOCATION)
      .optional(),
    barcode: z.string().optional(),
    requiresPrescription: booleanFromString.optional(),
    presentation: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.PRESENTATION)
      .optional(),
    concentration: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.CONCENTRATION)
      .optional(),
    measurementUnit: z
      .enum(PRODUCT_VALIDATION.ENUMS.MEASUREMENT_UNIT.VALUES)
      .optional(),
    administrationRoute: z
      .enum(PRODUCT_VALIDATION.ENUMS.ADMINISTRATION_ROUTE.VALUES)
      .optional(),
    storageCondition: z
      .enum(PRODUCT_VALIDATION.ENUMS.STORAGE_CONDITION.VALUES)
      .optional(),
    sanitaryRegistration: z
      .string()
      .min(1, PRODUCT_VALIDATION.REQUIRED_FIELDS.SANITARY_REGISTRATION)
      .optional(),
    deleteImage: booleanFromString.optional(),
  }),
});

export const deleteProductSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

export const getProductSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

import { z } from 'zod';
import { PAYMENT_METHODS, SALE_STATUS } from '../constants/sale.constants.js';
import { isValidObjectId, Types } from 'mongoose';

const objectIdValidation = z.union([
  z.string().refine((val) => isValidObjectId(val), {
    message: 'ID inválido',
  }),
  z.instanceof(Types.ObjectId).transform((id) => id.toString()),
]);

// Definir saleItemSchema antes de usarlo
const saleItemSchema = z.object({
  product: objectIdValidation,
  quantity: z.number().min(1, 'La cantidad debe ser mayor a 0'),
  unitPrice: z.number().min(0, 'El precio debe ser mayor o igual a 0'),
  discount: z
    .number()
    .min(0, 'El descuento debe ser mayor o igual a 0')
    .optional(),
});

export const createSaleSchema = z.object({
  body: z
    .object({
      cashRegisterId: objectIdValidation,
      items: z
        .array(
          z.object({
            productId: objectIdValidation,
            quantity: z.number().min(1, 'La cantidad debe ser mayor a 0'),
            unitPrice: z
              .number()
              .min(0, 'El precio debe ser mayor o igual a 0'),
            discount: z
              .number()
              .min(0, 'El descuento debe ser mayor o igual a 0')
              .optional(),
          })
        )
        .min(1, 'Debe incluir al menos un producto'),
      customerId: objectIdValidation.optional(),
      paymentMethod: z.enum(
        Object.values(PAYMENT_METHODS) as [string, ...string[]]
      ),
      discountType: z.enum(['percentage', 'fixed']).default('percentage'),
      discount: z.number().min(0).default(0),
      notes: z.string().optional(),
      amountReceived: z
        .number()
        .min(0, 'El monto recibido debe ser mayor o igual a 0')
        .nullable()
        .optional(),
    })
    .refine(
      (data): data is typeof data => {
        const subtotal = data.items.reduce((acc, item) => {
          return acc + item.unitPrice * item.quantity;
        }, 0);

        if (data.discountType === 'fixed') {
          return data.discount <= subtotal;
        } else {
          return data.discount <= 100;
        }
      },
      {
        message:
          'El descuento no puede ser mayor que el subtotal o el porcentaje no puede ser mayor a 100%',
      }
    ),
});

export const updateSaleSchema = z.object({
  params: z.object({
    id: objectIdValidation,
  }),
  body: z.object({
    customer: objectIdValidation.optional(),
    items: z
      .array(saleItemSchema)
      .min(1, 'Debe incluir al menos un producto')
      .optional(),
    paymentMethod: z
      .enum(Object.values(PAYMENT_METHODS) as [string, ...string[]])
      .optional(),
    discount: z
      .number()
      .min(0, 'El descuento debe ser mayor o igual a 0')
      .optional(),
    notes: z.string().optional(),
  }),
});

export const cancelSaleSchema = z.object({
  params: z.object({
    id: objectIdValidation,
  }),
  body: z.object({
    reason: z
      .string({
        required_error: 'El motivo de anulación es requerido',
      })
      .min(1, 'El motivo de anulación es requerido')
      .max(500, 'El motivo de anulación no puede exceder los 500 caracteres'),
    notes: z.string().optional(),
    canceledAt: z
      .date()
      .optional()
      .default(() => new Date()),
  }),
});

export const getSaleSchema = z.object({
  params: z.object({
    id: objectIdValidation,
  }),
});

export const getSalesSchema = z.object({
  query: z.object({
    page: z.string().optional(),
    limit: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    status: z
      .enum(Object.values(SALE_STATUS) as [string, ...string[]])
      .optional(),
    customer: objectIdValidation.optional(),
    cashRegister: objectIdValidation.optional(),
    search: z.string().optional(),
  }),
});

export const printSaleSchema = z.object({
  params: z.object({
    id: objectIdValidation,
  }),
});

export const exportSalesSchema = z.object({
  query: z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    status: z
      .enum(Object.values(SALE_STATUS) as [string, ...string[]])
      .optional(),
    customer: objectIdValidation.optional(),
    cashRegister: objectIdValidation.optional(),
    format: z.enum(['pdf', 'excel', 'csv']),
  }),
});

export const deleteSaleSchema = z.object({
  params: z.object({
    id: objectIdValidation,
  }),
});

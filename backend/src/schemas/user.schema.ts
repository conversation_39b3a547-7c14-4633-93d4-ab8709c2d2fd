import { z } from 'zod';
import { USER_VALIDATION } from '../constants/user.constants.js';
import { USER_ROLES } from '../constants/user.constants.js';
import { VALID_PERMISSIONS } from '../interfaces/user.interface.js';

export const createUserSchema = z.object({
  body: z.object({
    username: z
      .string({
        required_error: 'El nombre de usuario es requerido',
      })
      .min(
        USER_VALIDATION.USERNAME_MIN_LENGTH,
        `El nombre de usuario debe tener al menos ${USER_VALIDATION.USERNAME_MIN_LENGTH} caracteres`
      ),
    password: z
      .string({
        required_error: 'La contraseña es requerida',
      })
      .min(
        USER_VALIDATION.PASSWORD_MIN_LENGTH,
        `La contraseña debe tener al menos ${USER_VALIDATION.PASSWORD_MIN_LENGTH} caracteres`
      ),
    role: z.enum([USER_ROLES.ADMIN, USER_ROLES.CASHIER], {
      required_error: 'El rol es requerido',
      invalid_type_error: 'Rol inválido',
    }),
    permissions: z
      .array(
        z.enum([...VALID_PERMISSIONS] as const, {
          errorMap: () => ({ message: 'Permiso inválido' }),
        })
      )
      .optional(),
  }),
});

export const updateUserSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
  body: z.object({
    username: z
      .string()
      .min(
        USER_VALIDATION.USERNAME_MIN_LENGTH,
        `El nombre de usuario debe tener al menos ${USER_VALIDATION.USERNAME_MIN_LENGTH} caracteres`
      )
      .optional(),
    role: z
      .enum([USER_ROLES.ADMIN, USER_ROLES.CASHIER], {
        invalid_type_error: 'Rol inválido',
      })
      .optional(),
  }),
});

export const changePasswordSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
  body: z
    .object({
      newPassword: z
        .string({
          required_error: 'La nueva contraseña es requerida',
        })
        .min(
          USER_VALIDATION.PASSWORD_MIN_LENGTH,
          `La contraseña debe tener al menos ${USER_VALIDATION.PASSWORD_MIN_LENGTH} caracteres`
        ),
      confirmPassword: z
        .string({
          required_error: 'La confirmación de contraseña es requerida',
        })
        .min(1, 'La confirmación de contraseña es requerida'),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: 'Las contraseñas no coinciden',
      path: ['confirmPassword'],
    }),
});

export const updatePermissionsSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
  body: z.object({
    permissions: z.array(
      z.enum([...VALID_PERMISSIONS] as const, {
        errorMap: () => ({ message: 'Permiso inválido' }),
      })
    ),
  }),
});

export const getUserByIdSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
});

export const deleteUserSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
});

export const toggleUserStatusSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'El ID del usuario es requerido',
    }),
  }),
  body: z.object({
    isActive: z.boolean({
      required_error: 'El estado de activación es requerido',
    }),
  }),
});

import jwt, { SignOptions, Secret } from 'jsonwebtoken';
import ms from 'ms';
import { TokenPayload } from '../interfaces/auth.interface.js';

const JWT_SECRET: Secret = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN: string | number = process.env.JWT_EXPIRES_IN || '24h';

export const generateToken = (payload: TokenPayload): string => {
  const options: SignOptions = {
    expiresIn: JWT_EXPIRES_IN as unknown as ms.StringValue,
  };

  return jwt.sign(payload as jwt.JwtPayload, JWT_SECRET, options);
};

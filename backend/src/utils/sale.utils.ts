import { Sale } from '../models/sale.model.js';

export async function generateSaleNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  
  // Obtener el último número de venta del mes actual
  const lastSale = await Sale.findOne({
    number: new RegExp(`^${year}${month}`)
  })
  .sort({ number: -1 })
  .select('number');

  let sequence = 1;
  if (lastSale) {
    sequence = parseInt(lastSale.number.slice(-4)) + 1;
  }

  return `${year}${month}${sequence.toString().padStart(4, '0')}`;
}
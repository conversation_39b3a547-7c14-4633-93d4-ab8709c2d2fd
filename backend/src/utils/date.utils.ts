/**
 * Valida si una fecha es válida
 */
export const isValidDate = (date: any): boolean => {
  const d = new Date(date);
  return !isNaN(d.getTime());
};

/**
 * Valida si una fecha es futura (mayor que hoy)
 */
export const isFutureDate = (date: Date | string): boolean => {
  const d = new Date(date);
  const today = new Date();
  d.setUTCHours(0, 0, 0, 0);
  today.setUTCHours(0, 0, 0, 0);
  return d > today;
};

export const getDateRange = (startDate?: string, endDate?: string) => {
  if (!startDate && !endDate) return {};

  const query: any = {};

  if (startDate) {
    const start = new Date(startDate);
    start.setUTCHours(0, 0, 0, 0);
    query.$gte = start;
  }

  if (endDate) {
    const end = new Date(endDate);
    end.setUTCHours(23, 59, 59, 999);
    query.$lte = end;
  }

  return query;
};

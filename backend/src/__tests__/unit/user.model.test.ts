import { describe, it, expect } from '@jest/globals';
import { User } from '../../models/user.model.js';
import { USER_ROLES } from '../../constants/user.constants.js';

describe('User Model Test', () => {
  it('should create & save user successfully', async () => {
    const validUser = {
      username: 'testuser',
      password: 'Password123!',
      role: USER_ROLES.CASHIER,
      permissions: ['read:products'],
    };

    const savedUser = await User.create(validUser);

    expect(savedUser._id).toBeDefined();
    expect(savedUser.username).toBe(validUser.username);
    expect(savedUser.role).toBe(validUser.role);
    expect(savedUser.password).not.toBe(validUser.password);
  });

  it('should fail to save user without required fields', async () => {
    const userWithoutRequiredField = new User({
      role: USER_ROLES.CASHIER,
    });
    let err;

    try {
      await userWithoutRequiredField.save();
    } catch (error) {
      err = error;
    }

    expect(err).toBeDefined();
  });
});

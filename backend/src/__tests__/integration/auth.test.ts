import request from 'supertest';
import app from '../../app.js';
import { User } from '../../models/user.model.js';
import { USER_ROLES } from '../../constants/user.constants.js';
import { ERROR_MESSAGES } from '../../constants/error.constants.js';
import { API_CONSTANTS } from '../../constants/api.constants.js';

describe('Auth Endpoints', () => {
  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const testUser = await User.create({
        username: 'testuser',
        password: 'Test1234!',
        role: USER_ROLES.CASHIER,
      });

      const response = await request(app)
        .post(`${API_CONSTANTS.PREFIX}/auth/login`)
        .send({
          username: 'testuser',
          password: 'Test1234!',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body.user).toHaveProperty('username', 'testuser');
    });

    it('should fail with invalid credentials', async () => {
      const response = await request(app)
        .post(`${API_CONSTANTS.PREFIX}/auth/login`)
        .send({
          username: 'wronguser',
          password: 'wrongpass',
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe(ERROR_MESSAGES.AUTH.USER_NOT_FOUND);
    });
  });
});

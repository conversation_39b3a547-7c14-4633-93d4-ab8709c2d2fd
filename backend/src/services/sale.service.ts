import { Types } from 'mongoose';
import { Sale } from '../models/sale.model.js';
import { Product } from '../models/product.model.js';
import { SALE_STATUS, SALE_ERRORS } from '../constants/sale.constants.js';
import type {
  CreateSaleDto,
  UpdateSaleDto,
  CancelSaleDto,
  CreateSaleItemDto,
} from '../interfaces/sale.interface.js';
import { generateSaleNumber } from '../utils/sale.utils.js';

interface BulkWriteOperation {
  updateOne: {
    filter: { _id: string };
    update: { $inc: { stock: number } };
  };
}

const calculateTotal = (
  items: CreateSaleItemDto[],
  discountType: string,
  discount: number
) => {
  const subtotal = items.reduce((acc, item) => {
    const itemTotal = item.unitPrice * item.quantity; // Cambiado de price a unitPrice
    const itemDiscount = item.discount || 0;
    return acc + (itemTotal - itemDiscount);
  }, 0);

  const finalDiscount =
    discountType === 'percentage' ? subtotal * (discount / 100) : discount;

  return Math.max(subtotal - finalDiscount, 0);
};

export class SaleService {
  async createSale(
    data: CreateSaleDto,
    userId: string,
    cashRegisterId: string
  ) {
    if (!userId) {
      throw new Error(SALE_ERRORS.INVALID_USER);
    }

    try {
      // Verificar y actualizar stock de productos
      const productUpdates: BulkWriteOperation[] = [];
      let subtotal = 0;

      // Transformar los items recibidos al formato esperado y obtener nombres de productos
      const transformedItems = await Promise.all(
        data.items.map(async (item: CreateSaleItemDto) => {
          const product = await Product.findById(item.productId);
          if (!product) {
            throw new Error(`Producto ${item.productId} no encontrado`);
          }

          if (product.stock < item.quantity) {
            throw new Error(
              `Stock insuficiente para el producto ${product.name}`
            );
          }

          // Agregar la operación de actualización de stock
          productUpdates.push({
            updateOne: {
              filter: { _id: item.productId },
              update: { $inc: { stock: -item.quantity } },
            },
          });

          const itemSubtotal =
            item.quantity * item.unitPrice - (item.discount || 0);
          subtotal += itemSubtotal;

          return {
            product: item.productId,
            name: product.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount || 0,
            subtotal: itemSubtotal,
          };
        })
      );

      const totalDiscount = data.discount || 0;
      const total = calculateTotal(
        data.items,
        data.discountType || 'fixed',
        totalDiscount
      );

      const saleNumber = await generateSaleNumber();

      // Crear la venta
      const sale = await Sale.create({
        number: saleNumber,
        date: new Date(),
        customer: data.customerId,
        items: transformedItems,
        subtotal,
        discountType: data.discountType,
        discount: totalDiscount,
        total,
        paymentMethod: data.paymentMethod,
        status: SALE_STATUS.COMPLETED,
        cashRegister: data.cashRegisterId,
        createdBy: userId,
        notes: data.notes,
        amountReceived: data.amountReceived,
      });

      // Actualizar stock de productos
      if (productUpdates.length > 0) {
        await Product.bulkWrite(productUpdates);
      }

      // Poblar los datos necesarios antes de retornar
      return await Sale.findById(sale._id)
        .populate('customer', 'businessName documentNumber')
        .populate('items.product', 'name sku')
        .populate('createdBy', 'username')
        .populate('cashRegister');
    } catch (error) {
      throw error;
    }
  }

  async updateSale(id: string, data: UpdateSaleDto, userId: string) {
    if (!userId) {
      throw new Error(SALE_ERRORS.INVALID_USER);
    }

    const sale = await Sale.findById(id);
    if (!sale) {
      throw new Error(SALE_ERRORS.NOT_FOUND);
    }

    if (sale.status === SALE_STATUS.CANCELED) {
      throw new Error(SALE_ERRORS.ALREADY_CANCELED);
    }

    // Actualizar solo los campos permitidos
    const updateData: any = {
      updatedBy: userId,
    };

    if (data.customer) updateData.customer = data.customer;
    if (data.paymentMethod) updateData.paymentMethod = data.paymentMethod;
    if (data.discount !== undefined) {
      updateData.discount = data.discount;
      updateData.total = sale.subtotal - data.discount;
    }

    try {
      const updatedSale = await Sale.findByIdAndUpdate(
        id,
        { $set: updateData },
        { new: true }
      );

      return updatedSale;
    } catch (error) {
      throw error;
    }
  }

  async cancelSale(id: string, data: CancelSaleDto, userId: string) {
    try {
      // Primero verificamos la venta y obtenemos todos los datos necesarios
      const sale = await Sale.findById(id)
        .populate({
          path: 'items.product',
          model: 'Product',
          select: 'name sku',
        })
        .populate('customer', 'businessName documentNumber')
        .populate('createdBy', 'username')
        .populate('canceledBy', 'username')
        .populate('cashRegister');

      if (!sale) {
        throw new Error(SALE_ERRORS.NOT_FOUND);
      }

      if (sale.status === SALE_STATUS.CANCELED) {
        throw new Error(SALE_ERRORS.ALREADY_CANCELED);
      }

      // Actualizamos el stock de los productos
      for (const item of sale.items) {
        await Product.updateOne(
          { _id: item.product },
          { $inc: { stock: item.quantity } }
        );
      }

      // Actualizamos la venta
      const updatedSale = await Sale.findByIdAndUpdate(
        id,
        {
          $set: {
            status: SALE_STATUS.CANCELED,
            cancelReason: data.reason,
            cancelNotes: data.notes,
            canceledAt: data.canceledAt || new Date(),
            canceledBy: userId,
          },
        },
        { new: true }
      )
        .populate({
          path: 'items.product',
          model: 'Product',
          select: 'name sku',
        })
        .populate('customer', 'businessName documentNumber')
        .populate('createdBy', 'username')
        .populate('canceledBy', 'username')
        .populate('cashRegister');

      if (!updatedSale) {
        throw new Error(SALE_ERRORS.NOT_FOUND);
      }

      // Transformamos los datos
      const transformedSale = {
        ...updatedSale,
        items: updatedSale.items.map((item) => ({
          ...item,
          name: item.product?.name || 'Producto no encontrado',
          productId: item.product?._id || item.product,
          product: item.product,
        })),
      };

      return transformedSale;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Error al cancelar la venta: ${error.message}`);
      }
      throw error;
    }
  }

  async getSaleById(id: string) {
    const sale = await Sale.findById(id)
      .populate({
        path: 'items.product',
        model: 'Product',
        select: 'name sku price code description',
      })
      .populate('customer', 'businessName documentNumber')
      .populate('createdBy', 'username')
      .populate('cashRegister')
      .lean();

    if (!sale) {
      throw new Error(SALE_ERRORS.NOT_FOUND);
    }

    return sale;
  }

  async getSales(query: any) {
    const {
      page = 1,
      limit = 10,
      startDate,
      endDate,
      status,
      customer,
      cashRegister,
    } = query;

    const filter: any = {};

    if (startDate && endDate) {
      filter.date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    if (status) filter.status = status;
    if (customer) filter.customer = customer;
    if (cashRegister) filter.cashRegister = cashRegister;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [sales, total] = await Promise.all([
      Sale.find(filter)
        .populate({
          path: 'items.product',
          model: 'Product',
          select: 'name sku price code description',
        })
        .populate('customer', 'businessName documentNumber')
        .populate('createdBy', 'username')
        .populate('cashRegister')
        .sort({ date: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Sale.countDocuments(filter),
    ]);

    return {
      sales,
      totalRecords: total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
    };
  }

  async deleteSale(id: string, userId: string) {
    if (!userId) {
      throw new Error(SALE_ERRORS.INVALID_USER);
    }

    const sale = await Sale.findById(id);
    if (!sale) {
      throw new Error(SALE_ERRORS.NOT_FOUND);
    }

    // Solo permitir eliminar ventas canceladas
    if (sale.status !== SALE_STATUS.CANCELED) {
      throw new Error(SALE_ERRORS.MUST_BE_CANCELED);
    }

    const deletedSale = await Sale.findByIdAndDelete(id);
    if (!deletedSale) {
      throw new Error(SALE_ERRORS.NOT_FOUND);
    }

    return deletedSale;
  }
}

export const saleService = new SaleService();

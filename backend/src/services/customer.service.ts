import { Types } from 'mongoose';
import { Customer } from '../models/customer.model.js';
import { CUSTOMER_ERRORS } from '../constants/customer.constants.js';
import type {
  CreateCustomerSchema,
  UpdateCustomerSchema,
} from '../schemas/customer.schema.js';

export const customerService = {
  async getCustomers(params: { search?: string; page: number; limit: number }) {
    const query: any = {};

    if (params.search) {
      query.$or = [
        { businessName: { $regex: params.search, $options: 'i' } },
        { documentNumber: { $regex: params.search, $options: 'i' } },
      ];
    }

    const skip = (params.page - 1) * params.limit;

    const [customers, total] = await Promise.all([
      Customer.find(query)
        .populate('createdBy', '_id username')
        .sort({ businessName: 1 })
        .skip(skip)
        .limit(params.limit)
        .lean(),
      Customer.countDocuments(query),
    ]);

    return {
      customers,
      totalRecords: total,
      totalPages: Math.ceil(total / params.limit),
      currentPage: params.page,
    };
  },

  async getCustomerById(id: string | Types.ObjectId) {
    const customer = await Customer.findById(id);
    if (!customer) {
      throw new Error(CUSTOMER_ERRORS.NOT_FOUND);
    }
    return customer;
  },

  async createCustomer(
    data: CreateCustomerSchema,
    userId: Types.ObjectId | string
  ) {
    // Normalizar el número de documento (eliminar espacios y convertir a mayúsculas)
    const normalizedDocNumber = data.documentNumber.trim().toUpperCase();

    // Buscar si existe un cliente con el mismo tipo y número de documento
    const existingCustomer = await Customer.findOne({
      documentType: data.documentType,
      documentNumber: { $regex: new RegExp(`^${normalizedDocNumber}$`, 'i') },
    });

    if (existingCustomer) {
      throw new Error(
        `Ya existe un cliente con el documento ${data.documentType} ${normalizedDocNumber}`
      );
    }

    // Crear el cliente con el número de documento normalizado
    return Customer.create({
      ...data,
      documentNumber: normalizedDocNumber,
      createdBy:
        typeof userId === 'string' ? new Types.ObjectId(userId) : userId,
    });
  },

  async updateCustomer(
    id: string | Types.ObjectId,
    data: UpdateCustomerSchema
  ) {
    const customer = await Customer.findById(id);
    if (!customer) {
      throw new Error(CUSTOMER_ERRORS.NOT_FOUND);
    }

    // Prevenir modificación de clientes protegidos
    if (customer.isProtected) {
      throw new Error('No se puede modificar un cliente protegido');
    }

    // Si se está actualizando el número de documento, verificar duplicados
    if (data.documentNumber) {
      const normalizedDocNumber = data.documentNumber.trim().toUpperCase();

      const existingCustomer = await Customer.findOne({
        _id: { $ne: id },
        documentType: data.documentType || customer.documentType,
        documentNumber: { $regex: new RegExp(`^${normalizedDocNumber}$`, 'i') },
      });

      if (existingCustomer) {
        throw new Error(
          `Ya existe otro cliente con el documento ${
            data.documentType || customer.documentType
          } ${normalizedDocNumber}`
        );
      }

      data.documentNumber = normalizedDocNumber;
    }

    return Customer.findByIdAndUpdate(id, { $set: data }, { new: true });
  },

  async deleteCustomer(id: string | Types.ObjectId) {
    const customer = await Customer.findById(id);
    if (!customer) {
      throw new Error(CUSTOMER_ERRORS.NOT_FOUND);
    }

    // Prevenir eliminación de clientes protegidos
    if (customer.isProtected) {
      throw new Error('No se puede eliminar un cliente protegido');
    }

    await Customer.findByIdAndDelete(id);
    return true;
  },

  async getCustomersForExport() {
    const customers = await Customer.find()
      .populate('createdBy', '_id username')
      .sort({ businessName: 1 })
      .lean();

    return { customers };
  },

  async getAllCustomers(isActive?: boolean) {
    const query: any = {};

    if (typeof isActive === 'boolean') {
      query.isActive = isActive;
    }
    const customers = await Customer.find(query)
      .populate('createdBy', '_id username')
      .sort({ businessName: 1 })
      .lean();

    return { customers };
  },
};

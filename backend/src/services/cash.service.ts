import { Types, SortOrder } from 'mongoose';
import { CashRegister } from '../models/cashRegister.model.js';
import { CashMovement } from '../models/cashMovement.model.js';
import {
  CASH_ERRORS,
  CASH_REGISTER_STATUS,
  CASH_MOVEMENT_TYPES,
  CASH_CONFIG,
} from '../constants/cash.constants.js';
import { API_CONSTANTS } from '../constants/api.constants.js';
import type {
  CreateCashMovementDto,
  UpdateCashMovementDto,
  CashMovementQueryParams,
  CashRegisterQueryParams,
  CashRegisterSummary,
  BalanceValidation,
} from '../interfaces/cash.interface.js';
import { parse, startOfDay, endOfDay } from 'date-fns';

export class CashService {
  public async getCurrentRegister() {
    try {
      const register = await CashRegister.findOne({ status: 'OPEN' })
        .populate('openedBy', 'username _id')
        .populate('closedBy', 'username _id')
        .sort({ createdAt: -1 });

      return register;
    } catch (error) {
      console.error('Error getting current register:', error);
      throw new Error(CASH_ERRORS.FETCH_ERROR);
    }
  }

  public async openCashRegister(
    userId: string,
    initialBalance: number,
    observations?: string
  ) {
    try {
      const existingOpenRegister = await CashRegister.findOne({
        status: 'OPEN',
      });
      if (existingOpenRegister) {
        throw new Error(CASH_ERRORS.REGISTER_ALREADY_OPEN);
      }

      const register = await CashRegister.create({
        openingDate: new Date(),
        initialBalance,
        openedBy: userId,
        createdBy: userId,
        observations,
      });

      return register.populate('openedBy', 'username email');
    } catch (error) {
      console.error('Error opening cash register:', error);
      throw error;
    }
  }

  public async closeCashRegister(
    id: string,
    userId: string,
    finalBalance: number,
    observations?: string,
    forceClose: boolean = false
  ) {
    const register = await CashRegister.findById(id);
    if (!register) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
    }

    if (register.status === CASH_REGISTER_STATUS.CLOSED) {
      throw new Error(CASH_ERRORS.REGISTER_ALREADY_CLOSED);
    }

    // We only validate the balance if it is not a forced closure
    if (!forceClose) {
      const validation = await this.validateClosingBalance(id, finalBalance);
      if (!validation.isBalanced) {
        throw new Error(CASH_ERRORS.INVALID_CLOSING_BALANCE);
      }
    }

    const now = new Date();
    const updateData = {
      finalBalance,
      observations: forceClose
        ? `[CIERRE FORZADO] ${observations || ''}`
        : observations,
      status: CASH_REGISTER_STATUS.CLOSED,
      closedBy: new Types.ObjectId(userId),
      closingDate: now,
      closedAt: now,
      updatedBy: new Types.ObjectId(userId),
      forcedClose: forceClose, // Opcional: agregar un campo para trackear cierres forzados
    };

    try {
      const updatedRegister = await CashRegister.findByIdAndUpdate(
        id,
        { $set: updateData },
        { new: true, runValidators: true }
      )
        .populate('openedBy', 'username email')
        .populate('closedBy', 'username email');

      if (!updatedRegister) {
        throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
      }

      return updatedRegister;
    } catch (error) {
      console.error('Error closing cash register:', error);
      throw error;
    }
  }

  public async createCashMovement(
    userId: string,
    registerId: string,
    movementData: CreateCashMovementDto
  ) {
    const register = await CashRegister.findById(registerId);
    if (!register || register.status !== CASH_REGISTER_STATUS.OPEN) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_OPEN);
    }

    const movement = await CashMovement.create({
      ...movementData,
      cashRegister: registerId, // Cambiado de register a cashRegister
      createdBy: new Types.ObjectId(userId),
    });

    await this.updateRegisterBalance(registerId);

    return movement.populate('createdBy', 'username email');
  }

  public async updateCashMovement(
    id: string,
    movementData: UpdateCashMovementDto,
    userId: string
  ) {
    const movement = await CashMovement.findById(id);
    if (!movement) {
      throw new Error(CASH_ERRORS.MOVEMENT_NOT_FOUND);
    }

    const register = await CashRegister.findById(movement.cashRegister);
    if (!register || register.status !== CASH_REGISTER_STATUS.OPEN) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_OPEN);
    }

    const updatedMovement = await CashMovement.findByIdAndUpdate(
      id,
      {
        ...movementData,
        updatedBy: new Types.ObjectId(userId),
      },
      { new: true }
    ).populate(['createdBy', 'updatedBy']);

    return updatedMovement;
  }

  public async getCashMovements(
    registerId: string,
    query: CashMovementQueryParams
  ) {
    try {
      const {
        page = API_CONSTANTS.PAGINATION.DEFAULT_PAGE,
        limit = API_CONSTANTS.PAGINATION.DEFAULT_LIMIT,
        type,
        startDate,
        endDate,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const filter: Record<string, any> = {
        cashRegister: new Types.ObjectId(registerId),
      };

      if (type && type !== CASH_MOVEMENT_TYPES.ALL) {
        filter.type = type;
      }

      if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) {
          filter.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          filter.createdAt.$lte = new Date(endDate);
        }
      }

      if (search) {
        filter.$or = [
          { concept: { $regex: search, $options: 'i' } },
          { reference: { $regex: search, $options: 'i' } },
        ];
      }

      const [movements, total] = await Promise.all([
        CashMovement.find(filter)
          .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
          .skip((Number(page) - 1) * Number(limit))
          .limit(Number(limit))
          .populate('createdBy', 'username')
          .lean(),
        CashMovement.countDocuments(filter),
      ]);

      return {
        movements,
        totalRecords: total,
        totalPages: Math.ceil(total / Number(limit)),
        currentPage: Number(page),
      };
    } catch (error) {
      console.error('Error in getCashMovements:', error);
      throw new Error(CASH_ERRORS.FETCH_ERROR);
    }
  }

  public async validateClosingBalance(
    registerId: string,
    finalBalance: number
  ): Promise<BalanceValidation> {
    const register = await CashRegister.findById(registerId);
    if (!register) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
    }

    if (register.status === CASH_REGISTER_STATUS.CLOSED) {
      throw new Error(CASH_ERRORS.REGISTER_ALREADY_CLOSED);
    }

    const expectedBalance = await this.calculateRegisterBalance(registerId);
    const difference = Number((finalBalance - expectedBalance).toFixed(2));

    return {
      expectedBalance,
      providedBalance: finalBalance,
      difference,
      isBalanced: Math.abs(difference) <= CASH_CONFIG.BALANCE_TOLERANCE,
    };
  }

  public async getRegisterSummary(
    registerId: string
  ): Promise<CashRegisterSummary> {
    const register = await CashRegister.findById(registerId);
    if (!register) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
    }

    const movements = await CashMovement.find({ cashRegister: registerId });

    // Calculate totals
    const summary = movements.reduce(
      (acc, movement) => {
        if (movement.type === CASH_MOVEMENT_TYPES.INCOME) {
          acc.totalIncome += movement.amount;
          acc.byType.INCOME.count += 1;
          acc.byType.INCOME.total += movement.amount;
        } else if (movement.type === CASH_MOVEMENT_TYPES.EXPENSE) {
          acc.totalExpense += movement.amount;
          acc.byType.EXPENSE.count += 1;
          acc.byType.EXPENSE.total += movement.amount;
        }
        return acc;
      },
      {
        initialBalance: register.initialBalance,
        totalIncome: 0,
        totalExpense: 0,
        byType: {
          INCOME: { count: 0, total: 0 },
          EXPENSE: { count: 0, total: 0 },
        },
      }
    );

    // Calculate the current balance
    const currentBalance =
      summary.initialBalance + summary.totalIncome - summary.totalExpense;

    return {
      initialBalance: summary.initialBalance,
      totalIncome: summary.totalIncome,
      totalExpense: summary.totalExpense,
      currentBalance,
      totalMovements: movements.length,
      byType: summary.byType,
      incomeCount: summary.byType.INCOME.count,
      expenseCount: summary.byType.EXPENSE.count,
    };
  }

  public async calculateRegisterBalance(registerId: string): Promise<number> {
    const register = await CashRegister.findById(registerId);
    if (!register) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
    }

    const movements = await CashMovement.find({ cashRegister: registerId });

    const balance = movements.reduce((acc, movement) => {
      return movement.type === CASH_MOVEMENT_TYPES.INCOME
        ? acc + movement.amount
        : acc - movement.amount;
    }, register.initialBalance);

    return Number(balance.toFixed(2));
  }

  public async getRegisterDetails(registerId: string) {
    try {
      const register = await CashRegister.findById(registerId)
        .populate('openedBy', 'username email')
        .populate('closedBy', 'username email')
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email');

      if (!register) {
        throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
      }

      const movements = await CashMovement.find({ cashRegister: registerId })
        .sort({ createdAt: -1 })
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email');

      const summary = await this.getRegisterSummary(registerId);

      return {
        register,
        movements,
        summary,
      };
    } catch (error) {
      console.error('Error getting register details:', error);
      throw error;
    }
  }

  public async getCashRegistersHistory(query: CashRegisterQueryParams) {
    try {
      const {
        page = 1,
        limit = 10,
        startDate,
        endDate,
        status,
        search,
      } = query;

      const filter: Record<string, any> = {};

      if (startDate || endDate) {
        filter.openingDate = {};
        if (startDate) {
          // Parsear la fecha YYYY-MM-DD y obtener inicio del día
          const parsedDate = parse(
            startDate as string,
            'yyyy-MM-dd',
            new Date()
          );
          filter.openingDate.$gte = startOfDay(parsedDate);
        }
        if (endDate) {
          // Parsear la fecha YYYY-MM-DD y obtener fin del día
          const parsedDate = parse(endDate as string, 'yyyy-MM-dd', new Date());
          filter.openingDate.$lte = endOfDay(parsedDate);
        }
      }

      if (status && status !== 'ALL') {
        filter.status = status;
      }

      if (search) {
        filter.$or = [
          { 'openedBy.username': { $regex: search, $options: 'i' } },
          { 'closedBy.username': { $regex: search, $options: 'i' } },
          { observations: { $regex: search, $options: 'i' } },
        ];
      }

      const [registers, total] = await Promise.all([
        CashRegister.find(filter)
          .populate('openedBy', 'username')
          .populate('closedBy', 'username')
          .sort({ openingDate: -1 })
          .skip((Number(page) - 1) * Number(limit))
          .limit(Number(limit))
          .lean(),
        CashRegister.countDocuments(filter),
      ]);

      return {
        registers,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
        totalRecords: total,
      };
    } catch (error) {
      console.error('Error getting cash registers history:', error);
      throw new Error(CASH_ERRORS.FETCH_ERROR);
    }
  }

  public async deleteCashMovement(id: string, userId: string) {
    try {
      const movement = await CashMovement.findById(id);
      if (!movement) {
        throw new Error(CASH_ERRORS.MOVEMENT_NOT_FOUND);
      }

      // Check if the box is open
      const register = await CashRegister.findById(movement.cashRegister);
      if (!register) {
        throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
      }

      if (register.status !== CASH_REGISTER_STATUS.OPEN) {
        throw new Error(CASH_ERRORS.MOVEMENT_LOCKED);
      }

      // Check if the user is the creator of the movement or has special permissions
      if (movement.createdBy.toString() !== userId) {
        throw new Error(CASH_ERRORS.UNAUTHORIZED_DELETE);
      }

      await CashMovement.findByIdAndDelete(id);

      await this.updateRegisterBalance(register._id.toString());

      return true;
    } catch (error) {
      console.error('Error deleting cash movement:', error);
      throw error;
    }
  }

  private async updateRegisterBalance(registerId: string) {
    const currentBalance = await this.calculateRegisterBalance(registerId);
    await CashRegister.findByIdAndUpdate(registerId, { currentBalance });
  }

  public async getAllCashMovementsForExport(registerId: string) {
    const register = await CashRegister.findById(registerId)
      .populate('openedBy', 'username email')
      .populate('closedBy', 'username email')
      .lean();

    if (!register) {
      throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
    }

    // Obtener todos los movimientos sin límite y ordenados por fecha
    const movements = await CashMovement.find({ cashRegister: registerId })
      .sort({ createdAt: -1 })
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .lean();

    const summary = await this.getRegisterSummary(registerId);

    return {
      register,
      movements,
      summary,
      metadata: {
        generatedAt: new Date(),
        totalMovements: movements.length,
      },
    };
  }

  public async deleteCashRegister(id: string) {
    try {
      const register = await CashRegister.findById(id);
      if (!register) {
        throw new Error(CASH_ERRORS.REGISTER_NOT_FOUND);
      }

      // Eliminar todos los movimientos asociados
      await CashMovement.deleteMany({ cashRegister: id });

      // Eliminar el registro de caja
      await CashRegister.findByIdAndDelete(id);

      return {
        registerId: id,
        movements: await CashMovement.countDocuments({ cashRegister: id }),
      };
    } catch (error) {
      console.error('Error deleting cash register:', error);
      throw error;
    }
  }
}

export const cashService = new CashService();

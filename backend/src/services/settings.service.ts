import fs from 'fs/promises';
import path from 'path';
import { Settings } from '../models/settings.model.js';
import type { IGeneralSettings } from '../interfaces/settings.interface.js';
import { SETTINGS_ERRORS } from '../constants/settings.constants.js';

export const settingsService = {
  async getGeneralSettings() {
    const settings = await Settings.findOne();
    if (!settings?.general) {
      throw new Error(SETTINGS_ERRORS.NOT_FOUND);
    }
    return settings.general;
  },

  async getPublicSettings() {
    const settings = await Settings.findOne();
    return {
      systemName: settings?.general?.systemName || 'Sistema',
      logo: settings?.general?.logo || null,
    };
  },

  async updateGeneralSettings(generalSettings: IGeneralSettings) {
    try {
      // Si logo es null y hay un logo existente, eliminarlo
      if (generalSettings.logo === null) {
        const currentSettings = await Settings.findOne();
        if (currentSettings?.general?.logo) {
          await this.deleteLogoFile(currentSettings.general.logo);
        }
      }

      // Estructurar los datos correctamente con el objeto 'general'
      const updatedSettings = await Settings.findOneAndUpdate(
        {},
        {
          $set: {
            general: {
              systemName: generalSettings.systemName,
              businessName: generalSettings.businessName,
              address: generalSettings.address,
              phone: generalSettings.phone,
              email: generalSettings.email,
              taxId: generalSettings.taxId,
              description: generalSettings.description,
              logo: generalSettings.logo,
            },
          },
        },
        {
          new: true,
          upsert: true,
          runValidators: true,
        }
      );

      if (!updatedSettings?.general) {
        throw new Error(SETTINGS_ERRORS.UPDATE_FAILED);
      }

      return updatedSettings.general;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`${SETTINGS_ERRORS.UPDATE_ERROR}: ${error.message}`);
      }
      throw new Error(SETTINGS_ERRORS.UPDATE_ERROR);
    }
  },

  async deleteLogoFile(logoPath: string) {
    try {
      if (!logoPath) return;

      // Remove the initial /uploads/ from the path if it exists
      const cleanPath = logoPath.replace(/^\/uploads\//, '');

      // Build the full path to the file
      const fullPath = path.join(process.cwd(), 'uploads', cleanPath);

      // Check if file exists before attempting to delete
      try {
        await fs.access(fullPath);
        // Delete the file
        await fs.unlink(fullPath);
      } catch (error) {
        // Silent fail if file doesn't exist
      }
    } catch (error) {
      throw new Error(SETTINGS_ERRORS.DELETE_LOGO_FAILED);
    }
  },
};

import { User } from '../models/user.model.js';
import { generateToken } from '../utils/jwt.utils.js';
import { AUTH_ERRORS } from '../constants/auth.constants.js';
import type {
  LoginCredentials,
  LoginResponse,
  JwtPayload,
} from '../interfaces/auth.interface.js';

class AuthService {
  /**
   * Authenticate user and generate JWT token
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const { username, password } = credentials;

    const user = await User.findOne({ username }).select('+password');

    if (!user) {
      throw new Error(AUTH_ERRORS.INVALID_CREDENTIALS);
    }

    const isValidPassword = await user.comparePassword(password);

    if (!isValidPassword) {
      throw new Error(AUTH_ERRORS.INVALID_CREDENTIALS);
    }

    if (!user.isActive) {
      throw new Error(AUTH_ERRORS.USER_INACTIVE);
    }

    const payload: JwtPayload = {
      _id: user._id.toString(),
      username: user.username,
      role: user.role,
      permissions: user.permissions,
      isActive: user.isActive,
    };

    const token = generateToken({
      ...payload,
      _id: payload._id.toString(),
    });

    return {
      token,
      user: {
        _id: user._id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        isActive: user.isActive,
      },
    };
  }
}

export const authService = new AuthService();

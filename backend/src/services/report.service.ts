import { Sale } from '../models/sale.model.js';
import type { GetSalesReportFilters } from '../interfaces/report.interface.js';
import { Product } from '../models/product.model.js';

export class ReportService {
  async getSalesReport(filters: GetSalesReportFilters, userId: string) {
    const { startDate, endDate, status, customerId, cashRegisterId, search } =
      filters;

    const query: any = {};

    if (startDate || endDate) {
      query.date = {};
      if (startDate) {
        query.date.$gte = startDate; // Ya viene formateada del controller
      }
      if (endDate) {
        query.date.$lte = endDate; // Ya viene formateada del controller
      }
    }

    if (status) query.status = status;
    if (customerId) query.customer = customerId;
    if (cashRegisterId) query.cashRegister = cashRegisterId;

    if (search) {
      query.$or = [
        { number: { $regex: search, $options: 'i' } },
        { 'customer.businessName': { $regex: search, $options: 'i' } },
      ];
    }

    const sales = await Sale.find(query)
      .populate('customer', 'businessName documentNumber documentType')
      .populate('cashRegister')
      .populate('createdBy', 'username')
      .populate('items.product', 'name code price')
      .sort({ date: -1 })
      .lean();

    // Calcular totales y resumen
    const summary = {
      totalSales: sales.length,
      totalAmount: sales.reduce((acc, sale) => acc + sale.total, 0),
      completedSales: sales.filter((sale) => sale.status === 'COMPLETED')
        .length,
      canceledSales: sales.filter((sale) => sale.status === 'CANCELED').length,
    };

    return {
      sales,
      summary,
      total: sales.length,
    };
  }

  async getInventoryReport(filters: any, userId: string) {
    const {
      search,
      categoryId,
      laboratoryId,
      stockAlert,
      expirationAlert,
      isActive,
    } = filters;

    const query: any = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
      ];
    }

    if (categoryId) {
      query.category = categoryId;
    }

    if (laboratoryId) {
      query.laboratory = laboratoryId;
    }

    // Corregir la consulta de stock bajo
    if (stockAlert === true || stockAlert === 'true') {
      query.$expr = {
        $lte: ['$stock', '$minStock'],
      };
    }

    // Corregir la consulta de productos por vencer
    if (expirationAlert === true || expirationAlert === 'true') {
      const today = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(today.getDate() + 30);

      query.expirationDate = {
        $exists: true,
        $ne: null,
        $gte: today,
        $lte: thirtyDaysFromNow,
      };
    }

    if (typeof isActive === 'boolean') {
      query.isActive = isActive;
    }

    const products = await Product.find(query)
      .populate('category', 'name')
      .populate('laboratory', 'name')
      .sort({ name: 1 })
      .lean();

    // Calcular resumen
    const summary = {
      totalProducts: products.length,
      totalStock: products.reduce(
        (acc, product) => acc + (product.stock || 0),
        0
      ),
      totalValue: products.reduce(
        (acc, product) => acc + (product.cost || 0) * (product.stock || 0),
        0
      ),
      lowStockProducts: products.filter(
        (product) => (product.stock || 0) <= (product.minStock || 0)
      ).length,
      expiringProducts: products.filter((product) => {
        if (!product.expirationDate) return false;
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);
        return new Date(product.expirationDate) <= thirtyDaysFromNow;
      }).length,
      activeProducts: products.filter((product) => product.isActive).length,
    };

    return {
      products,
      summary,
    };
  }
}

export const reportService = new ReportService();

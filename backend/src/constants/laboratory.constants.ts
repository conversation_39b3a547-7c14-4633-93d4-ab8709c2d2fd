export const LABORATORY_ERRORS = {
  FETCH_ERROR: 'Error al obtener los laboratorios',
  NOT_FOUND: 'Laboratorio no encontrado',
  NAME_TAKEN: 'Ya existe un laboratorio con ese nombre',
  CREATION_FAILED: 'Error al crear el laboratorio',
  UPDATE_FAILED: 'Error al actualizar el laboratorio',
  DELETION_FAILED: 'Error al eliminar el laboratorio',
  STATUS_UPDATE_FAILED: 'Error al actualizar el estado del laboratorio',
  PROTECTED_LABORATORY:
    'No se puede eliminar el laboratorio "Sin laboratorio" ya que es un laboratorio protegido del sistema',
} as const;

export const LABORATORY_MESSAGES = {
  DELETED: 'Laboratorio eliminado exitosamente',
  STATUS_UPDATED: (isActive: boolean) =>
    `Estado del laboratorio actualizado a ${isActive ? 'activo' : 'inactivo'}`,
  PRODUCTS_MOVED: (count: number) =>
    `${count} producto${count === 1 ? '' : 's'} ${
      count === 1 ? 'ha sido movido' : 'han sido movidos'
    } al laboratorio "Sin laboratorio"`,
} as const;

export const LABORATORY_VALIDATION = {
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    MESSAGE: {
      MIN: 'El nombre debe tener al menos 2 caracteres',
      MAX: 'El nombre no puede exceder los 50 caracteres',
    },
  },
  DESCRIPTION: {
    MAX_LENGTH: 500,
    MESSAGE: 'La descripción no puede exceder los 500 caracteres',
  },
  COUNTRY: {
    MAX_LENGTH: 50,
    MESSAGE: 'El país no puede exceder los 50 caracteres',
  },
  WEBSITE: {
    MAX_LENGTH: 200,
    MESSAGE: {
      URL: 'Debe ser una URL válida',
      MAX: 'La URL no puede exceder los 200 caracteres',
    },
  },
} as const;

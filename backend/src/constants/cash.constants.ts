export const CASH_MOVEMENT_TYPES = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  ALL: 'ALL',
} as const;

export type MovementType =
  (typeof CASH_MOVEMENT_TYPES)[keyof typeof CASH_MOVEMENT_TYPES];

export const CASH_REGISTER_STATUS = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
  ALL: 'ALL',
} as const;

export type CashRegisterStatus =
  (typeof CASH_REGISTER_STATUS)[keyof typeof CASH_REGISTER_STATUS];

export const CASH_CONFIG = {
  BALANCE_TOLERANCE: 0.01,
  MAX_OBSERVATIONS_LENGTH: 200,
  MAX_REFERENCE_LENGTH: 50,
  MIN_AMOUNT: 0,
} as const;

export const CASH_ERRORS = {
  REGISTER_NOT_FOUND: 'Registro de caja no encontrado',
  REGISTER_ALREADY_CLOSED: 'El registro de caja ya está cerrado',
  REGISTER_ALREADY_OPEN: 'Ya existe un registro de caja abierto',
  REGISTER_NOT_OPEN: 'No hay un registro de caja abierto',
  MOVEMENT_NOT_FOUND: 'Movimiento no encontrado',
  FETCH_ERROR: 'Error al obtener los datos',
  CREATION_ERROR: 'Error al crear el registro',
  UPDATE_ERROR: 'Error al actualizar el registro',
  DELETE_ERROR: 'Error al eliminar el registro',
  CLOSE_ERROR: 'Error al cerrar el registro',
  VALIDATION_ERROR: 'Error en la validación',
  INVALID_CLOSING_BALANCE: 'El balance de cierre no coincide con el esperado',
  UNAUTHORIZED_OPERATION: 'No está autorizado para realizar esta operación',
  UNAUTHORIZED_DELETE: 'No está autorizado para eliminar este movimiento',
  MOVEMENT_LOCKED: 'No se puede modificar un movimiento de una caja cerrada',
  CALCULATION_ERROR: 'Error al calcular el balance',
  EXPORT_ERROR: 'Error al exportar los datos',
} as const;

export const CASH_MESSAGES = {
  REGISTER_CLOSED: 'Registro de caja cerrado exitosamente',
  REGISTER_OPENED: 'Caja abierta correctamente',
  MOVEMENT_CREATED: 'Movimiento creado correctamente',
  MOVEMENT_UPDATED: 'Movimiento actualizado correctamente',
  MOVEMENT_DELETED: 'Movimiento eliminado correctamente',
  REGISTER_DELETED: 'Registro de caja eliminado correctamente',
} as const;

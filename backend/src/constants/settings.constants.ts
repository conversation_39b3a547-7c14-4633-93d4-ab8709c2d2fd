import { config } from '../config/config.js';

export const SETTINGS_MESSAGES = {
  LOGO_REMOVED: 'Logo eliminado exitosamente',
} as const;

export const SETTINGS_ERRORS = {
  FETCH_ERROR: 'Error al obtener la configuración',
  NOT_FOUND: 'Configuración no encontrada',
  UPDATE_FAILED: 'Error al actualizar la configuración',
  DELETE_LOGO_FAILED: 'Error al eliminar el archivo del logo',
  UPDATE_ERROR: 'Error al actualizar la configuración',
  INVALID_JSON: 'Formato JSON inválido para la configuración',
  INVALID_FILE_TYPE:
    'Tipo de archivo no permitido. Solo se permiten imágenes (JPEG, PNG, WEBP)',
  FILE_TOO_LARGE: 'El tamaño del archivo excede el límite permitido',
} as const;

export const SETTINGS_VALIDATION = {
  SYSTEM_NAME: {
    MAX_LENGTH: 100,
    REQUIRED: 'El nombre del sistema es requerido',
    MAX_MESSAGE: 'El nombre del sistema no puede exceder 100 caracteres',
  },
  BUSINESS_NAME: {
    MAX_LENGTH: 100,
    REQUIRED: 'El nombre del negocio es requerido',
    MAX_MESSAGE: 'El nombre del negocio no puede exceder 100 caracteres',
  },
  ADDRESS: {
    MAX_LENGTH: 200,
    REQUIRED: 'La dirección es requerida',
    MAX_MESSAGE: 'La dirección no puede exceder 200 caracteres',
  },
  DESCRIPTION: {
    MAX_LENGTH: 500,
    MAX_MESSAGE: 'La descripción no puede exceder 500 caracteres',
  },
  PHONE: {
    REQUIRED: 'El teléfono es requerido',
    REGEX:
      /^\+?[0-9]{1,4}?[-.\s]?\(?[0-9]{1,3}?\)?[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,9}$/,
    INVALID_FORMAT: 'El formato del teléfono no es válido',
  },
  EMAIL: {
    REQUIRED: 'El correo electrónico es requerido',
    REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    INVALID_FORMAT: 'El formato del correo electrónico no es válido',
  },
  TAX_ID: {
    REQUIRED: 'El RUC/NIT es requerido',
    REGEX: /^[A-Z0-9\-\s]{1,20}$/i,
    INVALID_FORMAT: 'El formato del RUC/NIT no es válido',
  },
  LOGO: {
    MAX_SIZE: config.files.maxLogoSize,
    ALLOWED_MIME_TYPES: [
      'image/jpeg',
      'image/png',
      'image/webp',
    ] as readonly string[],
  },
} as const;

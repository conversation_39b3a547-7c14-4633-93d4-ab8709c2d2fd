import { config } from '../config/config.js';

export const PRODUCT_ERRORS = {
  FETCH_ERROR: 'Error al obtener los productos',
  CREATION_FAILED: 'Error al crear el producto',
  UPDATE_FAILED: 'Error al actualizar el producto',
  DELETION_FAILED: 'Error al eliminar el producto',
  NOT_FOUND: 'Producto no encontrado',
  SKU_TAKEN: 'El SKU ya está en uso',
  NAME_TAKEN: 'El nombre del producto ya está en uso',
  INSUFFICIENT_STOCK: 'Stock insuficiente',
  INVALID_EXPIRATION_DATE:
    'La fecha de caducidad no puede ser menor a la fecha actual',
  INVALID_UPDATE_FIELDS: 'Campos de actualización no válidos',
  UPDATE_STOCK_FAILED: 'Error al actualizar el stock',
  INVALID_IMAGE_TYPE:
    'Tipo de archivo no permitido. Solo se permiten imágenes (JPEG, PNG, WEBP)',
  INVALID_IMAGE_SIZE: 'El tamaño de la imagen excede el límite permitido',
} as const;

export const PRODUCT_MESSAGES = {
  CREATED: 'Producto creado exitosamente',
  UPDATED: 'Producto actualizado exitosamente',
  DELETED: 'Producto eliminado exitosamente',
  STOCK_UPDATED: 'Stock actualizado exitosamente',
  LOW_STOCK_ALERT: 'Productos con stock bajo detectados',
  EXPIRING_ALERT: 'Productos próximos a vencer detectados',
} as const;

export const PRODUCT_VALIDATION = {
  ALLOWED_MIME_TYPES: ['image/jpeg', 'image/png', 'image/webp'] as const,
  MAX_IMAGE_SIZE: config.files.maxImageSize,
  SKU: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    MESSAGE: 'El SKU debe tener entre 3 y 50 caracteres',
  },
  NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
    MESSAGE: 'El nombre debe tener entre 3 y 100 caracteres',
  },
  DESCRIPTION: {
    MAX_LENGTH: 500,
    MESSAGE: 'La descripción no debe exceder los 500 caracteres',
    REQUIRED: 'La descripción es requerida',
  },
  PRICE: {
    MIN: 0,
    MESSAGE: 'El precio debe ser mayor a 0',
    COST_MESSAGE: 'El costo debe ser mayor o igual a 0',
  },
  STOCK: {
    MIN: 0,
    MESSAGE: 'El stock no puede ser negativo',
    MIN_MESSAGE: 'El stock mínimo debe ser mayor o igual a 0',
    MAX_MESSAGE: 'El stock máximo debe ser mayor o igual a 0',
    COMPARISON_MESSAGE:
      'El stock máximo debe ser mayor o igual que el stock mínimo',
  },
  IMAGE: {
    MAX_SIZE: Number(process.env.MAX_IMAGE_SIZE_MB) * 1024 * 1024, // 5MB
    ALLOWED_TYPES: [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
    ] as const,
    SIZE_MESSAGE: 'El tamaño máximo de la imagen es 5MB',
    TYPE_MESSAGE: 'Formato de imagen no soportado. Use JPEG, PNG o WEBP',
  },
  REQUIRED_FIELDS: {
    LABORATORY: 'El laboratorio es requerido',
    CATEGORY: 'La categoría es requerida',
    LOCATION: 'La ubicación es requerida',
    BATCH_NUMBER: 'El número de lote es requerido',
    PRESENTATION: 'La presentación es requerida',
    CONCENTRATION: 'La concentración es requerida',
    SANITARY_REGISTRATION: 'El registro sanitario es requerido',
  },
  ENUMS: {
    MEASUREMENT_UNIT: {
      VALUES: [
        'TABLETA',
        'CAPSULA',
        'AMPOLLA',
        'FRASCO',
        'CREMA',
        'GOTAS',
      ] as const,
      MESSAGE: 'Unidad de medida inválida',
    },
    ADMINISTRATION_ROUTE: {
      VALUES: ['ORAL', 'INYECTABLE', 'TOPICA', 'OFTALMICA', 'OTRO'] as const,
      MESSAGE: 'Vía de administración inválida',
    },
    STORAGE_CONDITION: {
      VALUES: ['TEMPERATURA_AMBIENTE', 'REFRIGERACION', 'CONGELACION'] as const,
      MESSAGE: 'Condición de almacenamiento inválida',
    },
  },
  DATES: {
    EXPIRATION_MESSAGE:
      'La fecha de vencimiento debe ser posterior a la fecha actual',
  },
  NUMBER: {
    INVALID: 'Debe ser un número válido',
  },
  STOCK_MOVEMENT: {
    TYPE: {
      VALUES: ['IN', 'OUT', 'RETURN'] as const,
      MESSAGE: 'Tipo de movimiento inválido',
    },
    QUANTITY: {
      MIN: 1,
      MESSAGE: 'La cantidad debe ser mayor a 0',
    },
    REASON: 'La razón es requerida',
  },
} as const;

export const EXPIRATION_ALERT_DAYS = 30;
export const LOW_STOCK_THRESHOLD = 5;

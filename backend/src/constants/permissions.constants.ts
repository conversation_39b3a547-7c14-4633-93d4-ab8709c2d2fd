export const PERMISSIONS = {
  // Módulos principales
  INVENTORY: {
    PRODUCTS: {
      LIST: 'inventory:products:list',
      CREATE: 'inventory:products:create',
      EDIT: 'inventory:products:edit',
      DELETE: 'inventory:products:delete',
    },
    STOCK: {
      MOVEMENTS: {
        LIST: 'inventory:stock:movements:list',
        CREATE: 'inventory:stock:movements:create',
        EDIT: 'inventory:stock:movements:edit',
        DELETE: 'inventory:stock:movements:delete',
      },
      ENTRIES: {
        LIST: 'inventory:stock:entries:list',
        CREATE: 'inventory:stock:entries:create',
        EDIT: 'inventory:stock:entries:edit',
        DELETE: 'inventory:stock:entries:delete',
      },
      OUTPUTS: {
        LIST: 'inventory:stock:outputs:list',
        CREATE: 'inventory:stock:outputs:create',
        EDIT: 'inventory:stock:outputs:edit',
        DELETE: 'inventory:stock:outputs:delete',
      },
    },
  },
  CUSTOMERS: {
    LIST: 'customers:list',
    CREATE: 'customers:create',
    EDIT: 'customers:edit',
    DELETE: 'customers:delete',
  },
  CASH: {
    LIST: 'cash:list',
    MANAGE: 'cash:manage',
  },
  SALES: {
    LIST: 'sales:list',
    CREATE: 'sales:create',
    EDIT: 'sales:edit',
    DELETE: 'sales:delete',
    CANCEL: 'sales:cancel',
  },
  // Entidades base
  CATEGORIES: {
    LIST: 'categories:list',
    CREATE: 'categories:create',
    EDIT: 'categories:edit',
    DELETE: 'categories:delete',
  },
  LABORATORIES: {
    LIST: 'laboratories:list',
    CREATE: 'laboratories:create',
    EDIT: 'laboratories:edit',
    DELETE: 'laboratories:delete',
  },
  // Módulos de sistema
  USERS: {
    LIST: 'users:list',
    CREATE: 'users:create',
    EDIT: 'users:edit',
    DELETE: 'users:delete',
    CHANGE_PASSWORD: 'users:change-password',
  },
  REPORTS: {
    SALES: {
      LIST: 'reports:sales:list',
    },
    INVENTORY: {
      LIST: 'reports:inventory:list',
    },
  },
  SETTINGS: {
    LIST: 'settings:list',
    EDIT: 'settings:edit',
  },
} as const;

export const USER_ROLES = {
  ADMIN: 'admin',
  CASHIER: 'cashier',
} as const;

export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    '*', // Admin tiene todos los permisos
  ],
  [USER_ROLES.CASHIER]: [
    // Inventario - Productos
    PERMISSIONS.INVENTORY.PRODUCTS.LIST,
    // Inventario - Stock
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST,
    PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST,
    PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST,
    PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE,
    // Ventas
    PERMISSIONS.SALES.LIST,
    PERMISSIONS.SALES.CREATE,
    PERMISSIONS.SALES.CANCEL,
    // Categorías y Laboratorios (solo lectura)
    PERMISSIONS.CATEGORIES.LIST,
    PERMISSIONS.LABORATORIES.LIST,
    // Reportes
    PERMISSIONS.REPORTS.SALES.LIST,
    PERMISSIONS.REPORTS.INVENTORY.LIST,
    // Caja
    PERMISSIONS.CASH.LIST,
    PERMISSIONS.CASH.MANAGE,
    // Clientes (acceso limitado)
    PERMISSIONS.CUSTOMERS.LIST,
    PERMISSIONS.CUSTOMERS.CREATE,
    PERMISSIONS.CUSTOMERS.EDIT,
  ],
} as const;

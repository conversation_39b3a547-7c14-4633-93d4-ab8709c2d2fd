export const USER_ROLES = {
  ADMIN: 'admin',
  CASHIER: 'cashier',
} as const;

export const USER_VALIDATION = {
  USERNAME_MIN_LENGTH: 3,
  PASSWORD_MIN_LENGTH: 6,
} as const;

export const USER_MESSAGES = {
  DELETED: 'Usuario eliminado correctamente',
  PASSWORD_UPDATED: 'Contraseña actualizada correctamente',
  PERMISSIONS_UPDATED: 'Permisos actualizados correctamente',
  STATUS_UPDATED: (isActive: boolean) =>
    `Usuario ${isActive ? 'activado' : 'desactivado'} correctamente`,
} as const;

export const USER_ERRORS = {
  CREATION_FAILED: 'Error al crear usuario',
  UPDATE_FAILED: 'Error al actualizar usuario',
  DELETION_FAILED: 'Error al eliminar usuario',
  PASSWORD_CHANGE_FAILED: 'Error al cambiar contraseña',
  FETCH_ERROR: 'Error al obtener usuarios',
  PROFILE_FETCH_ERROR: 'Error al obtener perfil',
  PERMISSIONS_UPDATE_ERROR: 'Error al actualizar permisos',
  STATUS_UPDATE_ERROR: 'Error al modificar el estado del usuario',

  // Validation
  INVALID_ROLE: 'Rol inválido',
  INVALID_PERMISSIONS: 'Los permisos deben ser un array',
  INVALID_PASSWORD_LENGTH: `La contraseña debe tener al menos ${USER_VALIDATION.USERNAME_MIN_LENGTH} caracteres`,
  USERNAME_TAKEN:
    'El nombre de usuario ya está en uso. Por favor, elija otro nombre de usuario.',

  // Restrictions for superAdmin
  SUPERADMIN_ROLE_CHANGE:
    'No es posible cambiar el rol del administrador principal del sistema.',
  SUPERADMIN_USERNAME_CHANGE:
    'No es posible cambiar el nombre de usuario del administrador principal del sistema.',
  SUPERADMIN_DELETE:
    'No es posible eliminar al administrador principal del sistema.',
  SUPERADMIN_PERMISSIONS:
    'No es posible modificar los permisos del administrador principal del sistema.',
  SUPERADMIN_STATUS:
    'No es posible modificar el estado del administrador principal del sistema.',
  SUPERADMIN_PASSWORD:
    'Solo el administrador principal puede cambiar su propia contraseña.',

  // Restrictions for user
  SELF_STATUS_CHANGE: 'No puedes modificar tu propio estado de activación',
} as const;

export const SUPERADMIN_USERNAME = 'superadmin';
export const SUPERADMIN_IDENTIFIER = 'isSuperAdmin';

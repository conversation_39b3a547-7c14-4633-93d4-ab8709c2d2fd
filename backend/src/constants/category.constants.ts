export const CATEGORY_ERRORS = {
  CREATION_FAILED: 'Error al crear la categoría',
  UPDATE_FAILED: 'Error al actualizar la categoría',
  DELETION_FAILED: 'Error al eliminar la categoría',
  FETCH_ERROR: 'Error al obtener las categorías',
  NOT_FOUND: 'Categoría no encontrada',
  NAME_TAKEN: 'Ya existe una categoría con ese nombre',
  STATUS_UPDATE_FAILED: 'Error al actualizar el estado de la categoría',
  HAS_PRODUCTS: 'La categoría tiene productos asociados',
  PROTECTED_CATEGORY:
    'No se puede eliminar una categoría protegida del sistema',
} as const;

export const CATEGORY_MESSAGES = {
  CREATED: 'Categoría creada exitosamente',
  UPDATED: 'Categoría actualizada exitosamente',
  DELETED: 'Categoría eliminada exitosamente',
  STATUS_UPDATED: (isActive: boolean) =>
    `Categoría ${isActive ? 'activada' : 'desactivada'} exitosamente`,
  PRODUCTS_MOVED: (count: number) =>
    `Se movieron ${count} productos a la categoría "Sin categoría"`,
} as const;

export const CATEGORY_VALIDATION = {
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    MESSAGE: 'El nombre debe tener entre 2 y 50 caracteres',
    REQUIRED: 'El nombre es requerido',
  },
  DESCRIPTION: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 500,
    MESSAGE: 'La descripción debe tener entre 5 y 500 caracteres',
    REQUIRED: 'La descripción es requerida',
  },
} as const;

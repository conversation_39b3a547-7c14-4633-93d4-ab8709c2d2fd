import { Router } from 'express';
import { categoryController } from '../controllers/category.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  createCategorySchema,
  updateCategorySchema,
  deleteCategorySchema,
  toggleStatusSchema,
} from '../schemas/category.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authMiddleware);

// GET /api/categories
router.get(
  '/',
  checkPermission(PERMISSIONS.CATEGORIES.LIST),
  categoryController.getCategories.bind(categoryController)
);

router.get(
  '/all',
  checkPermission(PERMISSIONS.CATEGORIES.LIST),
  categoryController.getAllCategories.bind(categoryController)
);

// GET /api/categories/active
router.get(
  '/active',
  checkPermission(PERMISSIONS.CATEGORIES.LIST),
  categoryController.getActiveCategories.bind(categoryController)
);

// POST /api/categories
router.post(
  '/',
  checkPermission(PERMISSIONS.CATEGORIES.CREATE),
  validateRequest(createCategorySchema),
  categoryController.createCategory.bind(categoryController)
);

// PATCH /api/categories/:id
router.patch(
  '/:id',
  checkPermission(PERMISSIONS.CATEGORIES.EDIT),
  validateRequest(updateCategorySchema),
  categoryController.updateCategory.bind(categoryController)
);

// DELETE /api/categories/:id
router.delete(
  '/:id',
  checkPermission(PERMISSIONS.CATEGORIES.DELETE),
  validateRequest(deleteCategorySchema),
  categoryController.deleteCategory.bind(categoryController)
);

// PATCH /api/categories/:id/toggle-status
router.patch(
  '/:id/toggle-status',
  checkPermission(PERMISSIONS.CATEGORIES.EDIT),
  validateRequest(toggleStatusSchema),
  categoryController.toggleCategoryStatus.bind(categoryController)
);

export default router;

import { Router } from 'express';
import { loginSchema } from '../schemas/auth.schema.js';
import { validateSchema } from '../middleware/validateSchema.js';
import { AuthController } from '../controllers/auth.controller.js';

const router = Router();
const authController = new AuthController();

router.post('/login', validateSchema(loginSchema), (req, res, next) => {
  return authController.login(req, res, next);
});

export default router;

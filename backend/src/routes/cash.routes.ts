import { Router } from 'express';
import { cashController } from '../controllers/cash.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  openCashRegisterSchema,
  closeCashRegisterSchema,
  createCashMovementSchema,
  updateCashMovementSchema,
  getCashMovementsSchema,
  getCashRegistersHistorySchema,
} from '../schemas/cash.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authenticate);

// Ruta para cerrar caja
router.post(
  '/:id/close',
  checkPermission(PERMISSIONS.CASH.MANAGE),
  validateRequest(closeCashRegisterSchema),
  cashController.closeCashRegister
);

// Rutas para el registro de caja
router.get(
  '/current',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.getCurrentRegister
);

router.post(
  '/open',
  checkPermission(PERMISSIONS.CASH.MANAGE),
  validateRequest(openCashRegisterSchema),
  cashController.openCashRegister
);

router.get(
  '/:registerId/balance',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.getRegisterBalance
);

router.post(
  '/:registerId/validate-balance',
  checkPermission(PERMISSIONS.CASH.MANAGE),
  validateRequest(closeCashRegisterSchema),
  cashController.validateClosingBalance
);

router.get(
  '/:registerId/summary',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.getRegisterSummary
);

// Rutas para movimientos de caja
router.post(
  '/:registerId/movements',
  checkPermission(PERMISSIONS.CASH.MANAGE),
  validateRequest(createCashMovementSchema),
  cashController.createCashMovement
);

router.patch(
  // Cambiar de PUT a PATCH
  '/movements/:id',
  checkPermission(PERMISSIONS.CASH.MANAGE),
  validateRequest(updateCashMovementSchema),
  cashController.updateCashMovement
);

router.delete(
  '/movements/:id',
  authenticate,
  checkPermission(PERMISSIONS.CASH.MANAGE),
  cashController.deleteCashMovement
);

router.get(
  '/:registerId/movements',
  checkPermission(PERMISSIONS.CASH.LIST),
  validateRequest(getCashMovementsSchema),
  cashController.getCashMovements
);

router.get(
  '/history',
  checkPermission(PERMISSIONS.CASH.LIST),
  validateRequest(getCashRegistersHistorySchema),
  cashController.getCashRegistersHistory
);

router.get(
  '/history/:registerId',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.getCashRegisterDetail
);

router.get(
  '/:registerId/export',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.exportCashMovements
);

router.delete(
  '/:id',
  authenticate,
  checkPermission(PERMISSIONS.CASH.MANAGE),
  cashController.deleteCashRegister
);

// Ruta para obtener detalles de un registro específico
router.get(
  '/:id',
  checkPermission(PERMISSIONS.CASH.LIST),
  cashController.getRegisterDetails
);

export default router;

import express from 'express';
import { productController } from '../controllers/product.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import { uploadProductImage } from '../middleware/upload.middleware.js';
import {
  createProductSchema,
  updateProductSchema,
  deleteProductSchema,
} from '../schemas/product.schema.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = express.Router();

router.use(authMiddleware);

router.get(
  '/',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.LIST),
  productController.getProducts.bind(productController)
);

router.get(
  '/:id',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.LIST),
  productController.getProductById.bind(productController)
);

router.post(
  '/',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.CREATE),
  uploadProductImage.single('image'),
  validateRequest(createProductSchema),
  productController.create.bind(productController)
);

router.put(
  '/:id',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.EDIT),
  uploadProductImage.single('image'),
  validateRequest(updateProductSchema),
  productController.update.bind(productController)
);

router.delete(
  '/:id',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.DELETE),
  validateRequest(deleteProductSchema),
  productController.delete.bind(productController)
);

router.get(
  '/dashboard/inventory',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.LIST),
  productController.getInventoryDashboard.bind(productController)
);

router.get(
  '/sale/available',
  checkPermission(PERMISSIONS.INVENTORY.PRODUCTS.LIST),
  productController.getProductsForSale.bind(productController)
);

export default router;

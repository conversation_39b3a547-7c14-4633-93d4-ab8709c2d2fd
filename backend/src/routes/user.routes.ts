import express from 'express';
import { userController } from '../controllers/user.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  createUserSchema,
  updateUserSchema,
  changePasswordSchema,
  updatePermissionsSchema,
  getUserByIdSchema,
  deleteUserSchema,
  toggleUserStatusSchema,
} from '../schemas/user.schema.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = express.Router();

router.use(authMiddleware);

// GET /api/users
router.get(
  '/',
  checkPermission(PERMISSIONS.USERS.LIST),
  userController.getUsers.bind(userController)
);

// GET /api/users/profile
router.get('/profile', userController.getCurrentUser.bind(userController));

// POST /api/users
router.post(
  '/',
  checkPermission(PERMISSIONS.USERS.CREATE),
  validateRequest(createUserSchema),
  userController.create.bind(userController)
);

// GET /api/users/:id
router.get(
  '/:id',
  checkPermission(PERMISSIONS.USERS.LIST),
  validateRequest(getUserByIdSchema),
  userController.getById.bind(userController)
);

// PATCH /api/users/:id
router.patch(
  '/:id',
  checkPermission(PERMISSIONS.USERS.EDIT),
  validateRequest(updateUserSchema),
  userController.update.bind(userController)
);

// DELETE /api/users/:id
router.delete(
  '/:id',
  checkPermission(PERMISSIONS.USERS.DELETE),
  validateRequest(deleteUserSchema),
  userController.delete.bind(userController)
);

// PATCH /api/users/:id/change-password
router.patch(
  '/:id/change-password',
  checkPermission(PERMISSIONS.USERS.CHANGE_PASSWORD),
  validateRequest(changePasswordSchema),
  userController.changePassword.bind(userController)
);

// PATCH /api/users/:id/permissions
router.patch(
  '/:id/permissions',
  checkPermission(PERMISSIONS.USERS.EDIT),
  validateRequest(updatePermissionsSchema),
  userController.updatePermissions.bind(userController)
);

// PATCH /api/users/:id/status
router.patch(
  '/:id/status',
  checkPermission(PERMISSIONS.USERS.EDIT),
  validateRequest(toggleUserStatusSchema),
  userController.toggleUserStatus.bind(userController)
);

export default router;

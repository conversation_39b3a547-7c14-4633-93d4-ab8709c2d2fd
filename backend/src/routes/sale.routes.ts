import { Router } from 'express';
import { saleController } from '../controllers/sale.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import { checkOpenCashRegister } from '../middleware/cashRegister.middleware.js';
import {
  createSaleSchema,
  updateSaleSchema,
  cancelSaleSchema,
  getSaleSchema,
  getSalesSchema,
  deleteSaleSchema,
} from '../schemas/sale.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authMiddleware);

// GET /api/sales
router.get(
  '/',
  checkPermission(PERMISSIONS.SALES.LIST),
  validateRequest(getSalesSchema),
  saleController.getSales.bind(saleController)
);

// GET /api/sales/:id
router.get(
  '/:id',
  checkPermission(PERMISSIONS.SALES.LIST),
  validateRequest(getSaleSchema),
  saleController.getSaleById.bind(saleController)
);

// POST /api/sales
router.post(
  '/',
  checkPermission(PERMISSIONS.SALES.CREATE),
  checkOpenCashRegister,
  validateRequest(createSaleSchema),
  saleController.createSale.bind(saleController)
);

// PATCH /api/sales/:id
router.patch(
  '/:id',
  checkPermission(PERMISSIONS.SALES.EDIT),
  validateRequest(updateSaleSchema),
  saleController.updateSale.bind(saleController)
);

// POST /api/sales/:id/cancel
router.post(
  '/:id/cancel',
  checkPermission(PERMISSIONS.SALES.CANCEL),
  validateRequest(cancelSaleSchema),
  saleController.cancelSale.bind(saleController)
);

// DELETE /api/sales/:id
router.delete(
  '/:id',
  checkPermission(PERMISSIONS.SALES.DELETE),
  validateRequest(deleteSaleSchema),
  saleController.deleteSale.bind(saleController)
);

export default router;

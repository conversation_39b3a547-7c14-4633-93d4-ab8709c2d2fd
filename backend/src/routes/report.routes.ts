import { Router } from 'express';
import { reportController } from '../controllers/report.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  getSalesReportSchema,
  getInventoryReportSchema,
} from '../schemas/report.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authMiddleware);

// GET /api/reports/sales
router.get(
  '/sales',
  checkPermission(PERMISSIONS.REPORTS.SALES.LIST),
  validateRequest(getSalesReportSchema),
  reportController.getSalesReport.bind(reportController)
);

router.get(
  '/inventory',
  checkPermission(PERMISSIONS.REPORTS.SALES.LIST),
  validateRequest(getInventoryReportSchema),
  reportController.getInventoryReport.bind(reportController)
);

export default router;

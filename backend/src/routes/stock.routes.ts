import express from 'express';
import { stockMovementController } from '../controllers/stock.controller.js';
import {
  authenticate,
  checkPermission,
} from '../middleware/auth.middleware.js';
import { validateSchema } from '../middleware/validateSchema.js';
import {
  createStockEntrySchema,
  createStockOutputSchema,
  getStockMovementsSchema,
  getProductStockMovementsSchema,
  updateStockMovementSchema,
} from '../schemas/stock.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = express.Router();

router.use(authenticate);

const { MOVEMENTS, ENTRIES, OUTPUTS } = PERMISSIONS.INVENTORY.STOCK;

router
  .route('/movements')
  .get(
    checkPermission(MOVEMENTS.LIST),
    validateSchema(getStockMovementsSchema),
    stockMovementController.getStockMovements.bind(stockMovementController)
  );

router
  .route('/movements/:id')
  .patch(
    checkPermission(MOVEMENTS.EDIT),
    validateSchema(updateStockMovementSchema),
    stockMovementController.updateStockMovement.bind(stockMovementController)
  )
  .delete(
    checkPermission(MOVEMENTS.DELETE),
    stockMovementController.deleteStockMovement.bind(stockMovementController)
  );

router
  .route('/products/:id/movements')
  .get(
    checkPermission(MOVEMENTS.LIST),
    validateSchema(getProductStockMovementsSchema),
    stockMovementController.getProductStockMovements.bind(
      stockMovementController
    )
  );

router
  .route('/products/:id/entries')
  .post(
    checkPermission(ENTRIES.CREATE),
    validateSchema(createStockEntrySchema),
    stockMovementController.createStockEntry.bind(stockMovementController)
  );

router
  .route('/products/:id/outputs')
  .post(
    checkPermission(OUTPUTS.CREATE),
    validateSchema(createStockOutputSchema),
    stockMovementController.createStockOutput.bind(stockMovementController)
  );

router.get(
  '/low',
  checkPermission(MOVEMENTS.LIST),
  stockMovementController.getLowStockProducts.bind(stockMovementController)
);

router.get(
  '/expiring',
  checkPermission(MOVEMENTS.LIST),
  stockMovementController.getExpiringProducts.bind(stockMovementController)
);

export default router;

import { Router } from 'express';
import { laboratoryController } from '../controllers/laboratory.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  createLaboratorySchema,
  updateLaboratorySchema,
  deleteLaboratorySchema,
  toggleStatusSchema,
} from '../schemas/laboratory.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authMiddleware);

// GET /api/laboratories
router.get(
  '/',
  checkPermission(PERMISSIONS.LABORATORIES.LIST),
  laboratoryController.getLaboratories.bind(laboratoryController)
);

router.get(
  '/all',
  checkPermission(PERMISSIONS.LABORATORIES.LIST),
  laboratoryController.getAllLaboratories.bind(laboratoryController)
);

// GET /api/laboratories/active
router.get(
  '/active',
  checkPermission(PERMISSIONS.LABORATORIES.LIST),
  laboratoryController.getActiveLaboratories.bind(laboratoryController)
);

// POST /api/laboratories
router.post(
  '/',
  checkPermission(PERMISSIONS.LABORATORIES.CREATE),
  validateRequest(createLaboratorySchema),
  laboratoryController.createLaboratory.bind(laboratoryController)
);

// PATCH /api/laboratories/:id
router.patch(
  '/:id',
  checkPermission(PERMISSIONS.LABORATORIES.EDIT),
  validateRequest(updateLaboratorySchema),
  laboratoryController.updateLaboratory.bind(laboratoryController)
);

// DELETE /api/laboratories/:id
router.delete(
  '/:id',
  checkPermission(PERMISSIONS.LABORATORIES.DELETE),
  validateRequest(deleteLaboratorySchema),
  laboratoryController.deleteLaboratory.bind(laboratoryController)
);

// PATCH /api/laboratories/:id/toggle-status
router.patch(
  '/:id/toggle-status',
  checkPermission(PERMISSIONS.LABORATORIES.EDIT),
  validateRequest(toggleStatusSchema),
  laboratoryController.toggleLaboratoryStatus.bind(laboratoryController)
);

export default router;

import { Router } from 'express';
import { customerController } from '../controllers/customer.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import {
  createCustomerSchema,
  updateCustomerSchema,
  getCustomerByIdSchema,
} from '../schemas/customer.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';

const router = Router();

router.use(authMiddleware);

// Ruta de exportación (debe ir antes de las rutas con parámetros)
router.get(
  '/export',
  checkPermission(PERMISSIONS.CUSTOMERS.LIST),
  customerController.exportCustomers.bind(customerController)
);

// Agregar esta ruta antes de las rutas con parámetros
router.get(
  '/all',
  checkPermission(PERMISSIONS.CUSTOMERS.LIST),
  customerController.getAllCustomers.bind(customerController)
);

// GET /api/customers
router.get(
  '/',
  checkPermission(PERMISSIONS.CUSTOMERS.LIST),
  customerController.getCustomers.bind(customerController)
);

// GET /api/customers/:id
router.get(
  '/:id',
  checkPermission(PERMISSIONS.CUSTOMERS.LIST),
  validateRequest(getCustomerByIdSchema),
  customerController.getCustomerById.bind(customerController)
);

// POST /api/customers
router.post(
  '/',
  checkPermission(PERMISSIONS.CUSTOMERS.CREATE),
  validateRequest(createCustomerSchema),
  customerController.createCustomer.bind(customerController)
);

// PATCH /api/customers/:id
router.patch(
  '/:id',
  checkPermission(PERMISSIONS.CUSTOMERS.EDIT),
  validateRequest(updateCustomerSchema),
  customerController.updateCustomer.bind(customerController)
);

// DELETE /api/customers/:id
router.delete(
  '/:id',
  checkPermission(PERMISSIONS.CUSTOMERS.DELETE),
  validateRequest(getCustomerByIdSchema),
  customerController.deleteCustomer.bind(customerController)
);

export default router;

import express from 'express';
import { settingsController } from '../controllers/settings.controller.js';
import { authenticate as authMiddleware } from '../middleware/auth.middleware.js';
import { validateSchema as validateRequest } from '../middleware/validateSchema.js';
import { checkPermission } from '../middleware/auth.middleware.js';
import {
  generalSettingsSchema,
  getPublicSettingsSchema,
  getGeneralSettingsSchema,
} from '../schemas/settings.schema.js';
import { PERMISSIONS } from '../constants/permissions.constants.js';
import { uploadLogo } from '../middleware/upload.middleware.js';

const router = express.Router();

// Public routes
router.get(
  '/public',
  validateRequest(getPublicSettingsSchema),
  settingsController.getPublicSettings.bind(settingsController)
);

// Private routes
router.use(authMiddleware);

router.get(
  '/general',
  checkPermission(PERMISSIONS.SETTINGS.LIST),
  validateRequest(getGeneralSettingsSchema),
  settingsController.getGeneralSettings.bind(settingsController)
);

router.put(
  '/general',
  checkPermission(PERMISSIONS.SETTINGS.EDIT),
  uploadLogo.single('logo'),
  validateRequest(generalSettingsSchema),
  settingsController.updateGeneralSettings.bind(settingsController)
);

export default router;

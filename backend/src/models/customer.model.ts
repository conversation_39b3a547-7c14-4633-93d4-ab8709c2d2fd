import { Schema, model, type Document, type Types } from 'mongoose';
import { DOCUMENT_TYPES } from '../constants/customer.constants.js';

export interface ICustomerDocument extends Document {
  documentType: string;
  documentNumber: string;
  complement?: string;
  businessName: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  isGeneric?: boolean;
  isProtected?: boolean;
  isActive: boolean;
  createdBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const customerSchema = new Schema<ICustomerDocument>(
  {
    documentType: {
      type: String,
      required: true,
      enum: Object.values(DOCUMENT_TYPES),
    },
    documentNumber: {
      type: String,
      required: true,
    },
    complement: {
      type: String,
      required: false,
    },
    businessName: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: false,
    },
    phone: {
      type: String,
      required: false,
    },
    address: {
      type: String,
      required: false,
    },
    notes: {
      type: String,
      required: false,
    },
    isGeneric: {
      type: Boolean,
      default: false,
    },
    isProtected: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Índices
customerSchema.index({ documentType: 1, documentNumber: 1 }, { unique: true });
customerSchema.index({ businessName: 1 });
customerSchema.index({ isGeneric: 1 });
customerSchema.index({ isProtected: 1 });

export const Customer = model<ICustomerDocument>('Customer', customerSchema);

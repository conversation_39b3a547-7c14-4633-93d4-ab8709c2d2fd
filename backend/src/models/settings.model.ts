import mongoose from 'mongoose';
import { ISettings } from '../interfaces/settings.interface.js';
import { SETTINGS_VALIDATION } from '../constants/settings.constants.js';

const generalSettingsSchema = new mongoose.Schema(
  {
    systemName: {
      type: String,
      required: true,
      trim: true,
    },
    businessName: {
      type: String,
      required: true,
      trim: true,
    },
    address: {
      type: String,
      required: true,
      trim: true,
    },
    phone: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    taxId: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    logo: {
      type: String,
      trim: true,
    },
  },
  { _id: false }
);

const settingsSchema = new mongoose.Schema(
  {
    general: {
      type: generalSettingsSchema,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

export const Settings = mongoose.model<ISettings>('Settings', settingsSchema);

import { Schema, model } from 'mongoose';
import { ILaboratoryDocument } from '../interfaces/laboratory.interface.js';

const laboratorySchema = new Schema<ILaboratoryDocument>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    country: {
      type: String,
      trim: true,
    },
    website: {
      type: String,
      trim: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isProtected: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

export const Laboratory = model<ILaboratoryDocument>(
  'Laboratory',
  laboratorySchema
);

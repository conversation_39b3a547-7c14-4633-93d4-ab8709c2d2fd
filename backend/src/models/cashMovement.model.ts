import mongoose, { Schema } from 'mongoose';
import { ICashMovementDocument } from '../interfaces/cash.interface.js';

const cashMovementSchema = new Schema(
  {
    cashRegister: {
      type: Schema.Types.ObjectId,
      ref: 'CashRegister',
      required: true,
    },
    type: {
      type: String,
      enum: ['INCOME', 'EXPENSE'],
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    concept: {
      type: String,
      required: true,
    },
    reference: {
      type: String,
      required: false,
    },
    observations: {
      type: String,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Índices
cashMovementSchema.index({ cashRegister: 1 });
cashMovementSchema.index({ type: 1 });
cashMovementSchema.index({ createdAt: 1 });
cashMovementSchema.index({ createdBy: 1 });

export const CashMovement = mongoose.model<ICashMovementDocument>(
  'CashMovement',
  cashMovementSchema
);

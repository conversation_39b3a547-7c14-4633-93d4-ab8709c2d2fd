import mongoose, { Schema } from 'mongoose';
import { PAYMENT_METHODS, SALE_STATUS } from '../constants/sale.constants.js';

const saleItemSchema = new Schema({
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
  },
  unitPrice: {
    type: Number,
    required: true,
    min: 0,
  },
  discount: {
    type: Number,
    default: 0,
    min: 0,
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0,
  },
});

const saleSchema = new Schema(
  {
    number: {
      type: String,
      required: true,
      unique: true,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    customer: {
      type: Schema.Types.ObjectId,
      ref: 'Customer',
      required: true,
    },
    items: [saleItemSchema],
    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    discount: {
      type: Number,
      default: 0,
      min: 0,
    },
    total: {
      type: Number,
      required: true,
      min: 0,
    },
    paymentMethod: {
      type: String,
      enum: Object.values(PAYMENT_METHODS),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(SALE_STATUS),
      default: SALE_STATUS.COMPLETED,
    },
    cashRegister: {
      type: Schema.Types.ObjectId,
      ref: 'CashRegister',
      required: true,
    },
    invoice: {
      type: Schema.Types.ObjectId,
      ref: 'Invoice',
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    canceledBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      sparse: true,
    },
    canceledAt: {
      type: Date,
      sparse: true,
    },
    cancelReason: {
      type: String,
      trim: true,
      maxlength: 500,
      sparse: true,
    },
    cancelNotes: {
      type: String,
      trim: true,
      sparse: true,
    },
  },
  {
    timestamps: true,
  }
);

// Índices
saleSchema.index({ number: 1 });
saleSchema.index({ date: 1 });
saleSchema.index({ customer: 1 });
saleSchema.index({ status: 1 });
saleSchema.index({ cashRegister: 1 });
saleSchema.index({ createdBy: 1 });

export const Sale = mongoose.model('Sale', saleSchema);

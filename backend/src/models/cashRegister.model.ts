import { Schema, model } from 'mongoose';
import { CASH_REGISTER_STATUS } from '../constants/cash.constants.js';

const cashRegisterSchema = new Schema(
  {
    openingDate: { type: Date, required: true },
    closingDate: { type: Date },
    initialBalance: { type: Number, required: true },
    finalBalance: { type: Number },
    status: {
      type: String,
      enum: Object.values(CASH_REGISTER_STATUS),
      default: CASH_REGISTER_STATUS.OPEN,
    },
    observations: { type: String },
    openedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    closedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    forcedClose: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

export const CashRegister = model('CashRegister', cashRegisterSchema);

import mongoose from 'mongoose';
import { IProductDocument } from '../interfaces/product.interface.js';

const productSchema = new mongoose.Schema(
  {
    sku: {
      type: String,
      unique: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    image: {
      type: String,
    },
    laboratory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Laboratory',
      required: true,
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: true,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    cost: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    stock: {
      type: Number,
      required: true,
      default: 0,
      min: 0,
    },
    minStock: {
      type: Number,
      required: true,
      default: 5,
      min: 0,
    },
    maxStock: {
      type: Number,
      required: true,
      min: 0,
    },
    // Añadiendo los campos técnicos
    presentation: {
      type: String,
      required: true,
      trim: true,
    },
    concentration: {
      type: String,
      required: true,
      trim: true,
    },
    measurementUnit: {
      type: String,
      required: true,
      enum: ['TABLETA', 'CAPSULA', 'AMPOLLA', 'FRASCO', 'CREMA', 'GOTAS'],
    },
    administrationRoute: {
      type: String,
      required: true,
      enum: ['ORAL', 'INYECTABLE', 'TOPICA', 'OFTALMICA', 'OTRO'],
    },
    stockAlert: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    expirationAlert: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Índices
productSchema.index({ sku: 1 });
productSchema.index({ name: 'text', description: 'text' });
productSchema.index({ laboratory: 1 });
productSchema.index({ category: 1 });
productSchema.index({ isActive: 1 });
productSchema.index({ stock: 1 });
productSchema.index({ expirationDate: 1 });

export const Product = mongoose.model<IProductDocument>(
  'Product',
  productSchema
);

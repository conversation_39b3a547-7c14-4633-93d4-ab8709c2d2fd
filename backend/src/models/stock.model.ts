import mongoose, { Schema, Document } from 'mongoose';
import { STOCK_MOVEMENT_TYPES } from '../constants/stock.constants.js';

export interface IStockMovement extends Document {
  product: mongoose.Types.ObjectId;
  type: string;
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  batchNumber: string;
  expirationDate?: Date;
  cost?: number;
  reference?: string;
  observations?: string;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const stockMovementSchema = new Schema<IStockMovement>(
  {
    product: {
      type: Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: Object.values(STOCK_MOVEMENT_TYPES),
    },
    quantity: {
      type: Number,
      required: true,
    },
    previousStock: {
      type: Number,
      required: true,
    },
    newStock: {
      type: Number,
      required: true,
    },
    reason: {
      type: String,
      required: true,
    },
    batchNumber: {
      type: String,
      required: true,
    },
    expirationDate: {
      type: Date,
      required: function (this: IStockMovement) {
        return this.type === STOCK_MOVEMENT_TYPES.ENTRY;
      },
    },
    cost: {
      type: Number,
      required: function (this: IStockMovement) {
        return this.type === STOCK_MOVEMENT_TYPES.ENTRY;
      },
    },
    reference: {
      type: String,
      required: false,
    },
    observations: {
      type: String,
      required: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

export const StockMovement = mongoose.model<IStockMovement>(
  'StockMovement',
  stockMovementSchema
);

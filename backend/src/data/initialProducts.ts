import { Types } from 'mongoose';

const SYSTEM_USER_ID = process.env.SYSTEM_USER_ID;

export const initialProducts = [
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73101'),
    sku: 'PARA001',
    name: 'Paracetamol 500mg',
    description: 'Analgésico y antipirético para alivio del dolor y fiebre',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74001'), // Bayer
    category: new Types.ObjectId('65c3944f00c9d8232dc73001'), // Analgésicos
    price: 15.5,
    cost: 8.25,
    stock: 100,
    minStock: 20,
    maxStock: 150,
    location: 'A-1',
    expirationDate: '2025-12-31T00:00:00.000Z',
    batchNumber: 'LOT2023001',
    requiresPrescription: false,
    image: 'https://example.com/images/paracetamol.jpg',
    barcode: '7501234567890',
    presentation: 'Caja con 20 tabletas',
    concentration: '500mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456789',
    dosage: '1-2 tabletas cada 8 horas',
    sideEffects: 'Puede causar malestar estomacal',
    contraindications: 'Hipersensibilidad al paracetamol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73102'),
    sku: 'IBUP002',
    name: 'Ibuprofeno 400mg',
    description: 'Antiinflamatorio no esteroideo',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74002'), // Pfizer
    category: new Types.ObjectId('65c3944f00c9d8232dc73003'), // Antiinflamatorios
    price: 18.9,
    cost: 10.5,
    stock: 85,
    minStock: 15,
    maxStock: 120,
    location: 'A-2',
    expirationDate: '2025-06-30T00:00:00.000Z',
    batchNumber: 'LOT2023002',
    requiresPrescription: false,
    image: 'https://example.com/images/ibuprofeno.jpg',
    barcode: '7501234567891',
    presentation: 'Caja con 30 tabletas',
    concentration: '400mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456790',
    dosage: '1 tableta cada 8 horas',
    sideEffects: 'Puede causar irritación gástrica',
    contraindications: 'No usar en caso de úlcera gástrica',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73103'),
    sku: 'AMOX003',
    name: 'Amoxicilina 500mg',
    description: 'Antibiótico de amplio espectro',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74003'), // GSK
    category: new Types.ObjectId('65c3944f00c9d8232dc73002'), // Antibióticos
    price: 45.0,
    cost: 25.0,
    stock: 50,
    minStock: 10,
    maxStock: 80,
    location: 'B-1',
    expirationDate: '2025-03-15T00:00:00.000Z',
    batchNumber: 'LOT2023003',
    requiresPrescription: true,
    image: 'https://example.com/images/amoxicilina.jpg',
    barcode: '7501234567892',
    presentation: 'Caja con 15 cápsulas',
    concentration: '500mg',
    measurementUnit: 'CAPSULA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456791',
    dosage: '1 cápsula cada 8 horas por 7 días',
    sideEffects: 'Puede causar diarrea, náuseas',
    contraindications: 'Alergia a penicilinas',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73104'),
    sku: 'OMEP004',
    name: 'Omeprazol 20mg',
    description: 'Inhibidor de la bomba de protones para acidez estomacal',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74004'), // AstraZeneca
    category: new Types.ObjectId('65c3944f00c9d8232dc73006'), // Gastrointestinal
    price: 35.8,
    cost: 18.9,
    stock: 75,
    minStock: 15,
    maxStock: 100,
    location: 'B-2',
    expirationDate: '2025-09-20T00:00:00.000Z',
    batchNumber: 'LOT2023004',
    requiresPrescription: false,
    image: 'https://example.com/images/omeprazol.jpg',
    barcode: '7501234567893',
    presentation: 'Caja con 30 cápsulas',
    concentration: '20mg',
    measurementUnit: 'CAPSULA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456792',
    dosage: '1 cápsula cada 24 horas',
    sideEffects: 'Puede causar dolor de cabeza, náuseas',
    contraindications: 'Hipersensibilidad al omeprazol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73105'),
    sku: 'LORAT005',
    name: 'Loratadina 10mg',
    description: 'Antihistamínico para alergias',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74005'), // Merck
    category: new Types.ObjectId('65c3944f00c9d8232dc73004'), // Antialérgicos
    price: 22.5,
    cost: 12.3,
    stock: 60,
    minStock: 12,
    maxStock: 100,
    location: 'C-1',
    expirationDate: '2025-08-10T00:00:00.000Z',
    batchNumber: 'LOT2023005',
    requiresPrescription: false,
    image: 'https://example.com/images/loratadina.jpg',
    barcode: '7501234567894',
    presentation: 'Caja con 30 tabletas',
    concentration: '10mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456793',
    dosage: '1 tableta cada 24 horas',
    sideEffects: 'Puede causar somnolencia, boca seca',
    contraindications: 'Hipersensibilidad a la loratadina',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73106'),
    sku: 'DIAZ006',
    name: 'Diazepam 5mg',
    description: 'Ansiolítico y relajante muscular',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74006'), // Roche
    category: new Types.ObjectId('65c3944f00c9d8232dc73012'), // Ansiolíticos
    price: 55.0,
    cost: 30.0,
    stock: 40,
    minStock: 8,
    maxStock: 60,
    location: 'D-1',
    expirationDate: '2024-12-31T00:00:00.000Z',
    batchNumber: 'LOT2023006',
    requiresPrescription: true,
    image: 'https://example.com/images/diazepam.jpg',
    barcode: '7501234567895',
    presentation: 'Caja con 30 tabletas',
    concentration: '5mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456794',
    dosage: '1 tableta cada 8-12 horas',
    sideEffects: 'Puede causar somnolencia, mareos',
    contraindications: 'No usar con alcohol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73107'),
    sku: 'METF007',
    name: 'Metformina 850mg',
    description: 'Antidiabético oral',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74007'), // Sanofi
    category: new Types.ObjectId('65c3944f00c9d8232dc73013'), // Antidiabéticos
    price: 28.9,
    cost: 15.4,
    stock: 90,
    minStock: 20,
    maxStock: 150,
    location: 'D-2',
    expirationDate: '2025-11-30T00:00:00.000Z',
    batchNumber: 'LOT2023007',
    requiresPrescription: true,
    image: 'https://example.com/images/metformina.jpg',
    barcode: '7501234567896',
    presentation: 'Caja con 30 tabletas',
    concentration: '850mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456795',
    dosage: '1 tableta cada 12 horas',
    sideEffects: 'Puede causar náuseas, diarrea',
    contraindications: 'Insuficiencia renal grave',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73108'),
    sku: 'SALB008',
    name: 'Salbutamol Inhalador',
    description: 'Broncodilatador para asma',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74003'), // GSK
    category: new Types.ObjectId('65c3944f00c9d8232dc73008'), // Respiratorio
    price: 65.0,
    cost: 35.0,
    stock: 30,
    minStock: 10,
    maxStock: 50,
    location: 'E-1',
    expirationDate: '2025-10-15T00:00:00.000Z',
    batchNumber: 'LOT2023008',
    requiresPrescription: true,
    image: 'https://example.com/images/salbutamol.jpg',
    barcode: '7501234567897',
    presentation: 'Inhalador 200 dosis',
    concentration: '100mcg/dosis',
    measurementUnit: 'FRASCO',
    administrationRoute: 'OTRO',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456796',
    dosage: '1-2 inhalaciones cada 4-6 horas según necesidad',
    sideEffects: 'Puede causar temblores, taquicardia',
    contraindications: 'Hipersensibilidad al salbutamol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    sku: 'CETI009',
    name: 'Cetirizina Gotas',
    description: 'Antihistamínico en solución oral',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74008'), // UCB
    category: new Types.ObjectId('65c3944f00c9d8232dc73004'), // Antialérgicos
    price: 25.5,
    cost: 13.8,
    stock: 70,
    minStock: 15,
    maxStock: 100,
    location: 'C-2',
    expirationDate: '2025-07-20T00:00:00.000Z',
    batchNumber: 'LOT2023009',
    requiresPrescription: false,
    image: 'https://example.com/images/cetirizina.jpg',
    barcode: '7501234567898',
    presentation: 'Frasco 15ml',
    concentration: '10mg/ml',
    measurementUnit: 'GOTAS',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456797',
    dosage: '10 gotas cada 24 horas',
    sideEffects: 'Puede causar somnolencia',
    contraindications: 'Hipersensibilidad a la cetirizina',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    status: 'active',
    stockAlert: true,
    expirationAlert: true,
  },
  {
    sku: 'RAMI010',
    name: 'Ramipril 5mg',
    description: 'Antihipertensivo IECA',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74009'), // Novartis
    category: new Types.ObjectId('65c3944f00c9d8232dc73007'), // Cardiovascular
    price: 48.7,
    cost: 26.9,
    stock: 55,
    minStock: 12,
    maxStock: 80,
    location: 'F-1',
    expirationDate: '2025-05-25T00:00:00.000Z',
    batchNumber: 'LOT2023010',
    requiresPrescription: true,
    image: 'https://example.com/images/ramipril.jpg',
    barcode: '7501234567899',
    presentation: 'Caja con 30 tabletas',
    concentration: '5mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456798',
    dosage: '1 tableta cada 24 horas',
    sideEffects: 'Puede causar tos seca',
    contraindications: 'Embarazo, angioedema previo',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    status: 'active',
    stockAlert: true,
    expirationAlert: true,
  },
  {
    sku: 'DEXA011',
    name: 'Dexametasona Ampolla',
    description: 'Corticosteroide inyectable',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74002'), // Pfizer
    category: new Types.ObjectId('65c3944f00c9d8232dc73003'), // Antiinflamatorios
    price: 42.0,
    cost: 23.5,
    stock: 45,
    minStock: 10,
    maxStock: 70,
    location: 'H-1',
    expirationDate: '2025-06-15T00:00:00.000Z',
    batchNumber: 'LOT2023011',
    requiresPrescription: true,
    image: 'https://example.com/images/dexametasona.jpg',
    barcode: '7501234567900',
    presentation: 'Caja con 5 ampollas',
    concentration: '8mg/2ml',
    measurementUnit: 'AMPOLLA',
    administrationRoute: 'INYECTABLE',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456799',
    dosage: 'Según prescripción médica',
    sideEffects: 'Puede alterar niveles de glucosa',
    contraindications: 'Infecciones sistémicas sin tratamiento',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    status: 'active',
    stockAlert: true,
    expirationAlert: true,
  },
  {
    sku: 'CLOT012',
    name: 'Clotrimazol Crema',
    description: 'Antimicótico tópico',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74001'), // Bayer
    category: new Types.ObjectId('65c3944f00c9d8232dc73009'), // Dermatológicos
    price: 32.5,
    cost: 17.8,
    stock: 65,
    minStock: 15,
    maxStock: 90,
    location: 'I-2',
    expirationDate: '2025-08-30T00:00:00.000Z',
    batchNumber: 'LOT2023012',
    requiresPrescription: false,
    image: 'https://example.com/images/clotrimazol.jpg',
    barcode: '7501234567901',
    presentation: 'Tubo 30g',
    concentration: '1%',
    measurementUnit: 'CREMA',
    administrationRoute: 'TOPICA',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456800',
    dosage: 'Aplicar 2-3 veces al día',
    sideEffects: 'Puede causar irritación local',
    contraindications: 'Hipersensibilidad al clotrimazol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    status: 'active',
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73113'),
    sku: 'LEVO013',
    name: 'Levofloxacino 500mg',
    description: 'Antibiótico de amplio espectro',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74002'), // Pfizer
    category: new Types.ObjectId('65c3944f00c9d8232dc73002'), // Antibióticos
    price: 75.0,
    cost: 42.0,
    stock: 40,
    minStock: 10,
    maxStock: 60,
    location: 'B-3',
    expirationDate: '2025-04-30T00:00:00.000Z',
    batchNumber: 'LOT2023013',
    requiresPrescription: true,
    image: 'https://example.com/images/levofloxacino.jpg',
    barcode: '7501234567902',
    presentation: 'Caja con 10 tabletas',
    concentration: '500mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456801',
    dosage: '1 tableta cada 24 horas',
    sideEffects: 'Puede causar tendinitis, fotosensibilidad',
    contraindications: 'Embarazo, lactancia',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73114'),
    sku: 'INSU014',
    name: 'Insulina Glargina',
    description: 'Insulina de acción prolongada',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74007'), // Sanofi
    category: new Types.ObjectId('65c3944f00c9d8232dc73013'), // Antidiabéticos
    price: 850.0,
    cost: 520.0,
    stock: 25,
    minStock: 8,
    maxStock: 40,
    location: 'R-1',
    expirationDate: '2025-03-15T00:00:00.000Z',
    batchNumber: 'LOT2023014',
    requiresPrescription: true,
    image: 'https://example.com/images/insulina.jpg',
    barcode: '7501234567903',
    presentation: 'Pluma precargada 3ml',
    concentration: '100UI/ml',
    measurementUnit: 'FRASCO',
    administrationRoute: 'INYECTABLE',
    storageCondition: 'REFRIGERACION',
    sanitaryRegistration: 'RS-123456802',
    dosage: 'Según prescripción médica',
    sideEffects: 'Hipoglucemia, reacciones en el sitio de inyección',
    contraindications: 'Hipoglucemia aguda',
    storage: 'Mantener refrigerado entre 2-8°C',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73115'),
    sku: 'KETO015',
    name: 'Ketoconazol Shampoo',
    description: 'Antimicótico para uso capilar',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74003'), // GSK
    category: new Types.ObjectId('65c3944f00c9d8232dc73009'), // Dermatológicos
    price: 45.8,
    cost: 25.3,
    stock: 60,
    minStock: 15,
    maxStock: 85,
    location: 'I-3',
    expirationDate: '2025-09-30T00:00:00.000Z',
    batchNumber: 'LOT2023015',
    requiresPrescription: false,
    image: 'https://example.com/images/ketoconazol.jpg',
    barcode: '7501234567904',
    presentation: 'Frasco 120ml',
    concentration: '2%',
    measurementUnit: 'FRASCO',
    administrationRoute: 'TOPICA',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456803',
    dosage: 'Aplicar 2-3 veces por semana',
    sideEffects: 'Puede causar resequedad del cuero cabelludo',
    contraindications: 'Hipersensibilidad al ketoconazol',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true,
    expirationAlert: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73116'),
    sku: 'METF016',
    name: 'Metformina 850mg',
    description: 'Antidiabético oral',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74007'), // Sanofi
    category: new Types.ObjectId('65c3944f00c9d8232dc73013'), // Antidiabéticos
    price: 35.0,
    cost: 18.0,
    stock: 5, // ¡Stock bajo!
    minStock: 20,
    maxStock: 100,
    location: 'D-4',
    expirationDate: '2025-12-31T00:00:00.000Z', // No próximo a vencer
    batchNumber: 'LOT2023016',
    requiresPrescription: true,
    image: 'https://example.com/images/metformina.jpg',
    barcode: '7501234567905',
    presentation: 'Caja con 30 tabletas',
    concentration: '850mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456804',
    dosage: '1 tableta cada 12 horas',
    sideEffects: 'Puede causar malestar estomacal',
    contraindications: 'Insuficiencia renal severa',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: true, // Tiene stock bajo
    expirationAlert: false,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73117'),
    sku: 'AMLOD017',
    name: 'Amlodipino 5mg',
    description: 'Antihipertensivo',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74009'), // Novartis
    category: new Types.ObjectId('65c3944f00c9d8232dc73007'), // Cardiovascular
    price: 42.0,
    cost: 23.0,
    stock: 45, // Stock normal
    minStock: 15,
    maxStock: 80,
    location: 'E-2',
    expirationDate: '2024-03-15T00:00:00.000Z', // ¡Próximo a vencer!
    batchNumber: 'LOT2023017',
    requiresPrescription: true,
    image: 'https://example.com/images/amlodipino.jpg',
    barcode: '7501234567906',
    presentation: 'Caja con 30 tabletas',
    concentration: '5mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456805',
    dosage: '1 tableta cada 24 horas',
    sideEffects: 'Puede causar edema',
    contraindications: 'Hipersensibilidad al amlodipino',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: false,
    expirationAlert: true, // Próximo a vencer
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73118'),
    sku: 'DICL018',
    name: 'Diclofenaco 50mg',
    description: 'Antiinflamatorio',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74001'), // Bayer
    category: new Types.ObjectId('65c3944f00c9d8232dc73003'), // Antiinflamatorios
    price: 28.0,
    cost: 15.0,
    stock: 0, // Sin stock
    minStock: 25,
    maxStock: 120,
    location: 'F-1',
    expirationDate: '2025-08-20T00:00:00.000Z',
    batchNumber: 'LOT2023018',
    requiresPrescription: false,
    image: 'https://example.com/images/diclofenaco.jpg',
    barcode: '7501234567907',
    presentation: 'Caja con 20 tabletas',
    concentration: '50mg',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456806',
    dosage: '1 tableta cada 8 horas',
    sideEffects: 'Puede causar irritación gástrica',
    contraindications: 'Úlcera gástrica activa',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: false, // Producto inactivo
    stockAlert: true,
    expirationAlert: false,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73119'),
    sku: 'VITA019',
    name: 'Complejo B',
    description: 'Suplemento vitamínico',
    laboratory: new Types.ObjectId('65c3944f00c9d8232dc74002'), // Pfizer
    category: new Types.ObjectId('65c3944f00c9d8232dc73010'), // Vitaminas
    price: 25.0,
    cost: 12.0,
    stock: 85, // Stock óptimo
    minStock: 20,
    maxStock: 100,
    location: 'G-3',
    expirationDate: '2026-01-15T00:00:00.000Z', // No próximo a vencer
    batchNumber: 'LOT2023019',
    requiresPrescription: false,
    image: 'https://example.com/images/complejo-b.jpg',
    barcode: '7501234567908',
    presentation: 'Frasco 30 tabletas',
    concentration: 'N/A',
    measurementUnit: 'TABLETA',
    administrationRoute: 'ORAL',
    storageCondition: 'TEMPERATURA_AMBIENTE',
    sanitaryRegistration: 'RS-123456807',
    dosage: '1 tableta diaria',
    sideEffects: 'Puede causar orina de color amarillo brillante',
    contraindications: 'Ninguna conocida',
    storage: 'Mantener en lugar fresco y seco',
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
    isActive: true,
    stockAlert: false, // Stock normal
    expirationAlert: false, // No próximo a vencer
  },
] as const;

// Tipo para los productos
export interface Product {
  sku: string;
  name: string;
  description: string;
  laboratory: Types.ObjectId;
  category: Types.ObjectId;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  location: string;
  expirationDate: string;
  batchNumber: string;
  requiresPrescription: boolean;
  image?: string;
  barcode: string;
  presentation: string;
  concentration: string;
  measurementUnit:
    | 'TABLETA'
    | 'CAPSULA'
    | 'AMPOLLA'
    | 'FRASCO'
    | 'CREMA'
    | 'GOTAS';
  administrationRoute: 'ORAL' | 'INYECTABLE' | 'TOPICA' | 'OFTALMICA' | 'OTRO';
  storageCondition: 'TEMPERATURA_AMBIENTE' | 'REFRIGERACION' | 'CONGELACION';
  sanitaryRegistration: string;
  dosage: string;
  sideEffects: string;
  contraindications: string;
  storage: string;
  createdBy: Types.ObjectId;
  status: 'active' | 'inactive';
  stockAlert?: boolean;
  expirationAlert?: boolean;
}

import { Types } from 'mongoose';

export const UNCATEGORIZED_CATEGORY_ID = process.env.UNCATEGORIZED_CATEGORY_ID;

export const initialCategories = [
  {
    _id: new Types.ObjectId(UNCATEGORIZED_CATEGORY_ID),
    name: 'Sin categoría',
    description: 'Productos sin categoría asignada',
    isProtected: true,
    isActive: true,
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    name: '<PERSON>lgésicos',
    description: 'Medicamentos para el alivio del dolor',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73002'),
    name: 'Antibióticos',
    description: 'Medicamentos para tratar infecciones bacterianas',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73003'),
    name: 'Antiinflamatorios',
    description: 'Medicamentos para reducir la inflamación',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73004'),
    name: 'Antialérgicos',
    description: 'Medicamentos para tratar alergias',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73005'),
    name: 'Vitaminas y Suplementos',
    description: 'Complementos nutricionales y vitamínicos',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73006'),
    name: 'Gastrointestinal',
    description: 'Medicamentos para el sistema digestivo',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73007'),
    name: 'Cardiovascular',
    description: 'Medicamentos para el sistema circulatorio',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73008'),
    name: 'Respiratorio',
    description: 'Medicamentos para el sistema respiratorio',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73009'),
    name: 'Dermatológicos',
    description: 'Medicamentos para la piel',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73010'),
    name: 'Material de Curación',
    description: 'Vendas, gasas y material de curación',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73011'),
    name: 'Hormonas',
    description: 'Medicamentos hormonales',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73012'),
    name: 'Ansiolíticos',
    description: 'Medicamentos para la ansiedad y el estrés',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73013'),
    name: 'Antidiabéticos',
    description: 'Medicamentos para la diabetes',
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc73014'),
    name: 'Antidepresivos',
    description: 'Medicamentos para la depresión',
  },
] as const;

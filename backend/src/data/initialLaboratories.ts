import { Types } from 'mongoose';

const SYSTEM_USER_ID = process.env.SYSTEM_USER_ID;
const NO_LABORATORY_ID = process.env.NO_LABORATORY_ID;

export const initialLaboratories = [
  {
    _id: new Types.ObjectId(NO_LABORATORY_ID),
    name: 'Sin laboratorio',
    description:
      'Laboratorio por defecto para productos sin laboratorio asignado',
    country: null,
    website: null,
    isActive: true,
    isProtected: true,
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74001'),
    name: 'Bayer',
    description: 'Bayer AG - Empresa farmacéutica multinacional',
    country: 'Alemania',
    website: 'https://www.bayer.com',
    isActive: true,
    isProtected: false,
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74002'),
    name: '<PERSON><PERSON>zer',
    description: 'Pfizer Inc. - Compañía farmacéutica global',
    country: 'Estados Unidos',
    website: 'https://www.pfizer.com',
    isActive: true,
    isProtected: false,
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74003'),
    name: 'Roche',
    description: 'F. Hoffmann-La Roche AG',
    country: 'Suiza',
    website: 'https://www.roche.com',
    isActive: true,
    isProtected: false,
    createdBy: new Types.ObjectId(SYSTEM_USER_ID),
  },
] as const;

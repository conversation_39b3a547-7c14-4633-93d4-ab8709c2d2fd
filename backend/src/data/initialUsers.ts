import { Types } from 'mongoose';
import { PERMISSIONS } from '../constants/permissions.constants.js';
import { USER_ROLES } from '../constants/user.constants.js';

export const initialUsers = [
  {
    _id: new Types.ObjectId('67cb199b74d13e50d33661d1'),
    username: 'admin',
    password: 'admin123',
    role: USER_ROLES.ADMIN,
    permissions: ['*'],
    isSuperAdmin: true,
  },
  {
    username: 'cashier',
    password: 'cashier123',
    role: USER_ROLES.CASHIER,
    permissions: [
      // Inventario - Productos
      PERMISSIONS.INVENTORY.PRODUCTS.LIST,
      // Inventario - Stock
      PERMISSIONS.INVENTORY.STOCK.ENTRIES.LIST,
      PERMISSIONS.INVENTORY.STOCK.MOVEMENTS.LIST,
      PERMISSIONS.INVENTORY.STOCK.ENTRIES.CREATE,
      PERMISSIONS.INVENTORY.STOCK.OUTPUTS.LIST,
      PERMISSIONS.INVENTORY.STOCK.OUTPUTS.CREATE,
      // Ventas
      PERMISSIONS.SALES.LIST,
      PERMISSIONS.SALES.CREATE,
      PERMISSIONS.SALES.CANCEL,
      // Categorías y Laboratorios (solo lectura)
      PERMISSIONS.CATEGORIES.LIST,
      PERMISSIONS.LABORATORIES.LIST,
      // Reportes
      PERMISSIONS.REPORTS.SALES.LIST,
      PERMISSIONS.REPORTS.INVENTORY.LIST,
      // Caja
      PERMISSIONS.CASH.LIST,
      PERMISSIONS.CASH.MANAGE,
      // Clientes (acceso limitado)
      PERMISSIONS.CUSTOMERS.LIST,
      PERMISSIONS.CUSTOMERS.CREATE,
      PERMISSIONS.CUSTOMERS.EDIT,
    ],
  },
] as const;

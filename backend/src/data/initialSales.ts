import { Types } from 'mongoose';
import { PAYMENT_METHODS, SALE_STATUS } from '../constants/sale.constants.js';

export const initialSales = [
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75002'),
    number: 'VTA-2024-0002',
    date: new Date('2024-02-01T11:15:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74002'),
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73102'),
        quantity: 1,
        unitPrice: 18.9,
        discount: 0,
        subtotal: 18.9,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73103'),
        quantity: 1,
        unitPrice: 45.0,
        discount: 0,
        subtotal: 45.0,
      },
    ],
    subtotal: 63.9,
    discount: 5,
    total: 58.9,
    paymentMethod: PAYMENT_METHODS.CARD,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75003'),
    number: 'VTA-2024-0003',
    date: new Date('2024-02-02T09:30:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74003'),
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73104'),
        quantity: 2,
        unitPrice: 35.8,
        discount: 0,
        subtotal: 71.6,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73105'),
        quantity: 1,
        unitPrice: 22.5,
        discount: 0,
        subtotal: 22.5,
      },
    ],
    subtotal: 94.1,
    discount: 0,
    total: 94.1,
    paymentMethod: PAYMENT_METHODS.CASH,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75004'),
    number: 'VTA-2024-0004',
    date: new Date('2024-02-02T14:45:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74004'),
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73106'),
        quantity: 1,
        unitPrice: 55.0,
        discount: 5,
        subtotal: 50.0,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73107'),
        quantity: 2,
        unitPrice: 28.9,
        discount: 0,
        subtotal: 57.8,
      },
    ],
    subtotal: 107.8,
    discount: 10,
    total: 97.8,
    paymentMethod: PAYMENT_METHODS.TRANSFER,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75005'),
    number: 'VTA-2024-0005',
    date: new Date('2024-02-03T10:20:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74002'),
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73101'),
        quantity: 3,
        unitPrice: 15.5,
        discount: 2,
        subtotal: 44.5,
      },
    ],
    subtotal: 44.5,
    discount: 0,
    total: 44.5,
    paymentMethod: PAYMENT_METHODS.CASH,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75006'),
    number: 'VTA-2024-0006',
    date: new Date('2024-02-03T15:45:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74001'), // Juan Pérez
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73108'), // Salbutamol Inhalador
        quantity: 1,
        unitPrice: 65.0,
        discount: 0,
        subtotal: 65.0,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73105'), // Loratadina
        quantity: 2,
        unitPrice: 22.5,
        discount: 2,
        subtotal: 43.0,
      },
    ],
    subtotal: 108.0,
    discount: 8,
    total: 100.0,
    paymentMethod: PAYMENT_METHODS.CARD,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75007'),
    number: 'VTA-2024-0007',
    date: new Date('2024-02-04T09:20:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74015'), // Hospital del Este
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73103'), // Amoxicilina
        quantity: 5,
        unitPrice: 45.0,
        discount: 10,
        subtotal: 215.0,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73106'), // Diazepam
        quantity: 3,
        unitPrice: 55.0,
        discount: 5,
        subtotal: 160.0,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73104'), // Omeprazol
        quantity: 4,
        unitPrice: 35.8,
        discount: 0,
        subtotal: 143.2,
      },
    ],
    subtotal: 518.2,
    discount: 50,
    total: 468.2,
    paymentMethod: PAYMENT_METHODS.TRANSFER,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75008'),
    number: 'VTA-2024-0008',
    date: new Date('2024-02-04T11:30:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74014'), // Luis Torres
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73101'), // Paracetamol
        quantity: 2,
        unitPrice: 15.5,
        discount: 0,
        subtotal: 31.0,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73102'), // Ibuprofeno
        quantity: 1,
        unitPrice: 18.9,
        discount: 0,
        subtotal: 18.9,
      },
    ],
    subtotal: 49.9,
    discount: 0,
    total: 49.9,
    paymentMethod: PAYMENT_METHODS.CASH,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75009'),
    number: 'VTA-2024-0009',
    date: new Date('2024-02-04T14:15:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74002'), // Farmacia Santa Cruz
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73107'), // Metformina
        quantity: 5,
        unitPrice: 28.9,
        discount: 5,
        subtotal: 139.5,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73105'), // Loratadina
        quantity: 3,
        unitPrice: 22.5,
        discount: 2,
        subtotal: 65.5,
      },
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73104'), // Omeprazol
        quantity: 3,
        unitPrice: 35.8,
        discount: 3,
        subtotal: 104.4,
      },
    ],
    subtotal: 309.4,
    discount: 30,
    total: 279.4,
    paymentMethod: PAYMENT_METHODS.TRANSFER,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc75010'),
    number: 'VTA-2024-0010',
    date: new Date('2024-02-04T16:45:00Z'),
    customer: new Types.ObjectId('65c3944f00c9d8232dc74003'), // María González
    items: [
      {
        product: new Types.ObjectId('65c3944f00c9d8232dc73106'), // Diazepam
        quantity: 1,
        unitPrice: 55.0,
        discount: 0,
        subtotal: 55.0,
      },
    ],
    subtotal: 55.0,
    discount: 0,
    total: 55.0,
    paymentMethod: PAYMENT_METHODS.CARD,
    status: SALE_STATUS.COMPLETED,
    cashRegister: new Types.ObjectId('65c3944f00c9d8232dc73001'),
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
];

import { Types } from 'mongoose';
import { DOCUMENT_TYPES } from '../constants/customer.constants.js';

export const initialCustomers = [
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74000'), // ID fijo para el cliente genérico
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '0',
    businessName: 'Cliente Final',
    email: null,
    phone: null,
    address: null,
    isGeneric: true,
    isProtected: true,
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74001'),
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '1234567',
    businessName: '<PERSON>',
    email: '<EMAIL>',
    phone: '76543210',
    address: 'Calle 123, Ciudad',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'), // ID de un usuario admin
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74002'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '1234567890',
    businessName: 'Farmacia Santa Cruz',
    email: '<EMAIL>',
    phone: '77889900',
    address: 'Av. Principal 456',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74003'),
    documentType: DOCUMENT_TYPES.FOREIGN_ID,
    documentNumber: 'E123456',
    businessName: 'María González',
    phone: '71234567',
    address: 'Calle Los Pinos 789',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74004'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Clínica San Juan',
    email: '<EMAIL>',
    phone: '70011223',
    address: 'Av. Las Américas 1234',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74005'),
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '7654321',
    businessName: 'Carlos Rodríguez',
    email: '<EMAIL>',
    phone: '79876543',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74006'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Hospital Central',
    email: '<EMAIL>',
    phone: '72345678',
    address: 'Av. Principal 789',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74007'),
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '3456789',
    businessName: 'Ana Martínez',
    phone: '73456789',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74008'),
    documentType: DOCUMENT_TYPES.PASSPORT,
    documentNumber: 'P789012',
    businessName: 'John Smith',
    email: '<EMAIL>',
    phone: '74567890',
    address: 'Calle Internacional 456',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74009'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Farmacia del Sur',
    email: '<EMAIL>',
    phone: '75678901',
    address: 'Av. Sur 123',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74010'),
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '4567890',
    businessName: 'Pedro Sánchez',
    email: '<EMAIL>',
    phone: '76789012',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74011'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Clínica del Norte',
    email: '<EMAIL>',
    phone: '77890123',
    address: 'Av. Norte 456',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74012'),
    documentType: DOCUMENT_TYPES.FOREIGN_ID,
    documentNumber: 'E234567',
    businessName: 'Laura García',
    phone: '78901234',
    address: 'Calle Principal 789',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74013'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Farmacia Oriental',
    email: '<EMAIL>',
    phone: '79012345',
    address: 'Av. Oriental 123',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74014'),
    documentType: DOCUMENT_TYPES.IDENTITY_CARD,
    documentNumber: '5678901',
    businessName: 'Luis Torres',
    email: '<EMAIL>',
    phone: '70123456',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
  {
    _id: new Types.ObjectId('65c3944f00c9d8232dc74015'),
    documentType: DOCUMENT_TYPES.TAX_ID,
    documentNumber: '*********',
    businessName: 'Hospital del Este',
    email: '<EMAIL>',
    phone: '71234567',
    address: 'Av. Este 456',
    createdBy: new Types.ObjectId('65c3944f00c9d8232dc71001'),
  },
] as const;

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

export const validateSchema = (schema: z.ZodSchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = await schema.parseAsync({
        body: req.body,
        params: req.params,
        query: req.query,
      });

      // Asignar los datos validados
      req.body = result.body;
      req.query = result.query;
      req.params = result.params;

      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.log('Error de validación detallado:', error.errors);
        return res.status(400).json({
          error: 'Error de validación',
          details: error.errors.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        });
      }
      next(error);
    }
  };
};

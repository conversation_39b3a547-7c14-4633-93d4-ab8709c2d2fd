import { Request, Response, NextFunction } from 'express';
import { CashRegister } from '../models/cashRegister.model.js';
import { CASH_REGISTER_STATUS } from '../constants/cash.constants.js';
import { ValidationError } from '../utils/errors.js';

export const checkOpenCashRegister = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    const openRegister = await CashRegister.findOne({
      status: CASH_REGISTER_STATUS.OPEN,
    });

    if (!openRegister) {
      throw new ValidationError(
        'No hay una caja abierta. Debe abrir una caja para realizar esta operación.'
      );
    }

    // Agregar el ID del registro de caja a la solicitud para uso posterior
    req.body.cashRegisterId = openRegister._id;

    next();
  } catch (error) {
    next(error);
  }
};

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/config.js';
import { logger } from '../utils/logger.js';
import { AuthRequest, JwtPayload } from '../interfaces/auth.interface.js';
import { AUTH_ERRORS } from '../constants/auth.constants.js';
import { User } from '../models/user.model.js';
import { USER_ROLES } from '../constants/user.constants.js';

const verifyToken = (token: string) => {
  try {
    return jwt.verify(token, config.jwtSecret!);
  } catch (error) {
    throw new Error(AUTH_ERRORS.INVALID_TOKEN);
  }
};

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ error: AUTH_ERRORS.NO_TOKEN });
    }

    const decoded = verifyToken(token) as JwtPayload;

    const user = await User.findById(decoded._id);

    if (!user) {
      return res.status(401).json({
        error: AUTH_ERRORS.USER_NOT_FOUND,
        message: AUTH_ERRORS.SESSION_EXPIRED,
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: AUTH_ERRORS.USER_INACTIVE,
        message: AUTH_ERRORS.ACCOUNT_DISABLED,
      });
    }

    (req as AuthRequest).user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      error: AUTH_ERRORS.UNAUTHORIZED,
      message: AUTH_ERRORS.SESSION_EXPIRED,
    });
  }
};

export const checkPermission = (requiredPermission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthRequest;

    try {
      // Verify if user exists
      if (!authReq.user) {
        return res.status(401).json({
          error: AUTH_ERRORS.UNAUTHORIZED,
          message: AUTH_ERRORS.SESSION_EXPIRED,
        });
      }

      // Get updated user from the database
      const user = await User.findById(authReq.user._id);
      if (!user) {
        return res.status(401).json({
          error: AUTH_ERRORS.USER_NOT_FOUND,
          message: AUTH_ERRORS.SESSION_EXPIRED,
        });
      }

      authReq.user = user;
      // If user is superAdmin or admin, allow access to all routes
      if (user.isSuperAdmin || user.role === USER_ROLES.ADMIN) {
        return next();
      }

      // If user has all permissions, allow access to all routes
      const userPermissions = user.permissions || [];
      if (userPermissions.includes('*')) {
        return next();
      }

      // Verify if user has required permission
      if (!userPermissions.includes(requiredPermission)) {
        logger.warn(
          `Access denied for user ${
            user.username
          }. Required: ${requiredPermission}, Has: ${userPermissions.join(
            ', '
          )}`
        );
        return res.status(403).json({
          error: AUTH_ERRORS.FORBIDDEN,
          requiredPermission,
        });
      }

      next();
    } catch (error) {
      return res.status(403).json({
        error: AUTH_ERRORS.FORBIDDEN,
      });
    }
  };
};

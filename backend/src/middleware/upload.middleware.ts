import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { config } from '../config/config.js';

const UPLOAD_PATHS = {
  LOGOS: 'uploads/logos',
  PRODUCTS: 'uploads/products',
  BASE: 'uploads',
} as const;

const createUploadDirectories = () => {
  const directories = [UPLOAD_PATHS.LOGOS, UPLOAD_PATHS.PRODUCTS];
  directories.forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

createUploadDirectories();

const logoStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOAD_PATHS.LOGOS);
  },
  filename: (req, file, cb) => {
    const extension = path.extname(file.originalname);
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    cb(null, `logo-${uniqueSuffix}${extension}`);
  },
});

const productStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOAD_PATHS.PRODUCTS);
  },
  filename: (req, file, cb) => {
    const extension = path.extname(file.originalname);
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    cb(null, `product-${uniqueSuffix}${extension}`);
  },
});

const imageFileFilter = (
  req: Express.Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Tipo de archivo no permitido. Use JPEG, PNG o WebP'));
  }
};

export const uploadLogo = multer({
  storage: logoStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: config.files.maxLogoSize,
  },
});

export const uploadProductImage = multer({
  storage: productStorage,
  fileFilter: imageFileFilter,
  limits: {
    fileSize: config.files.maxImageSize,
  },
});

export const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, UPLOAD_PATHS.BASE);
    },
    filename: (req, file, cb) => {
      const extension = path.extname(file.originalname);
      const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
      cb(null, `file-${uniqueSuffix}${extension}`);
    },
  }),
  limits: {
    fileSize: config.files.maxImageSize,
  },
});

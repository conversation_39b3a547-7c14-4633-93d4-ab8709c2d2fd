import dotenv from 'dotenv';
import path from 'path';
import { z } from 'zod';
import { AUTH_CONSTANTS } from '../constants/auth.constants.js';

const __dirname = path.dirname(new URL(import.meta.url).pathname);

dotenv.config({ path: path.join(__dirname, '../../.env') });

const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3000'),
  MONGODB_URI: z.string().url(),
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default(AUTH_CONSTANTS.JWT_EXPIRES_IN),
  CORS_ORIGIN: z.string(),
});

try {
  envSchema.parse(process.env);
} catch (error) {
  throw new Error(
    `Environment validation failed: ${
      error instanceof Error ? error.message : 'Unknown error'
    }`
  );
}

export const config = {
  env: process.env.NODE_ENV as string,
  port: parseInt(process.env.PORT || '3000', 10),
  mongoUri: process.env.MONGODB_URI,
  jwtSecret: process.env.JWT_SECRET!,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN,
  corsOrigin: process.env.CORS_ORIGIN,
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
  files: {
    maxLogoSize: (Number(process.env.MAX_LOGO_SIZE_MB) || 2) * 1024 * 1024, // MB
    maxImageSize: (Number(process.env.MAX_IMAGE_SIZE_MB) || 5) * 1024 * 1024, // MB
  },
} as const;

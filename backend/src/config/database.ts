import mongoose from 'mongoose';
import { config } from './config.js';
import { logger } from '../utils/logger.js';

export const connectDB = async (): Promise<void> => {
  try {
    if (!config.mongoUri) {
      throw new Error('MongoDB URI is not defined in the configuration');
    }

    if (mongoose.connection.readyState === 1) {
      return;
    }

    await mongoose.connect(config.mongoUri, {
      serverSelectionTimeoutMS: 5000,
    });

    if (process.env.NODE_ENV !== 'test') {
      const dbName = config.mongoUri.split('/').pop();
      const host = config.mongoUri.split('@')[1]?.split('/')[0] || 'localhost';

      logger.section('Database Connection');
      logger.success(`Connected to MongoDB (${dbName} @ ${host})`);
    }
  } catch (error) {
    logger.error(
      `MongoDB connection error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
    throw error;
  }
};

export const disconnectDB = async (): Promise<void> => {
  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      if (process.env.NODE_ENV !== 'test') {
        logger.info('Disconnected from MongoDB');
      }
    }
  } catch (error) {
    logger.error(
      `MongoDB disconnection error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
    throw error;
  }
};

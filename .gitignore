# Dependencies
node_modules
frontend/node_modules
backend/node_modules

# Build outputs
dist
frontend/dist
backend/dist

# Environment variables
.env
backend/.env
frontend/.env
.env.local
.env.*.local

# Keep examples
!.env.example
!backend/.env.example
!frontend/.env.example

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
